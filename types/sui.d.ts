/**
 * @file sui.d.ts
 * <AUTHOR>
 */
import '@baidu/sui';
import { SanComponent, defineComponent } from "san/types";

declare module '@baidu/sui' {
    export class Alert extends SanComponent {};
    export class Checkbox extends SanComponent {
        static CheckboxGroup: SanComponent
    }

    export class Radio extends SanComponent {
        static RadioGroup: SanComponent;
    }

    export class Drawer extends SanComponent {};

    export class Dialog extends SanComponent {
        static warning<T extends {}>(target: {
            title?: string
            okText?: string
            content: string | SanComponent | defineComponent
            showCancel?: true
            onOk?: () => Promise<void> | void
        }): Promise<T>;
    }

    export class Table extends SanComponent {
        toggleExpandRow(e: Event, rowIndex: number) {}
    }
}
