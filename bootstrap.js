/**
 * 覆盖全局的ESL配置，加载定制化配置
 *
 * @file bootstrap.js
 * <AUTHOR>
 */
import '@/assets/styles/index.less';

import '@/common/client';
import '@/components/sidebar';

import './src/pages';
import './src/common/form-format';
import api from '@/common/client';
import checkActive from '@/common/check-active';

window._regions_ = [];

const getRegions = async () => {
    const result = await api.getEdgeNodeList({});
    window._regions_ = [...result.regions];
};

const getLinkRegion = () => {
    const reg = new RegExp('(^|\\?|&)region=([^&|^#]*)(\\s|&|$|#)', 'i');
    const result = window.location.href.match(reg);
    return result ? result[2] : '';
};

export async function start() {
    // 检测是否激活
    await checkActive();
    try {
        const linkRegion = getLinkRegion('region');
        if (/#\/kafka\//.test(window.location.hash) && linkRegion) {
            window.$context.setRegion(linkRegion);
        }
    }
    catch (error) {
        console.error(error);
    }
    const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
    if (isEdgeRegion) {
        getRegions();
    };
    // 路由切换
    window.addEventListener('hashchange', checkActive);
    return Promise.resolve();
}

export function stop() {
    window.removeEventListener('hashchange', checkActive);
    return Promise.resolve();
}
