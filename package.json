{"name": "console-kafka", "version": "1.0.0", "description": "百度智能云 | 消息服务", "main": "bootstrap.js", "types": "types/index.d.ts", "scripts": {"dev": "npx bce-cli dev --template=public-console --config=bce-config.js", "build": "NODE_OPTIONS=--openssl-legacy-provider npx bce-cli build --template=public-console --config=bce-config.js", "mock": "npx bce-cli dev --mock --template=public-console --config=bce-config.js", "dev:xs-console": "npx bce-cli dev --template=xs-console", "build:xs-console": "npx bce-cli build --template=xs-console", "dev:secret": "npx bce-cli dev --template=private-common --config=bce-config.js", "build:secret": "NODE_OPTIONS=--openssl-legacy-provider npx bce-cli build --template=private-common --config=bce-config.js"}, "author": "zhoushengqiang01", "license": "ISC", "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@babel/eslint-plugin": "^7.19.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@baidu/bce-cli": "1.1.6-beta.6", "@baiducloud/runtime": "^1.0.0-rc.41", "@ecomfe/eslint-config": "^7.4.0", "@ecomfe/stylelint-config": "^1.1.2", "@types/lodash": "^3.10.1", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "eslint": "^8.34.0", "prettier": "^2.8.4", "prettier-eslint": "^16.3.0", "san": "3.7.9"}, "dependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@baidu/bce-bcc-sdk-enum": "1.0.0-rc.20", "@baidu/bce-template": "^1.0.13", "@baidu/san-monaco-editor": "^2.0.0-beta.8", "@baidu/sui-icon": "^1.0.41", "@baidu/xicon-san": "^0.0.43", "bignumber.js": "^9.0.2", "copy-webpack-plugin": "^6.4.1", "echarts": "^5.4.2", "file-size": "^1.0.0", "jsencrypt": "3.3.1", "lodash": "^3.10.1", "resize-observer-polyfill": "^1.5.1", "san-store": "^2.1.3", "san-update": "^2.1.0"}}