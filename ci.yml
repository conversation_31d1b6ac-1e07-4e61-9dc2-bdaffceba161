Global:
  version: 2.0
  group_email: <EMAIL>

Default:
  profile: [buildProduction]

Profiles:
  - profile:
    name: buildProduction
    mode: AGENT
    environment:
      cluster: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.latest
    build:
      command: sh scripts/build.sh
    artifacts:
      release: true

  - profile:
    name: buildXuShangConsole
    mode: AGENT
    environment:
      cluster: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.latest
    build:
      command: sh scripts/build.sh xs-console
    artifacts:
      release: true

  - profile:
    name: buildPrivateSecret
    mode: AGENT
    environment:
      cluster: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.latest
    check:
      - reuse: TASK
        enable: false
    build:
      command: sh scripts/build.sh secret
    artifacts:
      release: true
