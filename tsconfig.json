{
  "compilerOptions": {
    "outDir": "dist",
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "target": "ES5",
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "paths": {
      "@/*": ["./src/*"],
      "@/common/*": ["./src/common/*"],
      "@/components/*": ["./src/components/*"],
    }
  },
  "exclude": ["output", "dist", "node_modules"]
}
