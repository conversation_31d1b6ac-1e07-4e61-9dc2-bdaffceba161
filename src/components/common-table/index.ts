/**
 * 针对sui table 做的 列表刷新类
 *
 * @file common-table.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {AppListPage} from '@baidu/sui-biz';

import {PAGER_SUI} from '@/common/config';

export default abstract class CommonTable extends AppListPage {
    // 继承 ListPage 需要
    pageTitle = '';

    // 搜索框的值的key
    searchboxKey = 'searchbox.keyword';

    // 继承 ListPage 需要
    attached() {}

    // 需要重写的方法
    public abstract getTableList(): Promise<void>;

    /**
     * 搜索
     */
    onSearch(args?: {value: string}): void {
        this.data.set('pager.page', 1);
        this.getComList();
    }

    /**
     * 分页
     * @param {Number} page page
     * @param {Number} pageSize pageSize
     */
    onPageChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', args.value.page);
        this.getComList();
    }

    /**
     * 分页 pageSize 设置
     */
    onPageSizeChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', 1);
        this.getComList();
    }

    // 获取列表选中的项目
    getSelection() {
        return _.map(this.data.get('selection.selectedIndex'),
            i => this.data.get(`table.datasource[${i}]`));
    }

    // 过滤项设置
    onFilter(args: {field: {name: string}, filter: {value: string | number}}) {
        this.data.set('pager.page', 1);
        this.data.set(args.field.name, args.filter.value);
        this.getComList();
    }

    /**
     * 获取表格 list
     */
    async getComList() {
        this.data.set('table.loading', true);
        this.data.set('table.error', false);
        // 清除多选
        this.data.set('selection.selectedIndex', []);
        try {
            await this.getTableList();
            this.data.set('table.loading', false);
        }
        catch (error) {
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    // 排序
    onSort(args: {value: {order: number, orderBy: number}}) {
        this.data.set('orderBy', args.value.orderBy);
        this.data.set('order', args.value.order);
        this.data.set('pager.page', 1);
        this.getComList();
    }

    resetTable() {
        this.data.merge('pager', PAGER_SUI);
        this.data.set(this.searchboxKey, '');
        this.getComList();
    }
}
