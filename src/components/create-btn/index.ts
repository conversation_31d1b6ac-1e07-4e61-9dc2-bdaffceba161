/**
 * 创建按钮
 *
 * @file create-btn.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import './index.less';

export default class extends Component {
    static template = html`
    <span class="create-btn">
        <s-button
            on-click="onClick"
            skin="{{skin}}"
            disabled="{{disabled}}">
            <outlined-plus class="create-icon" />
            {{text}}
        </s-button>
    </span>`;

    static components = {
        's-button': Button,
        'outlined-plus': OutlinedPlus
    };

    initData() {
        return {
            skin: 'primary'
        };
    }

    onClick() {
        this.fire('click', {});
    }
}
