/**
* drawer插件一些公用抽取模块
* index.less
*/
@borderColor: #E8E9EB;

@klass: drawer-common;

.@{klass} {
    width: 600px;
    .s-drawer-wrapper {
        overflow: hidden;
        right: 0 !important;
        
        .s-drawer-header {
            padding: 12px 24px !important;
            height: 56px;
            &-title {
                width: 100%;
    
                .@{klass}__head {
                    display: flex;

                    &_left {
                        flex: 1;
                        display: inline-flex;
                        align-items: center;

                        &.read {
                            flex-basis: calc(~"100% - 78px");
                            width: calc(~"100% - 78px");
                        }

                        &.edit {
                            flex-basis: calc(~"100% - 132px");
                            width: calc(~"100% - 132px");
                        }

                        &_title {
                            display: inline-block;
                            font-size: 16px;
                            line-height: 30px;
                            width: 100%;

                            .block {
                                display: inline-block;
                                vertical-align: middle;
                                width: 4px;
                                height: 20px;
                                margin-right: 10px;
                                background: var(--defaultColor);
                            }

                            .ellipsis-tip {
                                display: inline-block;
                                vertical-align: middle;
                                width: calc(~"100% - 23px");
                            }
                        }

                        &_text {
                            display: inline-block;
                            vertical-align: middle;
                            font-size: 16px;
                        }

                        &.extra {

                            .@{klass}__head_left_title {
                                width: auto;
                                display: flex;
                                align-items: center;

                                .ellipsis-tip {
                                    width: auto;
                                }
                            }

                            .@{klass}__head_left_text {
                                flex: 1;
                                max-width: 100%;
                                overflow: hidden;
                            }
                        }
                    }

                    &_right {
                        flex: 0;
                        display: flex;
                        align-items: center;

                        .s-button {
                            margin-top: 1px;
                        }
                    }
                }
            }
        }

        .s-drawer-content {
            width: 100%;
            height: calc(~"100% - 56px");
            padding: 0px;
            .@{klass}__content {
                padding: 24px;
                height: calc(~"100% - 64px");
                overflow-x: scroll;
            }

            .s-table-content {

                .s-table-container {
                    overflow-x: hidden;
                }
            }

            .@{klass}__bottom {
                padding: 16px;
                display: flex;
                justify-content: flex-end;
                border-top: 1px solid @borderColor;
            }
        }
    }
}
