/**
 * drawer插件一些公用抽取模块
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import { Component } from 'san';
import { html } from '@baiducloud/runtime';
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Notification } from '@baidu/sui';
import { OutlinedClose } from '@baidu/sui-icon';

import EllipsisTip from '@/components/ellipsis-tip';

import './index.less';

const klass = 'drawer-common';

export default class extends Component {
    private _close?: Function;

    static template = html`
    <template>
        <s-drawer
            open="{= open =}"
            class="${klass} {{class}}"
            direction="right"
            showClose="{{false}}"
            size="{{size}}"
            mask="{{false}}"
            getContainer="{{getContainer}}"
            on-close="onClose">
            <div slot="title" class="${klass}__head">
                <span class="${klass}__head_left {{headClass}} {{extraTitle ? 'extra' : ''}}">
                    <span class="${klass}__head_left_title">
                        <ellipsis-tip text="{{title}}" showTip="{{showTip}}" />
                    </span>
                    <span
                        s-if="{{extraTitle}}"
                        class="${klass}__head_left_text"
                        title="{{extraTitle}}">
                        <ellipsis-tip text="{{extraTitle}}" />
                    </span>
                </span>
                <span class="${klass}__head_right {{headClass}}">
                    <s-outlined-close class="ml10" on-click="onClose" />
                </span>
            </div>
            <div class="${klass}__content">
                <slot />
            </div>
            <div class="${klass}__bottom">
                <span class="${klass}__bottom_right {{headClass}}">
                    <template s-if="showEdit">
                        <s-button
                            s-if="{{mode === 'read'}}"
                            skin="primary"
                            on-click="onEdit">
                            编辑
                        </s-button>
                        <template s-else>
                            <s-button on-click="onCancel">取消</s-button>
                            <s-button
                                skin="primary"
                                class="ml12"
                                on-click="onOk">
                                保存
                            </s-button>
                        </template>
                    </template>
                </span>
            </div>
        </s-drawer>
    </template>`;

    static components = {
        's-button': Button,
        's-drawer': Drawer,
        's-outlined-close': OutlinedClose,
        'ellipsis-tip': EllipsisTip
    };

    initData() {
        return {
            open: true,
            mode: 'read',
            size: 600,
            showEdit: true,
            // 这里需要注意 保证这个节点存在
            getContainer: document.getElementById('main'),
        };
    }

    static computed: SanComputedProps = {
        headClass(): string {
            const showEdit = this.data.get('showEdit');
            const mode = this.data.get('mode');
            if (!showEdit) {
                return '';
            }
            return mode === 'read' ? 'read' : 'edit';
        }
    };

    attached() {
        this.initWidth();
        setTimeout(() => this.animation(true));
    }

    // 编辑
    onEdit() {
        this.data.set('mode', 'edit');
        this.fire('edit', {});
    }

    // 取消
    onCancel() {
        const checkChange = this.data.get('checkChange')
        if (checkChange()) {
            Dialog.warning({
                okText: '确定',
                content: '取消编辑将不保留已经修改的数据，确定继续吗？',
                showCancel: true,
                onOk: () => {
                    this.data.set('mode', 'read');
                    this.fire('cancel', {});
                }
            });
        }
        else {
            this.data.set('mode', 'read');
            this.fire('cancel', {});
        }
    }

    // 保存
    onOk() {
        const { checkChange, save } = this.data.get();
        if (!checkChange()) {
            Notification.warning('请先修改权限，修改完才能保存');
            return;
        }
        save().then(() => this.data.set('mode', 'read'));
    }

    // 关闭
    onClose() {
        if (!this._close) {
            this._close = _.debounce(() => {
                this.animation(false);
                setTimeout(() => {
                    this.data.set('open', false);
                    this.dispose && this.dispose();
                    this.fire('close', {});
                }, 500);
            }, 300);
        }
        this._close();
    }

    // 初始化width
    initWidth() {
        const node = document.querySelector('.drawer-common .s-drawer-wrapper') as HTMLElement;
        node.style.width = '0';
    }

    // 动画
    animation(isOpen: boolean) {
        const node = document.querySelector('.drawer-common .s-drawer-wrapper') as HTMLElement;
        const size: number = this.data.get('size');
        node && (node.style.width = isOpen ? (size + 'px') : '0');
    }
}