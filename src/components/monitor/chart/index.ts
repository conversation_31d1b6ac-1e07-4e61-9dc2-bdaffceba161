/**
 * 监控的表格
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import m from 'moment';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BcmChartPanel} from '@baiducloud/bcm-sdk-san';
import {BcmSDK} from '@baiducloud/bcm-sdk';
import HttpClient from '@baiducloud/httpclient';
import {SCOPE} from '@/common/config';
import {formatUtcTime} from '@/common/util';

import './index.less';

// 监控趋势图小图默认配置 类型
export type TIME_FILTER_TYPE = '1h' | '6h' | '1d' | '7d' | '14d' | '40d';

// 监控趋势图小图默认配置
export const MONITOR_DEFAULT_PERIOD = {
    '1h': 60,
    '3h': 180,
    '6h': 300,
    '1d': 1200,
    '7d': 8400,
    '30d': 36000
};

export default class KafkaChart extends Component {
    static template = html`
    <div class="kafka-chart">
        <bcm-chart
            s-ref="chart"
            title="{{item.name}}"
            api-type="metricName"
            scope="{{scope}}"
            height="{{200}}"
            dimensions="{{dimensions}}"
            nameType="{{nameType}}"
            metrics="{{item.metrics}}"
            statistics="{{item.statistics}}"
            time="{= time =}"
            period="{{periodTime}}"
            startTime="{{startTime}}"
            endTime="{{endTime}}"
            unit="{{item.unit}}"
            sdk="{{bcmSdk}}"
            seriesOption="{{seriesOption}}"
            connect-nulls="{{true}}"
            legend="{{legend}}"
            queryProccessor="{{queryProccessor}}"
            requester="{{requester}}"
            proccessor="{{proccessor}}"
            options="{{options}}"
        >
        <span slot="extra-title" class="title-select">
            <slot name="filter-title"/>
        </span>
        <div slot="detail-extra-filter-item" class="filter-item">
            <slot name="filter-item" />
        </div>
        </bcm-chart>
    </div>
    `;
    initData() {
        return {
            bcmSdk: new BcmSDK({
                client: new HttpClient({}, {
                    getCsrfToken() {
                        return this.$cookie.get('bce-user-info');
                    },
                    getCurrentRegion() {
                        return this.$context.getCurrentRegion();
                    }
                })
            }, this.$context),
            seriesOption: {
                type: 'line',
                chart: {
                    symbol: 'none'
                }
            },
            monitorDefaultPeriod: MONITOR_DEFAULT_PERIOD,
            queryProccessor: this.queryProccessor.bind(this),
            scope: SCOPE,
            options: {
                grid: {
                    containLabel: true,
                    x: 18,
                    x2: 20,
                    y: 50,
                    y2: 50
                },
                dataZoom: {
                    type: 'slider',
                    handleIcon: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAICAIAAABPmPnhAAAABmJLR0QA/wD/AP+gvaeTAAAAIUlEQVQImWNUyfjEgBsw4ZFDkb49nReNQYpucqQZKXI5AM9uBgn5QGRyAAAAAElFTkSuQmCC',
                    handleSize: '120%',
                    height: '8px',
                    backgroundColor: '#f4f4f2',
                    fillerColor: '#E6F0FF',
                    borderColor: 'transparent',
                    moveHandleStyle: {
                        opacity: 0,
                    },
                    dataBackground: {
                        lineStyle: {
                            opacity: 0,
                        },
                        areaStyle: {
                            opacity: 0,
                        },
                    },
                    selectedDataBackground: {
                        lineStyle: {
                            opacity: 0,
                        },
                        areaStyle: {
                            opacity: 0,
                        },
                    },
                },
            },
        };
    }

    static components = {
        'bcm-chart': BcmChartPanel
    };

    static computed: SanComputedProps = {
        legend(): NormalObject {
            const hide = this.data.get('hideLegend');
            return {
                show: !hide,
                left: 'right',
                itemHeight: 2,
                itemWidth: 12,
                scrollDataIndex: 0,
                orient: 'horizontal',
                align: 'right'
            };
        }
    };

    inited() {
        this.watch('time', time => time && this.refresh());
        this.watch('item', item => item && this.refresh());
    }

    refresh() {
        try {
            this.nextTick(() => {
                const detailDialog = this.ref('chart').dialog;
                if (detailDialog && detailDialog.el) {
                    detailDialog.data.raw.detailConf = _.clone(this.ref('chart').data.raw.conf);
                    this.nextTick(() => detailDialog.handleRefresh());
                }
                this.ref('chart') && (this.ref('chart') as unknown as BcmChartPanel).loadMetrics();
            });
        } catch (error) {}
    }

    // 处理时间
    queryProccessor(data: {startTime: string, endTime: string, [x: string]: string | number}) {
        return {
            ...data,
            endTime: formatUtcTime(m(data.endTime)),
            startTime: formatUtcTime(m(data.startTime))
        };
    }
}
