/**
 * 监控报错信息
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {Select, Button} from '@baidu/sui';
import {SCOPE} from '@/common/config';

import './index.less';

const klass = 'monitor-info';

export class MonitorInfo extends Component {

    static template = html`
    <div class="${klass}">
        <span>报错信息：</span>
        <span class="status normal ml10 mr15">{{okStateCount || 0}}项状态正常</span>
        <span class="status error ml10 mr15">{{alarmStateCount || 0}}项状态异常</span>
        <span class="status warning ml10 mr15">{{insufficientStateCount || 0}}项状态不足</span>
        <s-button
            class="ml15"
            s-if="{{hasAlarm}}"
            on-click="onAlarmClick">
            报警详情
        </s-button>
    </div>`;

    static components = {
        's-select': Select,
        's-button': Button
    };

    initData() {
        return {
            hasAlarm: true
        };
    }

    onAlarmClick() {
        const alarmSubfix = this.data.get('alarmSubfix');
        const region = this.$context.getCurrentRegionId();
        redirect(`/bcm/pro/#/alarm/rule?scope=${SCOPE}&${alarmSubfix}&region=${region}`);
    }
}
