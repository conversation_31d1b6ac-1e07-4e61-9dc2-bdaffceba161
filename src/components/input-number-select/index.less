.m-input-number-select {
    display: inline-flex;
    border: 1px solid var(--borderColor2);
    border-radius: 4px;
    overflow: hidden;

    &:hover {
        border-color: var(--defaultColor);
        cursor: pointer;
    }

    .s-inputnumber {
        margin-right: 0;
        border: none;

        .s-inputnumber-handler-wrap {
            display: none;
        }

        .s-inputnumber-input {
            width: 100%;
        }
    }

    .s-select {
        background-color: #f7f7f9;

        &.s-select-active .s-input {
            background-color: #f7f7f9;
        }

        .s-input {
            border: none;
        }
    }
}