/**
 * 数字输入&单位选择框
 * @file index.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {InputNumber, Select} from '@baidu/sui';
import './index.less';

const klass = 'm-input-number-select';

export class InputNumberSelect extends Component {
    static template = html`
        <div class="${klass}">
            <s-input-number
                value="{= value =}"
                width="{{width}}"
                on-change="onInputChange"
                disabled="{{disabled}}"
                max="{{max}}"
                min="{{min}}"
            >
            </s-input-number>
            <s-select
                width="70"
                value="{= selectValue =}"
                datasource="{{options}}"
                on-change="onSelectChange"
                disabled="{{selectDisabled}}"
            />
        </div>
    `;
    static components = {
        's-input-number': InputNumber,
        's-select': Select,
    };
    initData() {
        return {
            value: '',
            width: 100,
            selectValue: '',
            disabled: false,
            options: [],
            selectDisabled: false,
        };
    }

    onInputChange(e: {value: number}) {
        this.fire('input-change', e);
    }

    onSelectChange(e: {value: number | string}) {
        this.fire('select-change', e);
    }
}