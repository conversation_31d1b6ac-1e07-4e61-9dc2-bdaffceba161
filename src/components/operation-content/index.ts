/**
 * 任务详情-执行内容
 *
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {MonacoEditor} from '@baidu/san-monaco-editor';
import {getMonacoEditorSrcPath} from '@/common/util';
import './index.less';

const klass = 'operation-area';

export default class OperationContent extends Component {
    static template = html`
    <template>
        <div class="${klass}">
            <div class="title" s-if="title">{{title}}</div>
            <s-monaco
                s-ref="monaco"
                amd="{{false}}"
                language="json"
                readonly="{{true}}"
                theme="vs"
                width="{{width}}"
                height="{{height}}"
                options="{{codeEditorOptions}}"
                value="{= data =}"
                srcPath="{{monacoPath}}"
            >
            </s-monaco>
        </div>
    </template>`;

    static components = {
        's-monaco': MonacoEditor,
    };

    initData() {
        return {
            codeEditorOptions: {
                formatOnPaste: true,
                formatOnType: true,
                lineNumbers: 'on',
                readOnly: true
            },
            monacoPath: getMonacoEditorSrcPath(),
            data: ''
        };
    }
}
