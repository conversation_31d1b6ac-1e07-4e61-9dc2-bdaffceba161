
/**
 * 提示组件
 *
 * @file tip.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {OutlinedQuestionCircle, OutlinedInfoCircle} from '@baidu/sui-icon';
import {Popover} from '@baidu/sui';

import './index.less';

export default class extends Component {
    static template = html`
    <span class="tip-custom {{type === 'question' ? 'tip-question' : 'tip-sign'}}">
        <s-popover placement="{{placement}}">
            <s-question class="s-question" s-if="{{type === 'question'}}" color="#84868C" width="14px"/>
            <s-info class="s-exclamation" s-if="{{type === 'sign'}}" color="#84868C" width="14px"/>
            <div slot="content">
                <slot />
            </div>
        </s-popover>
    </span>`;

    static components = {
        's-popover': Popover,
        's-question': OutlinedQuestionCircle,
        's-info': OutlinedInfoCircle
    };

    initData() {
        return {
            type: 'question',
            placement: 'top'
        };
    }
}
