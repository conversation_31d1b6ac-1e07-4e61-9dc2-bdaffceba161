/**
 * index
 *
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {OutlinedExclamationCircle, OutlinedDownload} from '@baidu/sui-icon';
import {Link} from '@baidu/sui';
import {DOCS_LINK, CONFIG_TYPE} from '@/common/config';

import './index.less';

export default class extends Component {
    static template = html`
    <span class="help-doc">
        <s-outlined-download
            s-if="{{hasCode}}"
            color="#999999"
            width="18"
            on-click="onShowSimple"
        />
        <a on-click="onShowSimple" class="mr5" s-if="{{hasCode}}">代码样例</a>
        <s-link href="{{helpLink}}" target="blank">
            <s-outlined-exclamation-circle class="link-icon"/>
            帮助文档
        </s-link>
    </span>`;

    static components = {
        's-link': Link,
        's-outlined-download': OutlinedDownload,
        's-outlined-exclamation-circle': OutlinedExclamationCircle
    }

    initData() {
        return {
            hasCode: true,
            helpLink: DOCS_LINK.kafkaOrdinaryHelp
        }
    }

    attached() {
        const {type} = this.data.get('');
        if (type === CONFIG_TYPE.VIP) {
            this.data.set('hasCode', false);
            this.data.set('helpLink', DOCS_LINK.kafkaVipHelp);
        } else {
            this.data.set('hasCode', true);
            this.data.set('helpLink', DOCS_LINK.kafkaOrdinaryHelp);
        }
    }

    // 代码样例
    onShowSimple() {
        window.open(`/api/kafka/v2/download/kafka-client/samples?t=${new Date().getTime()}`, '_blank');
    }
}
