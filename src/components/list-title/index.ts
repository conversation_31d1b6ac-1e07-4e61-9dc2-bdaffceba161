/**
 * 列表标题
 *
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {Alert} from '@baidu/sui';
import HelpDoc from '../help-doc';
import {DOCS_LINK} from '@/common/config';

import './index.less';

const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
const klass = 'kafka-list-header';

export default class extends Component {
    static template = html`
    <div class="${klass}">
        <div class="${klass}__content">
            <span class="${klass}__content-title">{{title}}</span>
            <help-doc s-if="!${isXushang} && !hideHelpDoc" class="${klass}__content-right" type="{{type}}" />
        </div>
        <div class="${klass}__footer" s-if="hasOnlineAlert && !${isXushang}">
            <s-alert skin="success">
                <p s-if="type === 'vip'">
                    专享版消息服务for Kafka全新功能更新，请查看<a href="${DOCS_LINK.releaseLog}" target="_blank">功能发布记录</a>了解更多
                </p>
                <p s-else>
                    为了给您提供更好的服务体验，专享版消息服务for kafka将逐步替代共享版，请进入专享版使用体验，并着手开始安排您的迁移计划。如有任何使用问题欢迎联系我们。
                </p>
            </s-alert>
        </div>
    </div>`;

    static components = {
        's-alert': Alert,
        'help-doc': HelpDoc
    };
}
