/**
 * 更多省略
 *
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Popover} from '@baidu/sui';
import ResizeObserver from 'resize-observer-polyfill';
import {ClipBoard} from '@baidu/sui-biz';
import './index.less';

const klass = 'ellipsis-tip';

export default class extends Component {
    observer?: ResizeObserver;

    static template = html`
    <div class="${klass}">
        <s-popover
            s-if="{{showTip}}"
            content="{{text}}"
            class="ellipsis-tip-popover"
            trigger="{{computedTrigger}}"
            placement="{{placement}}">
            <span class="ellipsis-single">
                <span class="${klass}__text" style="{{'width: ' + width ? width + 'px' : 'auto'}}">
                    {{text}}
                </span>
                <s-clip-board s-if="{{copy}}" text="{{text}}" class="ml4"/>
            </span>
        </s-popover>
        <span s-else class="ellipsis-single">{{text}}<s-clip-board s-if="{{copy}}" text="{{text}}" /></span>
    </div>`;

    static components = {
        's-popover': Popover,
        's-clip-board': ClipBoard,
    };

    initData() {
        return {
            placement: 'left',
            showTip: true,
            alwaysTip: false,
            isToShowTip: false,
            copy: false
        };
    }

    static computed: SanComputedProps = {
        computedTrigger() {
            const showTip = this.data.get('showTip');
            const alwaysTip = this.data.get('alwaysTip');
            const isToShowTip = this.data.get('isToShowTip');
            return showTip && (alwaysTip || isToShowTip) ? 'hover' : '';
        }
    };

    attached() {
        if (this.data.get('showTip')) {
            this.observe();
        }
    }

    detached() {
        this.observer && this.observer.disconnect();
    }

    observe() {
        if (!this.el) {
            return;
        }
        this.observer = new ResizeObserver(() => {
            if (this.el) {
                const tipDom = this.el.querySelector('.ellipsis-single');
                const textDom = this.el.querySelector(`.${klass}__text`);
                const bool = tipDom && textDom
                    && textDom.getBoundingClientRect().width < tipDom.getBoundingClientRect().width;
                this.data.set('isToShowTip', !bool);
            }
        });
        this.observer.observe(this.el);
    }
}
