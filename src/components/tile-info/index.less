/**
* 信息平铺
* index.less
*/

.bms-infotile {
    margin-bottom: 20px;

    .s-legend {
        margin-bottom: 0;
    }

    &:last-child {
        margin-bottom: 0;
    }

    .tile-wrap {
        position: relative;
        padding-bottom: 20px;
        font-size: 0;

        &::after {
            content: ' ';
            position: absolute;
            left: 0;
            right: 0;
            bottom: -1px;
            height: 1px;
            width: 100%;
            background-color: var(--borderColor2);
        }

        &-item {
            display: inline-block;
            vertical-align: top;
            width: 33.33333%;
            margin-top: 20px;
            font-size: 12px;

            .box-label {
                display: inline-block;
                min-width: 65px;
            }
        }
    }

    &.is-last {

        .tile-wrap::after {
            display: none;
        }
    }
}
