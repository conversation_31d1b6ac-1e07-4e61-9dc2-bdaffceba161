/**
 * 平铺信息
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Loading, Alert} from '@baidu/sui';

import './index.less';

export default class TileInfo extends Component {
    static template = html`
    <div class="bms-infotile {{isLast ? 'is-last' : ''}}">
        <slot name="h-{{type}}-title">
            <s-append label="{{title}}" class="title" noHighlight>
                <span slot="extra">
                    <slot name="h-{{type}}-extra"></slot>
                </span>
            </s-append>
            <s-alert skin="info" s-if="title === '网络安全'">
                依据访问协议设置安全组出、入站的 TCP 协议端口以及 IP 范围
            </s-alert>
        </slot>
        <div class="tile-wrap">
            <template s-for="item,index in list">
                <div class="tile-wrap-item" s-if="{{item.isShow}}">
                    <slot name="c-{{item.key}}-item" var-label="{{item.label}}" var-text="{{item.text}}">
                        <span class="box-label">
                            <slot name="c-{{item.key}}-label" var-label="{{item.label}}">
                                {{item.label}}
                            </slot>
                        </span>
                        <span class="box-text">
                            <slot name="c-{{item.key}}-text" var-text="{{item.text}}">
                                {{item.text}}
                            </slot>
                        </span>
                    </slot>
                </div>
            </template>
        </div>
        <slot name="{{type}}-footer"></slot>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-loading': Loading,
        's-alert': Alert
    };
}
