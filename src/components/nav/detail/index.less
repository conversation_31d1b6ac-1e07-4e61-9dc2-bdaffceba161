/**
* 顶部导航栏
* index.less
*/
@backTextColor: #84868c;

.bms-detail-nav {
    display: flex;
    align-items: center;
    width: 100%;

    .back-btn {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: @backTextColor;

        .back-text {
            margin-left: 4px;
            font-size: 14px;
        }

        .s-icon {
            fill: @backTextColor;
        }

        &:hover {

            .back-text {
                color: var(--defaultColor);
            }

            .s-icon {
                fill: var(--defaultColor);
            }
        }
    }

    &__title {
        display: block;
        margin-left: 16px;
        font-size: 16px;
        color: var(--titleColor);
        font-weight: 500;
    }

    &__status {
        margin-left: 12px;

        .status {

            &.rolling {
                padding-right: 15px;
            }
            color: var(--titleColor);
            font-size: 12px;
            line-height: 20px;
            font-weight: 400;
        }
    }

    &__right {
        flex: 1;
        text-align: right;
    }
}
