/**
 * 详情导航栏
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {Button} from '@baidu/sui';
import {OutlinedLeft} from '@baidu/sui-icon';

import {renderStatus} from '@/common/html';

import './index.less';

const klass = 'bms-detail-nav';

export default class extends Component {
    static template = html`
    <div class="${klass}">
        <span
            class="back-btn"
            on-click="onBack">
            <outlined-left width="{{16}}" />
            <span class="back-text">返回</span>
        </span>
        <span class="${klass}__title">{{title}}</span>
        <span class="${klass}__status" s-if="{{options}}">{{options | render | raw}}</span>
        <slot name="extra" />
        <span class="${klass}__right" s-if="hasRight">
            <slot name="right" />
        </span>
    </div>`;

    static components = {
        's-button': Button,
        'outlined-left': OutlinedLeft
    };

    initData() {
        return {
            hasRight: false
        };
    }

    static filters = {
        render(options?: EnumItem) {
            return options ? renderStatus(options) : '';
        }
    };

    inited() {
        // @ts-ignore
        this.data.set('hasRight', this.sourceSlots.named.right);
    }

    onBack() {
        redirect(this.data.get('back'));
    }
}
