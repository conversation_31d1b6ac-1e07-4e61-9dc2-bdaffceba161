/**
 * 详情导航栏
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {OutlinedLeft} from '@baidu/sui-icon';

import './index.less';

const klass = 'bms-create-nav';

export default class extends Component {
    static template = html`
    <div class="${klass}">
        <span class="${klass}__left" on-click="onBack">
            <span class="back-icon">
                <outlined-left />
                返回
            </span>
            <span class="${klass}__left_title">{{backUpTitle}}</span>
        </span>
    </div>`;

    static components = {
        'outlined-left': OutlinedLeft
    }

    onBack() {
        redirect(this.data.get('back'));
    }
}
