/**
* 顶部导航栏
* index.less
*/
@iconColor: #83868c;

.bms-create-nav {
    position: relative;
    width: 100%;
    height: 48px;
    line-height: 48px;
    text-align: center;
    background-color: var(--whiteColor);

    &__left {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        padding-left: 24px;
        cursor: pointer;

        .s-icon {
            margin-top: -2px;
            fill: @iconColor;
        }

        &_title {
            margin-left: 16px;
            font-size: 16px;
            color: var(--titleColor);
            font-size: 16px;
            font-weight: 500;
        }

        .back-icon {
            display: inline-block;
            height: 48px;
            font-size: 14px;
            color: #84868C;
            line-height: 22px;
            font-weight: 400;

            &:hover {
                color: var(--defaultColor);

                .s-icon {
                    fill: var(--defaultColor);
                }
            }
        }
    }
}
