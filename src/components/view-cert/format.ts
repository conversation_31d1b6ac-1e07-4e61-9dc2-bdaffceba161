/**
 * 格式转化
 * 将证书对比信息处理，最终得到最后的证书信息
 * 目前这块实现基于ER
 *
 * @file format.ts
 * <AUTHOR>
 */
type TopicCertItem = {read: boolean, write: boolean, name: string};
type ConsumerCertItem = {use: boolean, name: string};

export default {

    TOPIC: {
        // 获取源、目标数据列表的差异内容
        findChange(source: Array<any>, target: Array<any>) {
            // @ts-ignore
            let add = [];
            // @ts-ignore
            let update = [];
            // @ts-ignore
            let del = [];

            let tIdsMap = {};
            for (let i = 0, l = target.length; i < l; i++) {
                // @ts-ignore
                tIdsMap[target[i].id] = target[i];
            }

            let sItem;
            let tItem;
            let itemChange;
            for (let i = 0, l = source.length; i < l; i++) {
                sItem = source[i];
                // @ts-ignore
                tItem = tIdsMap[sItem.id];
                if (tItem) {
                    itemChange = _getCertChange(sItem, tItem);
                }

                if (itemChange === -1) {
                    continue;
                }

                tItem.topicOperation = `${tItem.read ? 'Read' : ''}${tItem.write ? 'Write' : ''}`;
                // add
                if (itemChange === 0) {
                    let isDescChange = sItem.name !== tItem.name;
                    let addData = {
                        certificateId: tItem.id,
                        topicOperation: tItem.topicOperation
                    };
                    // @ts-ignore
                    isDescChange && (addData.description = tItem.name);
                    add.push(addData);
                }
                // update
                else if (itemChange === 1) {
                    let isOptChange = sItem.read !== tItem.read || sItem.write !== tItem.write;
                    let isDescChange = sItem.name !== tItem.name;
                    let updateData = {
                        uuid: tItem.uuid
                    };
                    // @ts-ignore
                    isOptChange && (updateData.topicOperation = tItem.topicOperation);
                    // @ts-ignore
                    isDescChange && (updateData.description = tItem.name);
                    update.push(updateData);
                }
                // delete
                else if (itemChange === 2) {
                    del.push(tItem.uuid);
                }
            }
            return {
                add,
                update,
                del
            };

            /**
             * 获取单个证书的需要提交的修改类型参数
             * 备注：ER处理数据逻辑
             * @param sItem 源证书
             * @param tItem 目标证书
             * @return {Number} -1没有变化 0添加 1修改 2删除
             */
            function _getCertChange(sItem: TopicCertItem, tItem: TopicCertItem) {
                if (sItem.read === tItem.read
                    && sItem.write === tItem.write
                    && sItem.name === tItem.name) {
                    return -1;
                }
                // todo: 把权限对象抽出来
                // 权限的优先级高于名称
                let isSourceHasAuth = sItem.read || sItem.write;
                let isTargetHasAuth = tItem.read || tItem.write;
                if (isSourceHasAuth && !isTargetHasAuth) {
                    return 2;
                }
                if (!isSourceHasAuth && isTargetHasAuth) {
                    return 0;
                }
                return 1;
            }
        }
    },

    CONSUMER: {
        // 获取源、目标数据列表的差异内容
        findChange(source: Array<any>, target: Array<any>) {
            // @ts-ignore
            let add = [];
             // @ts-ignore
            let update = [];
             // @ts-ignore
            let del = [];

            let tIdsMap = {};
            for (let i = 0, l = target.length; i < l; i++) {
                 // @ts-ignore
                tIdsMap[target[i].id] = target[i];
            }

            let sItem;
            let tItem;
            let itemChange;
            for (let i = 0, l = source.length; i < l; i++) {
                sItem = source[i];
                 // @ts-ignore
                tItem = tIdsMap[sItem.id];
                if (tItem) {
                    itemChange = _getCertChange(sItem, tItem);
                }

                if (itemChange === -1) {
                    continue;
                }

                tItem.consumerGroupOperation = `${tItem.use ? 'Use' : ''}`;
                // add
                if (itemChange === 0) {
                    let isDescChange = sItem.name !== tItem.name;
                    let addData = {
                        certificateId: tItem.id,
                        consumerGroupOperation: tItem.consumerGroupOperation
                    };
                    // @ts-ignore
                    isDescChange && (addData.description = tItem.name);
                    add.push(addData);
                }
                // update
                else if (itemChange === 1) {
                    let isOptChange = sItem.use !== tItem.use;
                    let isDescChange = sItem.name !== tItem.name;
                    let updateData = {
                        certificateId: tItem.id
                    };
                     // @ts-ignore
                    isOptChange && (updateData.consumerGroupOperation = tItem.consumerGroupOperation);
                     // @ts-ignore
                    isDescChange && (updateData.description = tItem.name);
                    update.push(updateData);
                }
                // delete
                else if (itemChange === 2) {
                    del.push({
                        certificateId: tItem.id,
                        consumerGroupOperation: ''
                    });
                }
            }

            return {
                add,
                update,
                del
            };

            /**
             * 获取单个证书的需要提交的修改类型参数
             * @param {Object} type 证书类型
             * @param {Object} sItem 源证书
             * @param {Object} tItem 目标证书
             * @return {number} -1没有变化 0添加 1修改 2删除
             */
            function _getCertChange(sItem: ConsumerCertItem, tItem: ConsumerCertItem) {
                if (sItem.use === tItem.use
                    && sItem.name === tItem.name) {
                    return -1;
                }
                // 权限的优先级高于名称
                let isSourceHasAuth = sItem.use;
                let isTargetHasAuth = tItem.use;
                if (isSourceHasAuth && !isTargetHasAuth) {
                    return 2;
                }
        
                if (!isSourceHasAuth && isTargetHasAuth) {
                    return 0;
                }
        
                return 1;
            }
        }
    }
}
