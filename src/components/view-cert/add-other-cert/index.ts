/**
 * 添加他人证书
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Input, Notification, Checkbox} from '@baidu/sui';

import {CILENT_SILENT, DIALOG_INPUT_WIDTH} from '@/common/config';
import {VAILDITE_ITEMS} from '@/common/rules';
import api from '@/common/client';

import './index.less';

const klass = 'add-other-cert';

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="${klass} ue-dialog"
            title="添加他人的证书"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <s-form
                class="form-format"
                s-ref="form"
                rules="{{rules}}"
                data="{= formData =}">
                <s-form-item label="主题：" class="form-item-center">
                    {{title}}
                </s-form-item>
                <s-form-item label="用户ID：" prop="accountId">
                    <s-input width="{{inputWidth}}" value="{= formData.accountId =}" />
                </s-form-item>
                <s-form-item label="证书序列号：" prop="certificateSN">
                    <s-input width="{{inputWidth}}" value="{= formData.certificateSN =}" />
                </s-form-item>
                <s-form-item s-if="{{type === 'topic'}}" label="备注：">
                    <s-input width="{{inputWidth}}" value="{= formData.description =}" />
                </s-form-item>
                <s-form-item
                    label="权限管理："
                    prop="authority"
                    class="${klass}__authority">
                    <s-checkbox-group value="{= formData.authority =}" on-change="onCheckboxChange">
                        <template s-if="{{type === 'topic'}}">
                            <s-checkbox value="Read">读取权限</s-checkbox>
                            <s-checkbox value="Write">写入权限</s-checkbox>
                        </template>
                        <template s-elif="{{type === 'consumer'}}">
                            <s-checkbox value="Use">使用权限</s-checkbox>
                        </template>
                    </s-checkbox-group>
                </s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup
    };

    initData() {
        return {
            rules: {
                accountId: VAILDITE_ITEMS.requiredInput,
                certificateSN: [VAILDITE_ITEMS.requiredInput],
                authority: [{required: true, message: '至少需要设置一种权限'}]
            },
            open: true,
            formData: {},
            max: 1,
            type: 'topic', // type主要有topic、consumer两种
            inputWidth: DIALOG_INPUT_WIDTH,
        };
    }

    inited() {
        if (this.data.get('type') === 'consumer') {
            this.data.set('formData.authority', ['Use'])
        }
    }

    attached() {
        this.getQuote();
    }

    onCheckboxChange() {
        this.nextTick(() => this.ref('form')?.validateFields(['authority']));
    }

    // 获取最大分区数
    async getQuote() {
        const {partition} = await api.bpsQuota({}, CILENT_SILENT);
        this.data.set('max', partition);
    }

    // 确认
    async onConfirm() {
        try {
            await this.ref('form')?.validateFields();
            this.data.set('confirming', true);
            const {name, formData, cluster, type} = this.data.get('');
            const isTopic = type === 'topic';
            const request = isTopic ? api.bpsTopicAuthForOther : api.bpsTopicAuthForOtherInConsumerGroup;
            await request({
                accountId: formData.accountId,
                certificateSN: formData.certificateSN,
                ...(isTopic
                    ? {
                        topic: name,
                        topicCluster: cluster,
                        description: formData.description,
                        topicOperation: formData.authority.join('')
                    }
                    : {
                        consumerGroupName: name,
                        consumerGroupOperation: formData.authority.join('')
                    })
            });
            Notification.success('保存成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
