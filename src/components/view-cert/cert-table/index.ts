/**
 * 证书表格
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Table, Checkbox, Popover} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz'

import EllipsisTip from '@/components/ellipsis-tip';
import {formatEmpty} from '@/common/util';
import InstantEditor from '@/components/instant-editor';

import './index.less';

const klass = 'view-cert__table';

export default class TableCmpt extends Component {
    static template = html` <div class="${klass}">
        <div class="${klass}__head">
            <span class="${klass}__head_left">
                <slot name="title">{{title}}</slot>
            </span>
        </div>
        <s-table columns="{{columns}}" datasource="{{datasource}}" loading="{{loading}}">
            <ellipsis-tip slot="c-id" text="{{row.id}}" placement="top" />
            <div slot="c-name" class="${klass}__name">
                {{row.name | formatEmpty}}
                <instant-editor
                    s-if="{{hasEditName}}"
                    value="{{row.name}}"
                    info="{{rowIndex}}"
                    request="{{editName}}"
                    placeholder="{{placeholderName}}"
                    canEmpty="{{true}}"
                />
            </div>
            <div slot="c-read">
                <template s-if="{{disabled && desc}}">
                    <s-popover content="{{desc}}">
                        <s-checkbox
                            checked="{{row.read}}"
                            disabled="{{disabled}}"
                            on-change="onChangeCheckBox('read', rowIndex, $event)"
                        />
                    </s-popover>
                </template>
                <template s-else>
                    <s-checkbox
                        checked="{{row.read}}"
                        disabled="{{disabled}}"
                        on-change="onChangeCheckBox('read', rowIndex, $event)"
                    />
                </template>
            </div>
            <div slot="c-write">
                <template s-if="{{disabled && desc}}">
                    <s-popover content="{{desc}}">
                        <s-checkbox
                            checked="{{row.write}}"
                            disabled="{{disabled}}"
                            on-change="onChangeCheckBox('write', rowIndex, $event)"
                        />
                    </s-popover>
                </template>
                <template s-else>
                    <s-checkbox
                        checked="{{row.write}}"
                        disabled="{{disabled}}"
                        on-change="onChangeCheckBox('write', rowIndex, $event)"
                    />
                </template>
            </div>
            <div slot="c-use">
                <s-checkbox
                    checked="{{row.use}}"
                    disabled="{{disabled}}"
                    on-change="onChangeCheckBox('use', rowIndex, $event)"
                />
            </div>
            <div slot="empty">
                <s-empty
                    vertical
                    actionText="{{type === 'other' ? '马上添加>' : ''}}"
                    on-click="onAddOtherCert"
                />
            </div>
        </s-table>
        <slot name="bottom"/>
    </div>`;

    static components = {
        's-table': Table,
        's-checkbox': Checkbox,
        'ellipsis-tip': EllipsisTip,
        'instant-editor': InstantEditor,
        's-popover': Popover,
        's-empty': Empty
    };

    initData() {
        return {
            hasEditName: false
        };
    }

    static filters = {
        formatEmpty
    };

    onChangeCheckBox(type: string, rowIndex: number, target: {value: boolean}) {
        this.data.set(`datasource[${rowIndex}].${type}`, target.value);
    }

    onAddOtherCert() {
        this.fire('add', 'onAddOtherCert');
    }
}
