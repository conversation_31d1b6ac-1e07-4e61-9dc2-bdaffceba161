/**
 * 查看证书
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import DrawerCommon from '@/components/drawer-common';
import api from '@/common/client';
import {CILENT_SILENT} from '@/common/config';

import CertTable from './cert-table';
import AddOtherCert from './add-other-cert';
import format from './format';

import './index.less';

const klass = 'view-cert';

const getColumns = (item: {name: string, label: string, width?: number}, isTopic: Boolean = true) => {
    return isTopic ?
        [
            {name: 'id', label: '证书序列号', width: 80},
            item,
            {name: 'read', label: '读取权限', width: 80},
            {name: 'write', label: '写入权限', width: 80}
        ]
        :
        [
            {name: 'id', label: '证书序列号', width: 80},
            item,
            {name: 'use', label: '使用权限', width: 80},
        ]
};

type CertTopicList = Array<{certificateId: string, description: string, topicOperation?: string, uuid?: string}>;
type CertConsumerList = Array<{certificateId: string, description: string, consumerGroupOperation: string}>;
type ResultList = {authorizedCertificateList?: CertTopicList, authorizedCertificates?: CertConsumerList};

export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="{{name}}"
            checkChange="{{checkChange}}"
            save="{{save}}"
            on-edit="onEdit"
            on-cancel="onCancel"
            on-close="onClose"
        >
            <cert-table
                title="我的特权证书"
                desc="特权证书对该主题的权限只能在证书里去修改"
                datasource="{= privilege =}"
                columns="{{normalColumns}}"
                loading="{{privilegeLoading}}"
                disabled
            />
            <cert-table
                title="我的普通证书"
                datasource="{= ordinary =}"
                columns="{{normalColumns}}"
                disabled="{{mode === 'read'}}"
                loading="{{ordinaryLoading}}"
            />
            <cert-table
                datasource="{= other =}"
                type="other"
                columns="{{otherColumns}}"
                disabled="{{mode === 'read'}}"
                loading="{{otherLoading}}"
                hasEditName="{{mode === 'edit' && isTopic}}"
                editName="{{editName}}"
                on-add="onAddOtherCert"
            >
                <div slot="title" class="${klass}_add">
                    他人的证书
                </div>
                <span
                    slot="bottom"
                    class="${klass}_add_btn mt24"
                    on-click="onAddOtherCert"
                    s-if="{{mode === 'read'}}">
                    <s-outlined-plus color="#2468f2" />
                    添加
                </span>
            </cert-table>
        </drawer-common>
    </template>`;

    static components = {
        's-outlined-plus': OutlinedPlus,
        'cert-table': CertTable,
        'drawer-common': DrawerCommon
    };

    initData() {
        return {
            mode: 'read',
            privilege: [],
            ordinary: [],
            other: [],
            type: 'topic',
            normalColumns: getColumns({name: 'name', label: '证书名称', width: 80}),
            otherColumns: getColumns({name: 'name', label: '备注', width: 80}),
            editName: this.editName.bind(this),
            checkChange: this.checkChange.bind(this),
            save: this.onOk.bind(this)
        };
    }

    static computed: SanComputedProps = {
        isTopic(): boolean {
            return this.data.get('type') === 'topic';
        }
    }

    inited() {
        if (!this.data.get('isTopic')) {
            this.data.set('normalColumns', getColumns({name: 'name', label: '证书名称', width: 80}, false));
            this.data.set('otherColumns', getColumns({name: 'name', label: '证书名称', width: 80}, false));
        }
    }

    attached() {
        this.getCert();
    }

    // 获取证书（三项证书）
    getCert() {
        this.getPrivilege();
        this.getOrdinary();
        this.getOther();
    }

    // 获取特权证书信息
    getPrivilege() {
        this.data.set('privilegeLoading', true);
        const request = this.data.get('isTopic') ? api.bpsTopicAuthCertList : api.bpsAuthCertListByConsumerGroup;
        request(this.getCertParams('privilege'))
            .then((data: ResultList) => {
                const backup = this.handleFormat(data);
                this.data.set('privilege', backup);
                this.data.set('privilegeBackup', backup);
            })
            .finally(() => this.data.set('privilegeLoading', false));
    }

    // 获取普通证书信息
    getOrdinary() {
        this.data.set('ordinaryLoading', true);
        const request = this.data.get('isTopic') ? api.bpsTopicAuthCertList : api.bpsAuthCertListByConsumerGroup;
        request(this.getCertParams('ordinary'))
            .then((data: ResultList) => {
                const backup = this.handleFormat(data);
                this.data.set('ordinary', backup);
                this.data.set('ordinaryBackup', backup);
            })
            .finally(() => this.data.set('ordinaryLoading', false));
    }

    // 获取他人证书信息
    getOther() {
        this.data.set('otherLoading', true);
        const request = this.data.get('isTopic') ? api.bpsTopicAuthCertList : api.bpsAuthCertListByConsumerGroup;
        request(this.getCertParams('other'))
            .then((data: ResultList) => {
                const backup = this.handleFormat(data);
                this.data.set('other', backup);
                this.data.set('otherBackup', backup);
            })
            .finally(() => this.data.set('otherLoading', false));
    }

    // 获取证书的参数
    getCertParams(type: 'privilege' | 'ordinary' | 'other') {
        const {name, cluster, isTopic} = this.data.get();
        const accountId = this.$context.getUserId();
        if (isTopic) {
            return  {
                accountId,
                topic: name,
                topicCluster: cluster,
                listPrivilegeCerts: type === 'privilege' ? 'true' : 'false',
                listOrdinaryCerts: type === 'ordinary' ? 'true' : 'false',
                listOtherCerts: type === 'other' ? 'true' : 'false',
                begin: null,
                limit: null
            }
        }
        else {
            return {
                accountId,
                certType: type,
                consumerGroupName: name
            }
        }
    }

    // 处理格式
    handleFormat(data: ResultList) {
        const {isTopic} = this.data.get();
        if (isTopic) {
            return _.map(data.authorizedCertificateList, item => ({
                id: item.certificateId,
                name: item.description,
                read: item.topicOperation && item.topicOperation.indexOf('Read') !== -1,
                write: item.topicOperation && item.topicOperation.indexOf('Write') !== -1,
                uuid: item.uuid
            }));
        }
        else {
            return _.map(data.authorizedCertificates, item => ({
                id: item.certificateId,
                name: item.description,
                use: item.consumerGroupOperation && item.consumerGroupOperation.indexOf('Use') !== -1
            }));
        }
    }

    // 检测内容是否有改变
    checkChange() {
        return this.isChange(this.filterData());
    }

    // 编辑
    onEdit() {
        this.data.set('mode', 'edit');
    }

    // 取消
    onCancel() {
        this.data.set('mode', 'read');
        this.getCert();
    }

    // 保存
    onOk() {
        const request = this.data.get('isTopic')
            ? api.bpsTopicAuthModify
            : api.bpsModifyAuthCertInConsumerGroup;
        return request(this.filterData(), {
                ...CILENT_SILENT,
                'x-target-host': 'cce'
            })
            .then(() => {
                Notification.success('保存成功');
                this.data.set('mode', 'read');
                _.each(['ordinary', 'other'], item => {
                    this.data.set(`${item}Backup`, this.data.get(`${item}`));
                });
                this.getCert();
            });
    }

    // 关闭
    onClose() {
        this.dispose();
    }

    // 表格备注信息编辑
    async editName(value: string, rowIndex: number) {
        this.data.set(`other[${rowIndex}].name`, value);
    }

    // 添加他人证书
    onAddOtherCert() {
        const {name, cluster, type} = this.data.get('');
        const dialog = new AddOtherCert({data: {name, cluster, type, title: name}});
        dialog.attach(document.body);
        dialog.on('success', () => this.getOther());
    }

    isChange(obj: {
        addAuthOfSelfCerts: Array<any>,
        updateAuthOfSelfCerts: Array<any>,
        updateAuthOfOtherCerts: Array<any>,
        deleteAuthIdOfSelfCerts: Array<any>,
        deleteAuthIdOfOtherCerts: Array<any>
    }) {
        return !!obj.addAuthOfSelfCerts.length
            || !!obj.updateAuthOfSelfCerts.length
            || !!obj.updateAuthOfOtherCerts.length
            || !!obj.deleteAuthIdOfSelfCerts.length
            || !!obj.deleteAuthIdOfOtherCerts.length;
    }

    // 在提交请求前统一处理数据内容和格式
    filterData() {
        const toFind = ['ordinary', 'other'];
        const {name, cluster, isTopic} = this.data.get();
        const changes = _.map(toFind, type => {
            const origin = this.data.get(`${type}Backup`);
            const target = this.data.get(`${type}`);
            return isTopic
                ? format.TOPIC.findChange(origin, target)
                : format.CONSUMER.findChange(origin, target);
        });

        const base = isTopic
            ? {topic: name, topicCluster: cluster}
            : {consumerGroupName: name};

        return {
            ...base,
            addAuthOfSelfCerts: changes[0] && changes[0].add,
            updateAuthOfSelfCerts: changes[0] && changes[0].update,
            updateAuthOfOtherCerts: changes[1] && changes[1].update,
            deleteAuthIdOfSelfCerts: changes[0] && changes[0].del,
            deleteAuthIdOfOtherCerts: changes[1] && changes[1].del
        }
    }
}
