/**
 * 产品侧边栏
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {Select, Tooltip} from '@baidu/sui';
import {OutlinedInfoCircle, OutlinedLink} from '@baidu/sui-icon';
import {AppSidebar} from '@baidu/sui-biz';
import api from '@/common/client';
import {isOneCloudId} from '@/common/util';

import {ROUTE_PATH, CONFIG_TYPE} from '@/common/config';

import './index.less';
const $flag = ServiceFactory.resolve('$flag');
const KafkaVip = $flag.KafkaVip;
const isXushang = $flag.KafkaXushang;
@decorators.asSidebar()
export default class extends Component {
    static template = html`
    <s-sidebar
        class="bms-sidebar"
        title="消息服务 for Kafka"
        activeName="{= active =}" >
        <s-select
            class="bms-sidebar__select"
            s-if="${KafkaVip} && !${isXushang} && ShareKafkaAccessToUse && !isEdgeRegion"
            datasource="{{selects}}"
            value="{{type}}"
            width="{{150}}"
            on-change="onTypeChange"
        />
        <s-sidebar-item
            s-if="{{isOrdinaryShow}}"
            title="主题"
            name="${ROUTE_PATH.topic}"
            link-to="${ROUTE_PATH.topic}"
            activeName="{{active}}"
        />
        <s-sidebar-item
            s-if="{{isOrdinaryShow}}"
            title="证书"
            name="${ROUTE_PATH.certificate}"
            link-to="${ROUTE_PATH.certificate}"
            activeName="{{active}}"
        />
        <s-sidebar-item
            s-if="{{isOrdinaryShow}}"
            title="消费组管理"
            name="${ROUTE_PATH.consumergroup}"
            link-to="${ROUTE_PATH.consumergroup}"
            activeName="{{active}}"
        />
        <s-sidebar-item title="集群" name="cluster" s-if="{{!isOrdinaryShow}}" expand>
            <s-sidebar-item
                title="集群列表"
                name="${ROUTE_PATH.clusterList}"
                link-to="${ROUTE_PATH.clusterList}"
                activeName="{{active}}"
            />
            <s-sidebar-item
                title="集群配置"
                name="${ROUTE_PATH.configList}"
                link-to="${ROUTE_PATH.configList}"
                activeName="{{active}}"
            />
        </s-sidebar-item>
        <s-sidebar-item title="连接器" name="kafkaConnect" s-if="{{!isOrdinaryShow}}" expand>
            <s-sidebar-item
                title="连接器任务"
                name="${ROUTE_PATH.connectTask}"
                link-to="${ROUTE_PATH.connectTask}"
                activeName="{{active}}"
            />
            <s-sidebar-item
                title="连接器配置"
                name="${ROUTE_PATH.connectConfig}"
                link-to="${ROUTE_PATH.connectConfig}"
                activeName="{{active}}"
            />
            <s-sidebar-item
                title="连接器插件"
                name="${ROUTE_PATH.connectPlugin}"
                link-to="${ROUTE_PATH.connectPlugin}"
                activeName="{{active}}"
            />
        </s-sidebar-item>
        <s-sidebar-item title="常用功能" name="other" s-if="{{!isOrdinaryShow}}" expand>
            <s-sidebar-item
                s-if="{{!isOneCloud}}"
                title="集群标签"
                iconLink="link"
                target="{{!${isXushang} ? '_blank' : '_self'}}"
                name="${ROUTE_PATH.clusterTag}"
                link-to="${ROUTE_PATH.clusterTag}"
                activeName="{{active}}"
            />
            <s-sidebar-item
                s-if="!${isXushang}"
                title="操作审计"
                iconLink="link"
                target="{{'_blank'}}"
                name="${ROUTE_PATH.clusterAudit}"
                link-to="${ROUTE_PATH.clusterAudit}"
                activeName="{{active}}"
            />
            <s-sidebar-item
                s-if="!${isXushang}"
                target="{{'_blank'}}"
                name="${ROUTE_PATH.clusterBsc}"
                link-to="${ROUTE_PATH.clusterBsc}"
                activeName="{{active}}"
            >
                <div slot="title">
                    流式计算
                    <s-link color="#84868C" width="16px" height="16px"></s-link>
                    <s-tooltip placement="right" content="{{bscTip}}">
                        <s-info class="s-exclamation" color="#84868C" width="14px"/>
                    </s-tooltip>
                </div>
            </s-sidebar-item>
        </s-sidebar-item>
        </s-sidebar>`;

    static components = {
        's-select': Select,
        's-sidebar': AppSidebar,
        's-sidebar-item': AppSidebar.Item,
        's-tooltip': Tooltip,
        's-info': OutlinedInfoCircle,
        's-link': OutlinedLink
    };

    initData() {
        return {
            selects: [
                {text: '专享版', value: CONFIG_TYPE.VIP},
                {text: '共享版', value: CONFIG_TYPE.ORDINARY},
            ],
            isEdgeRegion: false,
            type: KafkaVip ? CONFIG_TYPE.VIP : CONFIG_TYPE.ORDINARY,
            isOneCloud: isOneCloudId(),
            bscTip: '提供Spark & Flink 双引擎的Serverless 流式计算服务，处理来自Kaka集群的数据，满足日常流式场景需求。'
        };
    }

    static computed: SanComputedProps = {
        isOrdinaryShow() {
            return this.data.get('type') === CONFIG_TYPE.ORDINARY;
        }
    };

    inited() {
        const active = this.data.get('active');
        const arr = [ROUTE_PATH.topic, ROUTE_PATH.certificate, ROUTE_PATH.consumergroup];
        this.data.set('type', _.includes(arr, active) ? CONFIG_TYPE.ORDINARY : CONFIG_TYPE.VIP);
    }

    attached() {
        this.getWhiteInfo();
        this.data.set('isEdgeRegion', window.$context.getCurrentRegionId() === 'edge');
        const $framework = window.$framework;
        if ($framework) {
            const key = $framework.EVENTS.AFTER_REGION_CHANGED;
            $framework.events.on(key, () => this.regionChange());
        }
    }

    // 获取磁盘白名单信息
    getWhiteInfo() {
        const featureTypes = ['ShareKafkaAccessToUse'];
        api.getUserAcls({featureTypes}).then((target: {isExist: boolean}) => {
            this.data.set('ShareKafkaAccessToUse', target.isExist);
        });
    }

    // region 切换
    regionChange() {
        this.data.set('isEdgeRegion', window.$context.getCurrentRegionId() === 'edge');
    }

    // 类型改变
    onTypeChange(target: {value: string}) {
        this.data.set('type', target.value);
        redirect(`#${target.value === CONFIG_TYPE.VIP ? ROUTE_PATH.clusterList : ROUTE_PATH.topic}`);
    }
}
