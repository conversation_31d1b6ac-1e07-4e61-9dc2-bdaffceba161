/**
 * 下拉提示框
 *
 * @file tip-select.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {Select, Tooltip} from '@baidu/sui';
import {html} from '@baiducloud/runtime';

import './index.less';

export default class extends Component {

    static template = html`
    <span class="custom-select">
        <s-select
            s-ref="select"
            filterable="{{filterable}}"
            multiple="{{multiple}}"
            check-all="{{checkAll}}"
            placeholder="{{placeholder}}"
            value="{= value =}"
            noMatchText="{{noMatchText}}"
            noDataText="{{noDataText}}"
            filterPlaceholder="{{filterPlaceholder}}"
            readonly="{{readonly}}"
            width="{{width}}"
            height="{{height}}"
            on-change="onChange"
            loading="{{loading}}"
            labelFilter="{{labelFilter}}"
            on-visible-change="onVisibleChange"
            on-query-change="onqueryChange"
            getPopupContainer="{{getPopupContainer}}">
            <s-tooltip
                s-for="item in datasource"
                class="select-tooltip"
                placement="{{placement}}">
                <s-option
                    value="{{item.value}}"
                    label="{{item.text}}"
                />
                <div slot="content" class="tip-content">
                    <slot
                        name="c-value"
                        var-item="{{item}}">
                        {{item.text}}
                    </slot>
                </div>
            </s-tooltip>
        </s-select>
    </span>`;

    static components = {
        's-select': Select,
        's-option': Select.Option,
        's-tooltip': Tooltip,
    }

    initData() {
        return {
            placement: 'right'
        }
    }

    attached() {
        this.data.set('getPopupContainer', this.ref('select'));
    }

    onChange(target: {value: string}) {
        this.fire('change', {value: target.value});
    }

    onVisibleChange(target: {value: string}) {
        this.fire('visible-change', target.value);
    }

    onqueryChange(target: {value: string}) {
        this.fire('query-change', target.value);
    } 
}