/**
 * 一些通用装饰器
 * decorator.ts
 */

// 防抖装饰器
export const debounce = (time: number = 200) => {
    return (_target: any, _properKey: string, descriptor: PropertyDescriptor) => {
        const fn: Function = descriptor.value;
        let timer: number | null;
        descriptor.value = function(...args: any[]) {
            if (!timer) {
                timer = window.setTimeout(() => {
                    fn.apply(this, args);
                    timer && window.clearTimeout(timer);
                    timer = null;
                }, time);
            }
        };
    };
};

// 节流装饰器
export const throttle = (time: number = 200) => {
    return (_target: any, _properKey: string, descriptor: PropertyDescriptor) => {
        const fn: Function = descriptor.value;
        let timer: number | null;
        descriptor.value = function (...args: any[]) {
            if (!timer) {
                fn.apply(this, args);
                timer = window.setTimeout(() => {
                    timer && window.clearTimeout(timer);
                    timer = null;
                }, time);
            }
        };
    };
};
