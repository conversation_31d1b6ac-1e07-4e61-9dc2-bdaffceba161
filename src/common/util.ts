/**
 * util 函数单元
 *
 * @file util.js
 * <AUTHOR>
 */

import _ from 'lodash';
import m from 'moment';
import JSEncrypt from 'jsencrypt/bin/jsencrypt.min';

import {MINUTE} from '@/common/config';
import {AvailableInfo, authenticationModesEnum} from '@/common/enums/constant';
import {redirect, html, ServiceFactory} from '@baiducloud/runtime';
import {defineComponent} from 'san';
import {Dialog} from '@baidu/sui';
import filesize from 'file-size';

let zoneLabel = '';

export const byte2GB = (bytes: number) => {
    return (!bytes && bytes !== 0) ? '0' : filesize(bytes).to('GB');
};

export const formatBytes = (bytes: number, decimals = 2) => {
    return filesize(bytes).human('jedec');
};

const $flag = ServiceFactory.resolve('$flag');
export const pickEmpty = (obj: Object) => {
    // @ts-ignore
    return _.pick(obj, _.identity);
};

// 空文本
export const formatEmpty = (value?: string | number) => !value ? '--' : value;

// 格式化时间
export const formatTime = (time?: string | number | null) => {
    return time ? m(time).format('YYYY-MM-DD HH:mm:ss') : formatEmpty();
};

// 将时间转化为utc格式
export const formatUtcTime = (time: string | number, pattern?: string) => {
    return m(time).utc().format(pattern || 'YYYY-MM-DDTHH:mm:ss') + 'Z';
};

// 获取名称，输入slashName格式为xxx__name,由于前面固定，可以使用split方式来切割
export const getName = (slashName: string) => {
    let arr = slashName.split('__');
    // 如果没有__，原地返回
    if (arr.length === 1) {
        return arr[0];
    }
    arr.shift();
    return arr.join();
};

// 根据bool值，获取两个字母拼接
export const getJoin = (a: boolean, aS: string, b: boolean, bS: string) => {
    if (a && b) {
        return aS + bS;
    }
    if (a) {
        return aS;
    }
    if (b) {
        return bS;
    }
    return '';
};

// 根据数据是否 status为'available'来取第一项
export const getAvailable = (arr: Array<{ [x: string]: any, status: AvailableInfo }>) => {
    return _.find(arr, i => i.status === AvailableInfo.AVAILABLE) || null;
};

// 在数组中查找，能找到，返回对应的key对应文本，否则返回空
export const getItemInfoInArr = (
    arr: Array<{ [x: string]: any }>, target: string | number,
    key: string = 'value', label: string = 'text') => {
    const item = _.find(arr, i => i[key] === target);
    return item && item[label] ? item[label] : '';
};

// 复制到剪贴板
export const copyDashboard = (content: string) => {
    const dom = document.createElement('input');
    document.body.appendChild(dom);
    dom.setAttribute('value', content);
    dom.select();
    document.execCommand('copy');
    document.body.removeChild(dom);
};

// 将数组转化为对象
export const tranferArrayToObject = (arr: Array<NormalObject>, key: string, value: string) => {
    let obj: NormalObject = {};
    arr.forEach((item) => {
        if (item[key] && item[value] !== void 0) {
            obj[item[key]] = item[value];
        }
    });
    return obj;
};

// 专享版可用区文本转化
export const renderZoneLabel = (label: string) => {
    const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
    if (isEdgeRegion) {
        window._regions_?.forEach(item => {
            item.cities?.forEach(i => {
                i.serviceProviders?.forEach(j => {
                    if (j.regionId === label) {
                        zoneLabel = item.name + '-' + i.name + '-' + j.name;
                    }
                });
            });
        });
        if (zoneLabel === '') {
            zoneLabel = label;
        }
        return zoneLabel;
    }
    else {
        const arr = label.split('-');
        return !arr.length ? label : `可用区${(arr[arr.length - 1]).toUpperCase()}`;
    }
};

// rsa加密
export const rsaEncrypt = (data: string) => {
    const publicKey = window.$context.getPublicKey();
    if (!publicKey) {
        return data;
    }

    let encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);
    let enc = encrypt.encrypt(data);
    if (enc === false) {
        return data;
    }

    return enc;
};

// 下载文件公用方法
export const downloadFile = (url: string) => {
    const a = document.createElement('a');
    a.href = url;
    a.click();
    a.remove();
};

// 用于将字符串包装成SanComponent类型
export const strToSanCmpt = (str: String) => {
    return defineComponent({
        template: html`
            <span class="content">{{msg | raw}}</span>
        `,
        initData() {
            return {
                msg: str
            };
        }
    });
}

// 实名认证弹窗
export const showCertificationDialog = () => {
    const msg = '当前账户尚未完成实名认证。会影响部分功能的正常使用，请您尽快完成实名认证。'
    const dialog = Dialog.warning({
        title: '实名认证确认',
        content: strToSanCmpt(msg),
        okText: '立即认证',
        onOk: () => {
            redirect('/qualify/#/qualify/index');
        }
    });
    return dialog;
};

// 自动刷新定时器
export const getTimer = (callback: Function, time = MINUTE) => {
    return setInterval(callback, time);
};

// 自动刷新定时器
export const getModes = (authenticationModes: string[]) => {
    let modes: string[] = [];
    authenticationModes.forEach(item => {
        const mode = authenticationModesEnum[item];
        modes.push(mode ? mode : item);
    });
    return modes.length > 0 ? modes.join('; ') : '';
};

// 自动续费周期
export const getPayLoop = (renewTime: number, renewTimeUnit: string) => {
    return '每' + renewTime + (renewTimeUnit.toLowerCase() === 'month' ? '月' : '年');
};

export const formatePrice = (price: number) => {
    let floatPart = price.toString().split(".")[1];

    if (floatPart && floatPart.length >=6) {
        const floatPartNew = parseInt(floatPart.slice(0, 6)) / 1000000;
        return parseInt(price.toString().split(".")[0]) + floatPartNew;
    } else {
        return price;
    }
};

// 派发虚商订单信息，虚商平台会监听该事件跳转平台自己的支付页
export const XuShangPostPayMessage = ({
    orderId = '',
    orderType = 'NEW',
}) => {
    if (!orderId) {
        return;
    };

    window.parent?.postMessage?.({
        orderInfo: {
            orderId,
            serviceType: 'KAFKA',
            fromService: 'KAFKA',
            orderType,
            accountId: window.$context.UserId,
        }
    }, window.XSREFERRER);
};

// 标签数据转换
export const tranferTag = (arr: Array<NormalObject>) => {
    let res: string[] = [];
    arr.forEach((item) => {
        res.push(item.tagKey + ': ' +  item.tagValue);
    });
    return res;
};

/**
 * 标签信息过滤器
 * @type {Object}
 */
export const TagFilter = {
    tagText(item) {
        let tagHtml = '';
        _.each(item.tags, (item, index) => {
            if (index < 2) {
                let text = `${_.escape(item.tagKey)} : ${_.escape(item.tagValue) || '-'}`;
                text = text.length > 16 ? (text.slice(0 , 16) + '...') : text;
                tagHtml += `${text}<br/>`;
            }
        });
        item.tags.length > 2 && (tagHtml = item.tags.length + '个标签');
        return tagHtml;
    },
    tagTip(item) {
        let tags = '';
        _.each(item.tags, (item, index) => {
            tags += `${_.escape(item.tagKey)} : ${_.escape(item.tagValue) || '-'}<br/>`;
        });
        return tags;
    }
};

export const getMonacoEditorSrcPath = () => {
    return $flag.template === 'public-console'
        ? 'https://code.bdstatic.com/npm/monaco-editor@0.21.2/min/' : './';
};

// 为沙盒环境
const isDev = () => /qasandbox/.test(location.hostname);

// 是否为集团云onecloud账户
export const isOneCloudId = () => window.$context.getUserId() === (isDev()
    ? '620aa86787a042b58b00af8ccb733efa' : '0c0b3c9dbb6e41308d3bfd587d908922');


export const maxBy = (array: any[], iteratee: any) => {
    if (!array || array.length === 0) {
        return undefined;
    }

    // 如果迭代器是字符串，则转换成函数
    const getValue = (typeof iteratee === 'function') ? iteratee : (obj) => obj[iteratee];

    let maxElement = array[0];
    let maxValue = getValue(maxElement);

    for (let i = 1; i < array.length; i++) {
        const value = getValue(array[i]);

        if (value > maxValue) {
            maxValue = value;
            maxElement = array[i];
        }
    }

    return maxValue;
};

export const isInvalid = (item: any): boolean => {
    return ['', undefined, null].includes(item);
};