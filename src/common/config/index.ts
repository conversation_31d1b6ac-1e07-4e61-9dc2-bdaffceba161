/**
 * @file 配置文件
 * <AUTHOR>
 */
import {ServiceFactory} from '@baiducloud/runtime';

export {default as TABLE_COLUMNS} from './columns';
// const $flag = ServiceFactory.resolve('$flag');
// 在search框旁边的select框高度
export const SELECT_SEARCH_HEIGHT = '28';
export const SELECT_SEARCH_WIDTH = '300';
// sui分页一些公用配置
export const PAGER_SUI = {
    pageSize: 10,
    page: 1,
    pageSizes: [10, 20, 50, 100]
};

// sui表格一些公用配置
export const TABLE_SUI = {
    error: false,
    loading: false,
    datasource: []
};

// sui的表格多选配置
export const SELECTION_SUI = {
    mode: 'multi',
    selectedIndex: []
};

// 输入框长度
export const INPUT_WIDTH = 240;

// 弹框类输入框长度
export const DIALOG_INPUT_WIDTH = 400;

// 弹框类输入框长度-短
export const DIALOG_INPUT_WIDTH_SHOT = 280;

// 下拉高度
export const SELECT_HEIGHT = 28;

// 集群创建高可用模式可用区个数
export const ZoneMutilMax = 3;

// 请求静默
export const CILENT_SILENT = {
    'X-silence': true,
    'x-silent': true
};

// 分页请求所有配置
export const PAGER_MAX = {
    pageNo: 1,
    pageSize: 100000
};

// 路由
export const ROUTE_PATH = {
    newActive: '/kafka/order/create',
    vipActive: '/kafka/cluster/activate',
    topic: '/kafka/topic/list',
    certificate: '/kafka/certificate/list',
    consumergroup: '/kafka/consumergroup/list',
    monitor: '/kafka/topic/monitor',
    clusterList: '/kafka/cluster/list',
    clusterTag: '/tag/#/tag/instance/list',
    clusterAudit: '/bct/#/bct/user/audit/log',
    clusterBsc: '/bsc/#/bsc/home',
    clusterCreate: '/kafka/cluster/create',
    clusterUpgrage: '/kafka/cluster/upgrage',
    clusterPaymentUpgrage: '/kafka/cluster/payment/upgrage',
    clusterDetailInfo: '/kafka/cluster/detail/info',
    clusterDetailTopic: '/kafka/cluster/detail/topic',
    clusterDetailConsumer: '/kafka/cluster/detail/consumer',
    clusterDetailUser: '/kafka/cluster/detail/user',
    clusterDetailAuthority: '/kafka/cluster/detail/authority',
    clusterDetailProcess: '/kafka/cluster/detail/process',
    clusterDetailStorage: '/kafka/cluster/detail/storage',
    clusterDetailMonitor: '/kafka/cluster/detail/monitor',
    clusterDetailLog: '/kafka/cluster/detail/log',
    clusterDetailMessage: '/kafka/cluster/detail/message',
    clusterOperationList: '/kafka/cluster/detail/operation',
    clusterTaskDetail: '/kafka/cluster/detail/operation/task',
    // 主题详情相关页面
    clusterDetailTopicDetailInfo: '/kafka/cluster/detail/topic/info',
    clusterDetailTopicDetailQuote: '/kafka/cluster/detail/topic/quote',
    clusterDetailTopicDetailAuthority: '/kafka/cluster/detail/topic/authority',
    clusterDetailTopicDetailSubscription: '/kafka/cluster/detail/topic/subscription',
    clusterDetailTopicDetailMessage: '/kafka/cluster/detail/topic/detail/message',
    clusterDetailTopicDetailStorage: '/kafka/cluster/detail/topic/detail/storage',
    // 消费组详情相关页面
    clusterDetailConsumerSubscription: '/kafka/cluster/detail/consumer/subscription',
    configList: '/kafka/config/list',
    configCreate: '/kafka/config/create',
    configDetail: '/kafka/config/detail',
    // 连接器任务相关界面
    connectTask: '/kafka/connect/task',
    connectCreate: '/kafka/connect/create',
    connectDetailInfo: '/kafka/connect/detail/info',
    // 连接器配置相关界面
    connectConfig: '/kafka/connect/config',
    // 连接器插件相关页面
    connectPlugin: '/kafka/connect/plugin',
};

// 一些单位
export const CELL = {
    Mbps: 'Mbps',
    GB: 'GB'
};

export const LANG_MAP = {
    day: '天',
    hour: '小时',
    minute: '分钟',
    second: '秒'
};

export const CONFIG_TYPE = {
    ORDINARY: 'ordinary',
    VIP: 'vip'
};

export const MINUTE = 60 * 1000;

/** 1小时对应的ms */
export const OneHourMs = 60 * MINUTE;
/** 1天对应的ms */
export const OneDayMs = 24 * OneHourMs;

// 监控相关
export const SCOPE = 'BCE_KAFKA';
export const CLUSTER_SCOPE = 'BCE_MQ_KAFKA';
export const SERVICE_TYPE = 'KAFKA';

// 颜色相关
export const COLOR_CONF = {
    defaultColor: '#2468f2',
    disableColor: '#b8babf'
};

export const none = {
    type: 'PLAINTEXT',
    verify: 'NONE',
    aclEnable: false,
    secret: false,
    usage: 'VPC内访问',
    userVision: true,
    port: 9092
};
export const nonevpc = {
    type: 'PLAINTEXT',
    verify: 'NONE',
    aclEnable: false,
    secret: false,
    usage: '产品间转储',
    userVision: false,
    port: 9096
};
export const sasl = {
    type: 'SASL_PLAINTEXT',
    verify: 'SASL/SCRAM',
    secret: false,
    aclEnable: true,
    usage: 'VPC内访问',
    userVision: true,
    port: 9093
};
export const saslnetwork = {
    type: 'SASL_SSL',
    verify: 'SASL/SCRAM',
    aclEnable: true,
    secret: true,
    usage: '公网访问',
    userVision: true,
    port: 9095
};
export const ssl = {
    type: 'SSL',
    verify: 'SSL',
    secret: true,
    aclEnable: false,
    usage: 'VPC内访问',
    userVision: true,
    port: 9097
};
export const sslnetwork = {
    type: 'SSL',
    verify: 'SSL',
    aclEnable: false,
    secret: true,
    usage: '公网访问',
    userVision: true,
    port: 9099
};
export const sasl_plain = {
    type: 'SASL_PLAINTEXT',
    verify: 'SASL/PLAIN',
    secret: false,
    aclEnable: true,
    usage: 'VPC内访问',
    userVision: true,
    port: 9093
};
export const sasl_plain_network = {
    type: 'SASL_SSL',
    verify: 'SASL/PLAIN',
    aclEnable: true,
    secret: true,
    usage: '公网访问',
    userVision: true,
    port: 9095
};

export const ssl_private = {
    type: 'SSL',
    verify: 'SSL',
    secret: true,
    aclEnable: false,
    usage: '产品间转储',
    userVision: false,
    port: 9101
};
export const postPaidParam = {
    serviceType: 'KAFKA',
    count: 1,
    duration: 1,
    timeUnit: 'MINUTE',
    // region: this.$context.getCurrentRegionId(),
    // scene: "NEW",
    chargeItem: 'RunningTimeMinutes',
    productType: 'postpay',
    // orderType: "RESIZE", 变配参数
    // instanceId: this.data.get('clusterId') 变配参数
};

export const prePaidParam = {
    serviceType: 'KAFKA',
    count: 1,
    // region: this.$context.getCurrentRegionId(),
    // orderType: "NEW",  降配"SHRINKAGE"  升配"DILATATION"
    productType: 'prepay',
    subProductType: 'Cpt2',
    // duration: priceData.timeLength, 页面内添加
    // timeUnit: priceData.timeUnit.toUpperCase(), 页面内添加
    // instanceId: this.data.get('clusterId') 变配参数
};

export const TICKET_LINK = 'https://ticket.bce.baidu.com/?#/ticket/create?module=KAFKA&requestId=af05dc85-61ca-4a93-aced-c17abe8a0ec2';

// 这里配置path即可！！！！！！！！！！！！
// 私有化交付时，文档服务的域名会不同，下面的DocService会为业务动态渲染环境中的文档服务域名
let DOC_LINKS = {
    bmrProduct: '/product/bmr.html',
    blsProduct: '/product/bls.html',
    paloProduct: '/product/palo.html',
    iotProduct: '/product/iot.html',
    bosProduct: '/product/bos.html',
    kafkaIntroDoc: '/doc/Kafka/index.html',
    kafkaHelp: '/doc/Kafka/s/9jwvygf3k',
    kafkaCharging: '/doc/Kafka/s/Rjwvygjhv',
    tagHelp: '/doc/TAG/s/Tkbp242pi',
    kafkaOrdinaryHelp: '/doc/Kafka/s/9jwvygf3k',
    kafkaVipHelp: '/doc/Kafka/s/Jl4dv49qa',
    releaseLog: '/doc/Kafka/s/Ik0enfo04',
    autoRenew: '/doc/Finance/s/gjwvysrlu',
    brokerDeploySet: '/doc/BCC/s/4kb783hy7',
};
try {
    ServiceFactory.register('$doc', ServiceFactory.create('$doc', DOC_LINKS));
    DOC_LINKS = ServiceFactory.resolve('$doc') as typeof DOC_LINKS;
}
catch (e) {
    console.error('无法注册$doc');
}

// 文档链接
export const DOCS_LINK = DOC_LINKS;

export const kafkaRoleInfo = {
    roleName: 'BceServiceRole_KAFKA',
    policyId: '5e0298b794d941f1acfccf38ed2f8c4e',
    serviceId: '1b0b33380b6b427c8ed50f1ce99ec505',
};
