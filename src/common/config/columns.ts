/**
 * 公用配置 - 公用table
 *
 * @file table-columns.js
 * <AUTHOR>
 */

import {formatTime, formatEmpty} from '@/common/util';

export default {

    partitionColumns: [
        {name: 'partition', label: '分区', width: ''},
        {name: 'commitOffset', label: '当前提交offset', width: ''},
        {name: 'logEndOffset', label: '最新offset', width: ''},
        {name: 'lag', label: '滞后量'},
        {
            name: 'consumerInstance',
            label: '消费实例',
            render: (item: Subscription) => formatEmpty(item.consumerInstance),
            width: ''
        },
        {
            name: 'commitTimestamp',
            label: '最后消费时间',
            minWidth: 60,
            render: (item: Subscription) => {
                return item.commitTimestamp > 0
                    ? formatTime(item.commitTimestamp)
                    : formatEmpty();
            },
            width: ''
        }
    ],

    protocolColumns: [
        {name: 'type', label: '协议类型', width: 150},
        {name: 'verify', label: '认证方式', width: 100},
        {name: 'aclEnable', label: '权限控制', width: 100},
        {name: 'secret', label: '传输加密', width: 100},
        {name: 'usage', label: '使用场景', width: 100},
        {name: 'port', label: '访问端口', width: 100}
    ],
};
