/* eslint-disable max-len */
/**
 * client.js
 *
 * @file client.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {ServiceFactory} from '@baiducloud/runtime';
import HttpClient from '@baiducloud/httpclient';

const suffex = '/api/kafka/v2';
const suffexV3 = '/api/kafka/v3';
export const suffexV3Cluster = suffexV3 + '/clusters';

// 旧版api
const api = {
    // 订单创建
    bpsOrderCreate: `${suffex}/order/create`,
    // 订单是否已创建
    bpsOrderCreateCheck: `${suffex}/order/create_check`,

    // 获取主题列表，支持分页搜索
    bpsTopicList: `${suffex}/topic/list`,
    // 获取所有主题列表
    bpsTopicListAll: `${suffex}/topic/listall`,
    // 创建主题
    bpsTopicCreate: `${suffex}/topic/create`,
    // 删除主题
    bpsTopicDelete: `${suffex}/topic/delete`,
    // 检查监听该主题的服务
    bpsTopicListener: `${suffex}/topiclistener/list`,

    // 配置列表，主要为获取uuid
    bpsCertificateList: `${suffex}/certificate/list`,
    // 配置刷新
    bpsCertificateRegenerate: `${suffex}/certificate/regenerate`,
    // 集群列表
    bpsTopicCluster: `${suffex}/cluster/permission`,
    // 分区扩容
    bpsPartitionAdd: `${suffex}/topic/partition/add`,
    // 消费者组
    bpsGroupList: `${suffex}/consumergroups/list`,
    // group分区监控
    bpsGroupPart: `${suffex}/metrics/partitionstates`,

    // 配额
    bpsQuota: `${suffex}/quota`,

    // add 2017.12.26
    // 查询对此topic有权限的证书
    bpsTopicAuthCertList: `${suffex}/authorization/topics/list`,
    // 在ConsumerGroup页面，查询对此consumerGroup有权限的证书
    bpsAuthCertListByConsumerGroup: `${suffex}/authorization/consumergroup/list`,
    // 为别人的证书增加权限
    bpsTopicAuthForOther: `${suffex}/authorization/add`,
    // 更改证书权限
    bpsTopicAuthModify: `${suffex}/authorization/topics/modify`,

    // 在ConsumerGroup页面，点击查看证书，里面进行修改证书的操作接口
    bpsModifyAuthCertInConsumerGroup: `${suffex}/authorization/consumergroup/modify`,
    // 在ConsumerGroup页面，点击查看证书，为别人的证书增加权限
    bpsTopicAuthForOtherInConsumerGroup: `${suffex}/authorization/consumergroup/add`,


    // 创建证书
    bpsCertificateCreate: `${suffex}/certificate/create`,
    // 删除证书
    bpsCertificateDelete: `${suffex}/certificate/delete`,
    // 更改证书备注
    bpsCertificateUpdate: `${suffex}/certificate/update`,
    // 更改证书保留时长
    bpsCertificateLogRetentionUpdate: `${suffex}/topic/alter/logRetention`,

    // 查询证书权限
    // 给定证书id，返回自有topic的权限列表和对别人topic的权限列表
    bpsCertificateAuthList: `${suffex}/authorization/certificates/list`,
    // 在证书列表，给定证书id，返回自有consumergroup的权限列表和对别人consumergroup的权限列表
    bpsConsumerGroupListInAuthCert: `${suffex}/authorization/certificates/consumergroup/list`,
    // 更改证书权限
    bpsCertificateAuthModify: `${suffex}/authorization/certificates/modify`,
    // 在证书列表页面，点击查看ConsumerGroup，在这里面修改ConsumerGroup的操作接口
    bpsModifyConsumerGroupInAuthCert: `${suffex}/authorization/certificates/consumergroup/modify`,

    // Consumer Group 列表
    bpsConsumerGroupList: `${suffex}/consumergroups/new/list`,
    // 创建Consumer Group
    bpsConsumerGroupCreate: `${suffex}/consumergroups/new/create`,
    // 删除Consumer Group
    bpsConsumerGroupDelete: `${suffex}/consumergroups/new/delete`,
    // 重置位点
    bpsConsumerGroupResetOffset: `${suffex}/consumergroups/new/resetoffset`,
    // 根据消费组查询topics
    bpsConsumerGroupTopics: `${suffex}/consumergroups/new/list/topics`,

    // BCM趋势图，被所有模块依赖
    bcmAlarmStateSummary: '/api/bcm/alarm/state/summary',
    bcmMetricDataMetricName: '/api/bcm/metricdata/v2/datas/metricname',
    bcmMetricDataDimensions: '/api/bcm/metricdata/v2/datas/dimensions',
    bcmMetricList: '/api/bcm/metricmeta/metric/list',

    // iam对应接口
    // 判断激活接口
    iamStsRoleQuery: '/api/iam/sts/role/query',
    // 激活接口
    iamStsRoleActivate: '/api/iam/sts/role/activate',
    // 用户信息接口
    iamAccountDetail: '/api/iam/account/detail'
};

class Client extends HttpClient {
    [key: string]: Function;

    constructor() {
        super({}, ServiceFactory.resolve('$context'));
        _.each(api, (url: string, key: string) => {
            this[key] = (param: Object, options: Object = {}) => this.post(url, param, options);
        });
    }

    // 下载证书
    downloadCert(uuid: string): Promise<string> {
        const url = `${suffex}/download/kafka-client/config/v2/${this.$context.getCurrentRegionId()}/${uuid}`;
        return this.get(url, {responseType: 'blob'}, {}).then(() => url);
    }

    // 创建集群
    createCluster(param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}`, param, options);
    }

    // 创建集群-集群类型
    listDeployType(param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/types`, param, options);
    }

    // 创建集群-集群版本列表
    listAvailableVersion(param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/versions`, param, options);
    }

    // 创建集群-可用vpc
    listVpc(param: object, options: object = {}) {
        return this.get(`${suffexV3Cluster}/vpcs`, param, options);
    }

    // 创建集群-可用的子网和可用区
    listSubnet(vpcid: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/vpcs/${vpcid}/subnets`, param, options);
    }

    // 创建集群-对应子网的安全信息
    listSecurityGroup(vpcid: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/vpcs/${vpcid}/security-groups`, param, options);
    }

    // 创建集群-检测名称
    checkClusterName(param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/check-name`, param, options);
    }

    // 创建集群-列出用户在某个region可用区情况
    listZone(param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/zones`, param, options);
    }

    // 创建集群-根据传入的可用区返回用户可用的套餐
    queryFlavor(param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/flavors`, param, options);
    }

    // 创建集群-询价接口
    queryPrice(param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/prices`, param, options);
    }

    // 集群变更-询价接口
    upgrageQueryPrice(clusterId: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/prices`, param, options);
    }

    // 虚商-询价接口
    queryPriceInXS(param: Object, options: Object = {}) {
        return this.post('/api/price/v3/order', param, options);
    }

    // 集群列表-列出集群
    listCluster(param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}`, param, options);
    }

    // 集群详情-获取集群详情
    getClusterDetail(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}`, param, options);
    }

    // 更新集群
    updateCluster(clusterId: string, param: Object, options: Object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}`, param, options);
    }

    // 集群详情-查看接入点信息
    getAccessPoints(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/access-endpoints`, param, options);
    }

    // 集群详情-集群配置详情内容申请创建集群
    getZkPasswd(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/zookeeper-password`, param, options);
    }

    // 集群详情-集群配置详情内容申请创建集群
    getNodeList(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/nodes`, param, options);
    }

    // 集群详情-集群配置详情内容申请创建集群
    deleteCluster(clusterId: string, param: Object, options: Object = {}) {
        return this.delete(`${suffexV3Cluster}/${clusterId}`, param, options);
    }

    // 集群详情-重启节点
    restartNode(clusterId: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/node`, param, options);
    }

    // 集群主题相关-创建主题
    createClusterTopic(clusterId: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/topics`, param, options);
    }

    // 集群主题相关-Topic高级配置
    getClusterTopicConfigOptions(param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/topics/config-options`, param, options);
    }

    // 集群主题相关-更新Topic的相关参数
    updateClusterTopic(clusterId: string, topic: string , param: Object, options: Object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/topics/${topic}`, param, options);
    }

    // 集群主题相关-主题重分区
    resplitClusterTopic(clusterId: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/reassign-partitions`, param, options);
    }


    listOperations(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/operations`, param, options);
    }

    listJobs(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/jobs`, param, options);
    }

    getOperationDetail(clusterId: string, operationId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/operations/${operationId}`, param, options);
    }

    getGroupDetaill(clusterId: string, operationId: string, groupName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/operations/${operationId}/groups/${groupName}`, param, options);
    }

    // 集群主题相关-主题列表
    listClusterTopic(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics`, param, options);
    }

    // 集群主题相关-删除主题
    deleteClusterTopic(clusterId: string, topic: string, param: Object, options: Object = {}) {
        return this.delete(`${suffexV3Cluster}/${clusterId}/topics/${topic}`, param, options);
    }

    // 集群主题相关-集群详情
    getClusterTopicDetail(clusterId: string, topic: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topic}`, param, options);
    }

    // 获取分区状态汇总信息（表头信息）
    getClusterTopicStatusSummary(clusterId: string, topic: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topic}/partitions/statuses/overview`, param, options);
    }

    // 集群主题相关-获取分区状态
    getClusterTopicStatus(clusterId: string, topic: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topic}/partitions/statuses`, param, options);
    }

    // 集群主题相关-获取主题订阅列表
    listClusterTopicSubscribeStatus(clusterId: string, topic: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topic}/consumer-groups`, param, options);
    }

    // 获取主题订阅关系汇总信息（表头信息）
    getClusterTopicSubscribeStatusSummary(clusterId: string, topic: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topic}/consumer-groups/overview`, param, options);
    }

    // 集群主题相关-获取主题与消费组之间的订阅关系
    getClusterTopicConsumerGroupSubscribeStatus(clusterId: string, topic: string, groupName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topic}/consumer-groups/${groupName}/subscribe-details`, param, options);
    }

    // 集群消费组相关-创建消费组
    createClusterConsumerGroup(clusterId: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/consumer-groups`, param, options);
    }

    // 集群消费组相关-获取消费组列表
    listClusterConsumerGroups(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/consumer-groups`, param, options);
    }

    // 集群消费组相关-获取消费组详情
    getClusterConsumerGroupDetail(clusterId: string, groupName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/consumer-groups/${groupName}/topics`, param, options);
    }

    // 集群消费组相关-获取该消费组订阅的某个topic的详细信息
    getClusterConsumerGroupSubscribeTopicDetail(clusterId: string, groupName: string, topicName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/consumer-groups/${groupName}/topics/${topicName}/consume-details`, param, options);
    }

    // 集群消费组相关-获取订阅该消费组的Topic&Partition信息
    listClusterSubscribedTopicPartitions(clusterId: string, groupName: string, topicName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/consumer-groups/${groupName}/topics/${topicName}/partitions`, param, options);
    }

    // 集群消费组相关-重置位点
    resetClusterOffset(clusterId: string, groupName: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/consumer-groups/${groupName}/offsets`, param, options);
    }

    // 集群消费组相关-删除消费组
    deleteClusterConsumerGroup(clusterId: string, groupName: string, param: Object, options: Object = {}) {
        return this.delete(`${suffexV3Cluster}/${clusterId}/consumer-groups/${groupName}`, param, options);
    }

    // 集群消费组相关-获取消费组订阅的Topic汇总信息
    getClusterSubscribedTopicsSummary(clusterId: string, groupName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/consumer-groups/${groupName}/topics/overview`, param, options);
    }

    // 用户管理-用户列表
    getClusterUserList(clusterId: string, param: Object = {}, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/users`, param, options);
    }

    // 用户管理-创建用户
    createClusterUser(clusterId: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/users`, param, options);
    }

    // 用户管理-重置用户密码
    updateClusterUser(clusterId: string, username: string, param: Object, options: Object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/users/${username}`, param, options);
    }

    // 用户管理-删除用户
    deleteClusterUser(clusterId: string, username: string, options: Object = {}) {
        return this.delete(`${suffexV3Cluster}/${clusterId}/users/${username}`, {}, options);
    }

    // 集群监控-获取集群节点列表
    listClusterBrokers(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/monitor/nodes`, param, options);
    }

    // 集群监控-获取Topic列表
    listClusterAllTopics(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/monitor/topics`, param, options);
    }

    // 集群监控-获取消费组列表
    listClusterAllConsumerGroups(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/monitor/consumer-groups`, param, options);
    }

    // 集群监控-获取消费组列表
    listClusterConsumerGroupsTopics(clusterId: string, groupName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/monitor/consumer-groups/${groupName}/topics`, param, options);
    }

    // 集群监控-获取集群Tab下需要展示的监控项列表
    listMonitorItems(param: { type: 'cluster' | 'topic' | 'consumerGroup' }, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/monitor/monitor-items`, param, options);
    }

    // 权限管理-权限列表
    getClusterAuthorityList(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/acls`, param, options);
    }

    // 权限管理-创建权限
    createClusterAuthority(clusterId: string, param: Object, options: Object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/acls`, param, options);
    }

    // 权限管理-删除权限
    deleteClusterAuthority(clusterId: string, param: Object, options: Object = {}) {
        return this.delete(`${suffexV3Cluster}/${clusterId}/acls`, param, options);
    }

    // 获取用户是否在白名单中
    getUserAcls(param: {featureTypes: string[]}, options: Object = {}) {
        return this.post(`${suffexV3}/users/acls`, param, options);
    }

    // 获取代金券列表
    getCouponList(param: Object, options: Object = {}) {
        return this.post('/api/coupon/avail', param, options);
    }

    // 激活代金券
    activeCoupon(param: Object, options: Object = {}) {
        return this.post('/api/coupon/activate', param, options);
    }
    // 获取流量峰值列表
    listThroughputs(param: {}, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/throughputs`, param, options);
    }

    // 获取规格推荐列表
    getClusterRecommandList(throughput: number, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/throughputs/${throughput}`, param, options);
    }

    // 获取集群分区数上限
    getClusterPartitionLimit(param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/partition-limit`, param, options);
    }

    // 获取集群日志列表
    listClusterLogList(clusterId: string, nodeId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/nodes/${nodeId}/logs`, param, options);
    }

    // 获取日志详情
    getLogDetail(clusterId: string, nodeId: string, filename: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/nodes/${nodeId}/logs/${filename}`, param, options);
    }

    // 节点重启接口
    restartClusterNode(clusterId: string, nodeId: string, param: Object, options: Object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/nodes/${nodeId}/restart-broker`, param, options);
    }

    // 消息查询接口
    listMessages(clusterId: string, topicName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topicName}/messages`, param, options);
    }

    listTopicOffset(clusterId: string, topicName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topicName}/partitions/offsets`, param, options);
    }

    // 立即执行任务
    excuteTask(clusterId: string, operationId: string, param: Object, options: Object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/operations/${operationId}/start`, param, options);
    }

    // 取消执行任务
    cancelTask(clusterId: string, operationId: string, param: Object, options: Object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/operations/${operationId}/cancel`, param, options);
    }

    suspendTask(clusterId: string, operationId: string, param: Object, options: Object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/operations/${operationId}/suspend`, param, options);
    }

    resumeTask(clusterId: string, operationId: string, param: Object, options: Object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/operations/${operationId}/resume`, param, options);
    }

    // 获取重分区任务执行进度
    getResignTaskProcess(clusterId: string, operationId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/operations/${operationId}/reassign-details`, param, options);
    }

    listDisks(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/monitor/disks`, param, options);
    }

    listTopicDisks(clusterId: string, topicName: string, brokerId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topicName}/brokers/${brokerId}/disks`, param, options);
    }

    listDataDisks(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/disks`, param, options);
    }

    bcmMetricDataMetricData(param: Object, options: Object = {}) {
        return this.post('/api/bcm/v2/csm/data/metricAllData', param, options);
    }

    // 获取标签列表
    getSearchTagList(param: Object, options = {}) {
        return this.post('/api/tag/list', param, options);
    }

    // 修改单个集群标签
    KAFKATagUpdate(param: Object, options = {}) {
        return this.post(`${suffexV3}/tag/assign`, param, options);
    }

    getControllerBroker(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/controller`, param, options);
    }

    getControllerHistroy(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/controller-history`, param, options);
    }

    getQuota(param: Object, options = {}) {
        return this.get(`${suffexV3}/quotas/low-workload`, param, options);
    }

    getBcmWhiteList(payload = {}) {
        return this.get('/api/bcm/v1/csm/config/check_user', payload);
    }

    getBrokerDetail(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/brokers/disk-usage`, param, options);
    }

    getClusterDiskUsage(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/disk-usage`, param, options);
    }

    getTopicDiskUsage(clusterId: string, topicName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topicName}/disk-usage`, param, options);
    }

    getDiskStorageDetail(clusterId: string, brokerId: string, diskId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/brokers/${brokerId}/disks/${diskId}/topics/disk-usage`, param, options);
    }

    getTopicBrokerDiskUsage(clusterId: string, topicName: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topicName}/brokers/disk-usage`, param, options);
    }

    getTopicBrokerDiskUsageDetail(clusterId: string, topicName: string, brokerId: string, diskId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/topics/${topicName}/brokers/${brokerId}/disks/${diskId}/disk-usage`, param, options);
    }
    getDecreasePlan(clusterId: string, param: Object, options = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/decrease-broker-count-plan`, param, options);
    }

    getParameters(clusterId: string, param: Object, options = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/reassign-partitions/parameters`, param, options);
    }

    downloadFile(clusterId: string, param: Object, options = {}) {
        const url = `${suffexV3Cluster}/${clusterId}/cert`;
        return this.get(url, {responseType: 'blob'}).then(() => url);
    }
    listConfig(param: Object, options: Object = {}) {
        return this.get(`${suffexV3}/configs`, param, options);
    }
    deleteConfig(configId: string, param: Object, options: Object = {}) {
        return this.delete(`${suffexV3}/configs/${configId}`, param, options);
    }
    createConfig(param: Object, options: Object = {}) {
        return this.post(`${suffexV3}/configs`, param, options);
    }

    stopCluster(clusterId: string, param: Object, options = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/stop`, param, options);
    }

    startCluster(clusterId: string, param: Object, options = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/start`, param, options);
    }

    restartCluster(clusterId: string, param: Object, options = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/restart`, param, options);
    }

    listConfigParams(param: Object, options: Object = {}) {
        return this.get(`${suffexV3}/configs/default-options`, param, options);
    }

    // 集群配置详情
    getConfigDetail(configId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3}/configs/${configId}`, param, options);
    }

    createConfigVersion(configId: string, param: Object, options: Object = {}) {
        return this.put(`${suffexV3}/configs/${configId}/revision`, param, options);
    }

    getConfigVersions(configId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3}/configs/${configId}/revisions`, param, options);
    }

    getConfigVersionDetail(configId: string, revisionId: number, param: Object, options: Object = {}) {
        return this.get(`${suffexV3}/configs/${configId}/revisions/${revisionId}`, param, options);
    }

    getClusterConfigDetail(clusterId: string, param: Object, options: Object = {}) {
        return this.get(`${suffexV3}/clusters/${clusterId}/configurations`, param, options);
    }

    checkResource(param: object, options = {}) {
        return this.post(`${suffexV3Cluster}/check-resources`, param, options);
    }

    selectLeader(clusterId: string, topic: string, param: object = {}, options: object = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/topics/${topic}/elect-leaders`, param, options);
    };

    getReSplitPlan(clusterId: string, param: object, options = {}) {
        return this.get(`${suffexV3}/clusters/${clusterId}/reassign-plan`, param, options);
    }

    clusterPaymentUpdate(clusterId: string, param: object, options = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/orders/to-prepay`, param, options);
    }

    messageSend(clusterId: string, topicName: string, param: object, options: object = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/topics/${topicName}/messages/send`, param, options);
    }

    getEips(param?: object, options: object = {}) {
        return this.post(`${suffexV3Cluster}/eips`, param, options);
    }

    getDecreaseBrokerCountPlan(clusterId: string, param?: object, options: object = {}) {
        return this.get(`${suffexV3}/clusters/${clusterId}/decrease-broker-count-plan`, param, options);
    }

    getEdgeNodeList(param: object, options: object = {}) {
        return this.get(`${suffexV3Cluster}/regionIds`, param, options);
    }

    getQuotas(clusterId: string, param: object, options = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/quotas`, param, options);
    }

    deleteQuotas(clusterId: string, param: object, options = {}) {
        return this.delete(`${suffexV3Cluster}/${clusterId}/quotas`, param, options);
    }

    updateQuotas(clusterId: string, param: object, options = {}) {
        return this.put(`${suffexV3Cluster}/${clusterId}/quotas`, param, options);
    }

    createQuotas(clusterId: string, param: object, options = {}) {
        return this.post(`${suffexV3Cluster}/${clusterId}/quotas`, param, options);
    }

    kafkaRoleQuery(data = {}) {
        return this.post('/api/iam/sts/role/query', data);
    }

    // 激活sts role的授权
    kafkaRoleActivate(data = {}) {
        return this.post('/api/iam/sts/role/activate', data);
    }

    kafkaIamConstantsV3(data = {}) {
        return this.get('/api/system/constants/v3', data);
    }

    getJmxEndpoiont(clusterId: string, param: object = {}, options: object = {}) {
        return this.get(`${suffexV3Cluster}/${clusterId}/jmx-endpoint`, param, options);
    }

}

export default new Client();

export const bjClient = new HttpClient({
    headers: {
        'X-Region': 'bj',
    }
}, {
    getCurrentRegion: () => 'bj',
    getCurrentRegionId: () => 'bj',
    getCsrfToken() {
        return this.$cookie.get('bce-user-info');
    },
});

bjClient.bcmMetricDataMetricData = (param: Object, options: Object = {}) => {
    return bjClient.post('/api/bcm/v2/csm/data/metricAllData', param, options);
};
