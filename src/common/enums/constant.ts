/**
 * @file 枚举
 * <AUTHOR>
 */

// 集群状态配置
export enum ClusterStatusType {
    NEW = 'NEW',
    DEPLOYING = 'DEPLOYING',
    ACTIVE = 'ACTIVE',
    DEPLOY_ROLLBACKING = 'DEPLOY_ROLLBACKING',
    DEPLOY_FAILED = 'DEPLOY_FAILED',
    DEPLOY_ROLLBACK_FAILED = 'DEPLOY_ROLLBACK_FAILED',
    PRE_UPDATING = 'PRE_UPDATING',
    UPDATING = 'UPDATING',
    UPDATE_ROLLBACKING = 'UPDATE_ROLLBACKING',
    UPDATE_ROLLBACK_FAILED = 'UPDATE_ROLLBACK_FAILED',
    PRE_REBOOTING = 'PRE_REBOOTING',
    REBOOTING = 'REBOOTING',
    REBOOT_ROLLBACKING = 'REBOOT_ROLLBACKING',
    REBOOT_ROL<PERSON><PERSON><PERSON>K_FAILED = 'REBOOT_ROLLBACK_FAILED',
    PRE_DEPLOY_ROLLBACKING = 'PRE_DEPLOY_ROLLBACKING',
    PRE_UPDATE_ROLLBACKING = 'PRE_UPDATE_ROLLBACKING',
    PRE_REBOOT_ROLLBACKING = 'PRE_REBOOT_ROLLBACKING',
    PRE_SUSPENDING = 'PRE_SUSPENDING',
    SUSPENDING = 'SUSPENDING',
    SUSPENDED = 'SUSPENDED',
    SUSPEND_ROLLBACKING = 'SUSPEND_ROLLBACKING',
    SUSPEND_ROLLBACK_FAILED = 'SUSPEND_ROLLBACK_FAILED',
    PRE_RELEASING = 'PRE_RELEASING',
    PRE_RESUMING = 'PRE_RESUMING',
    RESUMING = 'RESUMING',
    RESUME_ROLLBACKING = 'RESUME_ROLLBACKING',
    RESUME_ROLLBACK_FAILED = 'RESUME_ROLLBACK_FAILED',
    PRE_SUSPEND_ROLLBACKING = 'PRE_SUSPEND_ROLLBACKING',
    PRE_RESUME_ROLLBACKING = 'PRE_RESUME_ROLLBACKING'
}

export enum ConfigStatusType {
    USED = 'USED',
    UNUSED = 'UNUSED',
}

// 代理节点状态枚举
export enum NodeClientStatusType {
    NEW = 'NEW',
    ALIVE = 'ALIVE',
    DEAD = 'DEAD',
    LOST = 'LOST'
}

// 付费类型
export enum PaymentType {
    PREPAID = 'Prepaid', // 预付费
    POSTPAID = 'Postpaid' // 后付费
}

// 任务类型
export enum ClusterTaskType {
    INCREASE_BROKER_COUNT = 'INCREASE_BROKER_COUNT',
    DECREASE_BROKER_COUNT = 'DECREASE_BROKER_COUNT',
    EXPAND_BROKER_DISK_CAPACITY = 'EXPAND_BROKER_DISK_CAPACITY',
    UPGRADE_BROKER_NODE_TYPE = 'UPGRADE_BROKER_NODE_TYPE',
    DOWNGRADE_BROKER_NODE_TYPE = 'DOWNGRADE_BROKER_NODE_TYPE',
    UPDATE_BROKER_NODE_TYPE = 'UPDATE_BROKER_NODE_TYPE',
    RESTART_BROKER = 'RESTART_BROKER',
    UPDATE_KAFKA_CONFIG = 'UPDATE_KAFKA_CONFIG',
    REASSIGN_PARTITION = 'REASSIGN_PARTITION',
    UPDATE_ACCESS_CONFIG = 'UPDATE_ACCESS_CONFIG',
    UPGRADE_KAFKA_VERSION = 'UPGRADE_KAFKA_VERSION',
    SUSPEND_CLUSTER = 'SUSPEND_CLUSTER',
    RESUME_CLUSTER = 'RESUME_CLUSTER',
    RESIZE_PUBLIC_IP_BANDWIDTH = 'RESIZE_PUBLIC_IP_BANDWIDTH',
    ENABLE_PUBLIC_IP = 'ENABLE_PUBLIC_IP',
    DISABLE_PUBLIC_IP = 'DISABLE_PUBLIC_IP',
    UPDATE_STORAGE_POLICY = 'UPDATE_STORAGE_POLICY',
    UPDATE_CLUSTER_SECURITY_GROUP = 'UPDATE_CLUSTER_SECURITY_GROUP',
    UPDATE_KAFKA_TOPIC_CONFIG = 'UPDATE_KAFKA_TOPIC_CONFIG',
    ENABLE_INTRANET_IP = 'ENABLE_INTRANET_IP',
    DISABLE_INTRANET_IP = 'DISABLE_INTRANET_IP',
    ENABLE_INTRANET_IP_ROLLBACK = 'ENABLE_INTRANET_IP_ROLLBACK',
    DISABLE_INTRANET_IP_ROLLBACK = 'DISABLE_INTRANET_IP_ROLLBACK',
    SWITCH_INTRANET_IP = 'SWITCH_INTRANET_IP',
    RESTART_CLUSTER = 'RESTART_CLUSTER',
    UPDATE_CLUSTER = 'UPDATE_CLUSTER',
}

// 任务状态
export enum ClusterTaskStatusType {
    PREPARE = 'PREPARE',
    NEW = 'NEW',
    PENDING = 'PENDING',
    RUNNING = 'RUNNING',
    FINISHED = 'FINISHED',
    FAILED = 'FAILED',
    SUSPENDED = 'SUSPENDED',
    CANCELED = 'CANCELLED'
}

// 推荐规格类型
export enum RecommandClusterType {
    HIGHPERFORMANCE= 'highPerformance', // 高性能
    COSTEFFECTIVE = 'costEffective' // 高可用
}

export enum Deployment {
    PERFROMANCE = 'HP',
    AVAILABILITY = 'HA'
};

// 付费时长
export enum PaymentTime {
    oneMon = 1,
    twoMon = 2,
    thrMon = 3,
    fourMon = 4,
    fiveMon = 5,
    sixMon = 6,
    sevMon = 7,
    eightMon = 8,
    nineMon = 9,
    oneYear = 12,
    twoYear = 24,
    thrYear = 36
}

export enum TIMERANGE_LIST {
    '1h' = 60 * 60 * 1000,
    '3h' = 3 * 60 * 60 * 1000,
    '6h' = 6 * 60 * 60 * 1000,
    '1d' = 24 * 60 * 60 * 1000,
    '3d' = 3 * 24 * 60 * 60 * 1000,
    '7d' = 7 * 24 * 60 * 60 * 1000,
    '14d' = 14 * 24 * 60 * 60 * 1000,
    '30d' = 30 * 24 * 60 * 60 * 1000,
    '90d' = 90 * 24 * 60 * 60 * 1000,
    '180d' = 180 * 24 * 60 * 60 * 1000,
    'selfConfig' = 0
}

// 创建集群模块
export enum ClusterRefType {
    REGION = 'region-config',
    CLUSTER = 'cluster-config',
    NODE = 'node-config',
    DISK = 'disk-config',
    NETWORK = 'network-config',
    ACCESS = 'access-config',
    TAG = 'tag-config'
}

// 可用状态
export enum AvailableInfo {
    AVAILABLE = 'available',
    UNAVAILABLE = 'unavailable'
}

// 权限-资源类型
export enum AuthorityResourceType {
    TOPIC = 'TOPIC',
    GROUP = 'GROUP',
    CLUSTER = 'CLUSTER',
    TRANSACTIONAL_ID = 'TRANSACTIONAL_ID'
};

// 权限-订阅状态
export enum OperationType {
    CONSUME = 'CONSUME',
    PRODUCE = 'PRODUCE',
    IDEMPOTENT_WRITE = 'IDEMPOTENT_WRITE',
    WRITE = 'WRITE'
}

export enum MessageQueryType {
    byTime = 'byTime',
    byOffset = 'byOffset'
}

export enum GroupNameType {
    // 资源余量检查
    CHECK_BCC_RESOURCE_FOR_KAFKA = '检查BCC资源余量',
    CHECK_CDS_RESOURCE_FOR_KAFKA = '检查CDS资源余量',
    // 资源申请
    APPLY_DEPLOY_SET_RESOUREC = '申请部署集资源',
    APPLY_BCC_RESOURCE_FOR_KAFKA = '申请KAFKA资源',
    APPLY_BCC_RESOURCE_FOR_ZOOKEEPER = '申请ZOOKEEPER资源',
    APPLY_EIP_RESOURCE_FOR_KAFKA = '申请EIP资源',
    ENABLE_PUBLIC_IP_STAGE_ASSIGN_KAFKA_NODE_EIP = '分配公网IP',
    APPLY_NEUTRON_RESOURCE = '申请NEUTRON资源',
    APPLY_CDS_RESOURCE_FOR_KAFKA = '申请CDS资源',
    RESIZE_NODE_TYPE_RESOURCE_KAFKA = '节点机型变更',
    // 环境准备
    INIT_DEPLOY_ENVIRONMENT = '初始化部署环境',
    // 系统操作
    RESIZE_FS_CAPACITY_FOR_KAFKA = '文件系统扩容',
    // 服务启动
    START_KAFKA_SERVICE = '启动KAFKA服务',
    RESTART_KAFKA_SERVICE = '重启KAFKA服务',
    CONFIG_KAFKA_SERVICE = '配置KAFKA服务',
    START_ZOOKEEPER_SERVICE = '启动ZOOKEEPER服务',
    // 元信息更新
    UPDATE_METADATA_FOR_CLUSTER = '更新集群元信息',
    // 主题重分区
    TOPIC_REASSIGN_PARTITION = '主题重分区',
    CANCEL_REASSIGN_PARTITION = '取消执行主题重分区',
    // 释放资源
    RELEASE_ALL_RESOURCE = '释放资源',
    // 空执行
    NOTHING = '空执行',
    ADJUST_SECURITY_GROUP = '调整安全组',
    RESIZE_EIP_BANDWIDTH_FOR_KAFKA = '变更EIP带宽',
    CHECK_EIP_RESOURCE_FOR_KAFKA = '检查EIP资源',
    UPDATE_KAFKA_CONFIG = '更新集群配置',
    UPDATE_KAFKA_TOPIC_CONFIG = '更新主题配置',
    ASSIGN_EIP_RESOURCE_FOR_KAFKA = '分配EIP资源',
    CHECK_NEUTRON_RESOURCE_FOR_KAFKA = '检查NEUTRON资源'
}

export enum DiagnosisType {
    APPLY_DEPLOY_SET_FAILED = '申请部署集失败',
    APPLY_BCC_FAILED = '申请BCC失败',
    BCC_SOLD_OUT = 'BCC售罄',
    APPLY_EIP_FAILED = '申请EIP失败',
    BIND_EIP_FAILED = '绑定EIP失败',
    START_WORKER_FAILED = '启动WORKER失败',
    GET_NEUTRON_METADATA_FAILED = '获取NEUTRON元信息失败',
    APPLY_NEUTRON_FAILED = '申请NEUTRON失败',
    CONFIG_KAFKA_FAILED = '配置KAFKA失败',
    START_KAFKA_FAILED = '启动KAFKA失败',
    CONFIG_ZOOKEEPER_FAILED = '配置ZOOKEEPER失败',
    START_ZOOKEEPER_FAILED = '启动ZOOKEEPER失败',
    UPDATE_METADATA_FOR_CLUSTER_FAILED = '更新集群元信息失败',
    UPDATE_METADATA_FOR_NODE_FAILED = '更新节点元信息失败',
    RESIZE_CDS_CAPACITY_FAILED = 'CDS扩容失败',
    RESIZE_CDS_CAPACITY_ROLLBACK_FAILED = 'CDS扩容回滚失败',
    RESIZE_FS_CAPACITY_FAILED = '文件系统扩容失败',
    UPDATE_NODE_TYPE_FOR_KAFKA_FAILED = 'KAFKA变配失败',
    UPDATE_NODE_TYPE_FOR_KAFKA_ROLLBACK_FAILED = 'KAFKA变配回滚失败',
    REINIT_VIRT_NET_FAILED = '网卡多队列配置失败',
    RECOVER_KAFKA_FAILED = '恢复KAFKA失败',
    RESTART_KAFKA_FAILED = '重启KAFKA失败',
    REASSIGN_PARTITION_FOR_TOPIC_FAILED = '执行主题重分区失败',
    CANCEL_REASSIGN_PARTITION_FOR_TOPIC_FAILED = '取消执行主题重分区失败',
    DISABLE_CLOUD_METRICS_FAILED = '释放监控项失败',
    RELEASE_NEUTRON_FAILED = '释放NEUTRON失败',
    RELEASE_BCC_FAILED = '释放BCC失败',
    RELEASE_EIP_FAILED = '释放EIP失败',
    RELEASE_DEPLOY_SET_FAILED = '放部署集失败',
    UNKONW_FAILED = '未知错误',
    BIND_SECURITY_GROUP_FAILED = '绑定安全组失败',
    UNBIND_SECURITY_GROUP_FAILED = '解绑安全组失败',
    RESIZE_EIP_BANDWIDTH_FAILED = '变更EIP带宽失败',
    RESIZE_EIP_BANDWIDTH_ROLLBACK_FAILED = 'EIP带宽变更回滚失败',
    DISABLE_EIP_ROLLBACK_FAILED = '回滚关闭EIP失败',
    APPLY_VPC_RESOUREC = '申请VPC资源',
}

export enum operationType {
    INCREASE_BROKER_COUNT = '扩容节点',
    INCREASE_BROKER_COUNT_ROLLBACK = '回滚扩容',
    REASSIGN_PARTITION = '调整主题',
    REASSIGN_PARTITION_ROLLBACK = '回滚调整',
    DECREASE_BROKER_COUNT = '缩容节点',
    DECREASE_BROKER_COUNT_ROLLBACK = '回滚缩容',
    EXPAND_BROKER_DISK_CAPACITY = '扩容磁盘',
    EXPAND_BROKER_DISK_CAPACITY_ROLLBACK = '回滚扩容',
    UPDATE_BROKER_NODE_TYPE = '变配节点',
    UPDATE_BROKER_NODE_TYPE_ROLLBACK = '回滚变配',
    RESTART_BROKER = '重启节点',
    RESTART_BROKER_ROLLBACK = '回滚重启',
    UPDATE_KAFKA_CONFIG = '更新配置',
    UPDATE_KAFKA_CONFIG_ROLLBACK = '回滚更新',
    UPDATE_KAFKA_TOPIC_CONFIG = '更新配置',
    UPDATE_KAFKA_TOPIC_CONFIG_ROLLBACK = '回滚更新',
    RESTART_CLUSTER = '重启集群',
    RESTART_CLUSTER_ROLLBACK = '回滚重启',
    UPDATE_ACCESS_CONFIG = '变更访问',
    UPDATE_ACCESS_CONFIG_ROLLBACK = '回滚变更',
    UPGRADE_KAFKA_VERSION ='升级版本',
    UPGRADE_KAFKA_VERSION_ROLLBACK = '回滚升级',
    RESUME_CLUSTER = '启动集群',
    RESUME_CLUSTER_ROLLBACK = '启动回滚',
    SUSPEND_CLUSTER = '停止集群',
    SUSPEND_CLUSTER_ROLLBACK = '停止回滚',
    RESIZE_PUBLIC_IP_BANDWIDTH = '变更带宽',
    RESIZE_PUBLIC_IP_BANDWIDTH_ROLLBACK = '回滚变更',
    ENABLE_PUBLIC_IP = '开启公网',
    DISABLE_PUBLIC_IP = '关闭公网',
    ENABLE_PUBLIC_IP_ROLLBACK = '回滚开启',
    DISABLE_PUBLIC_IP_ROLLBACK = '回滚关闭',
    UPDATE_STORAGE_POLICY = '变更策略',
    UPDATE_STORAGE_POLICY_ROLLBACK = '回滚变更',
    UPDATE_CLUSTER_SECURITY_GROUP = '更新安全组',
    UPDATE_CLUSTER_SECURITY_GROUP_ROLLBACK = '回滚变更',
    ENABLE_INTRANET_IP = '开启转储',
    DISABLE_INTRANET_IP = '关闭转储',
    ENABLE_INTRANET_IP_ROLLBACK = '回滚开启',
    DISABLE_INTRANET_IP_ROLLBACK = '回滚关闭',
    SWITCH_INTRANET_IP = '变更转储',
    SWITCH_INTRANET_IP_ROLLBACK = '回滚变更',
}

export enum taskType {
    INCREASE_BROKER_COUNT = '增加节点数量',
    DECREASE_BROKER_COUNT = '减少节点数量',
    EXPAND_BROKER_DISK_CAPACITY = '扩容节点磁盘',
    UPGRADE_BROKER_NODE_TYPE = '升级节点规格',
    DOWNGRADE_BROKER_NODE_TYPE = '降低节点规格',
    UPDATE_BROKER_NODE_TYPE = '变更节点规格',
    RESTART_BROKER = '重启集群节点',
    UPDATE_KAFKA_CONFIG = '更新集群配置',
    REASSIGN_PARTITION = '主题重新分区',
    UPDATE_ACCESS_CONFIG = '变更集群访问',
    UPGRADE_KAFKA_VERSION = '升级集群版本',
    RESUME_CLUSTER = '启动集群服务',
    SUSPEND_CLUSTER = '停止集群服务',
    RESIZE_PUBLIC_IP_BANDWIDTH = '变更公网带宽',
    ENABLE_PUBLIC_IP = '开启公网',
    DISABLE_PUBLIC_IP = '关闭公网',
    UPDATE_STORAGE_POLICY = '变更容量策略',
    UPDATE_CLUSTER_SECURITY_GROUP = '更新集群安全组',
    UPDATE_KAFKA_TOPIC_CONFIG = '更新主题配置',
    DISABLE_INTRANET_IP = '关闭产品间转储',
    ENABLE_INTRANET_IP = '开启产品间转储',
    SWITCH_INTRANET_IP = '变更产品间转储',
    RESTART_CLUSTER = '重启集群',
    UPDATE_CLUSTER = '变更集群'
}

export enum authenticationModesEnum {
    SASL_SCRAM = 'SASL/SCRAM 身份认证',
    NONE = 'None (无需身份认证)',
    SSL = 'SSL双向认证',
    SASL_PLAIN = 'SASL/PLAIN 身份认证',
}

export enum IAuthenticationModeValue {
    SASL_SCRAM = 'SASL_SCRAM',
    NONE = 'NONE',
    SSL = 'SSL',
    SASL_PLAIN = 'SASL_PLAIN',
}

export enum storagePolicyType {
    AUTO_DELETE = 'AUTO_DELETE',
    AUTO_EXPAND = 'AUTO_EXPAND',
    DYNAMIC_RETENTION = 'DYNAMIC_RETENTION',
}

export enum VersionTypeEnum {
    TRIAL = '（试用）',
    ACTIVE = '',
    RECOMMENDED = '（推荐）'
}

export enum EntityType {
    USER = 'user',
    CLINET = 'clientId',
    USER_CLIENT = 'user-clientId'
}
