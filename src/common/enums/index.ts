/* eslint-disable max-len */
/**
 * @file 枚举
 * <AUTHOR>
 */

import {Enum} from '@baiducloud/runtime';

import {
    ClusterStatusType,
    NodeClientStatusType,
    ClusterTaskStatusType,
    ClusterTaskType,
    PaymentType,
    PaymentTime,
    OperationType,
    AuthorityResourceType,
    MessageQueryType,
    ConfigStatusType,
    storagePolicyType,
    EntityType,
    taskType
} from '@/common/enums/constant';

// 默认所有
export const AllEnum = new Enum(
    {alias: 'ALL', text: '全部', value: ''}
);

// 配置策略
export const Certificate = new Enum(
    {alias: 'Administrator', text: '管理员', value: 'administrator', action: ['admin', 'publish', 'subscribe']},
    {alias: 'Write', text: '只读', value: 'write', action: ['subscribe']},
    {alias: 'Read', text: '只写', value: 'read', action: ['publish']},
    {alias: 'ReadWrite', text: '读写', value: 'readwirte', action: ['publish', 'subscribe']}
);

// 策略权限
export const CertificateAction = new Enum(
    {alias: 'admin', text: '管理', value: 'admin'},
    {alias: 'publish', text: '发布', value: 'publish'},
    {alias: 'subscribe', text: '订阅', value: 'subscribe'}
);

// 主题搜索配置
export const TopicSearch = new Enum(
    {alias: 'name', text: '主题名称', value: 'TopicName'}
);

// 主题状态配置
export const TopicStatus = new Enum(
    {alias: 'Deleting', text: '删除中', value: 'Deleting', klass: 'warning'},
    {alias: 'Normal', text: '正常', value: 'Normal', klass: 'normal'},
    {alias: 'Notify', text: '通知监听服务中', value: 'Notify', klass: 'warning'},
    {alias: 'Executing', text: '执行中', value: 'Executing', klass: 'warning'}
);

// 证书权限类型
export const CertificateType = new Enum(
    {alias: 'privilege', text: '特权证书', value: 'privilege'},
    {alias: 'ordinary', text: '普通证书', value: 'ordinary'}
);

// 主题搜索配置
export const ConsumerGroupSearch = new Enum(
    {alias: 'name', text: '分组名称', value: 'groupName'}
);

// 集群状态配置，注意前后顺序
export const ClusterStatus = new Enum(
    {alias: ClusterStatusType.NEW, text: '新建', value: ClusterStatusType.NEW, klass: 'rolling'},
    {alias: ClusterStatusType.ACTIVE, text: '服务中', value: ClusterStatusType.ACTIVE, klass: 'normal'},
    {alias: ClusterStatusType.DEPLOYING, text: '正在部署', value: ClusterStatusType.DEPLOYING, klass: 'rolling'},
    {alias: ClusterStatusType.DEPLOY_FAILED, text: '部署失败', value: ClusterStatusType.DEPLOY_FAILED, klass: 'error'},
    {alias: ClusterStatusType.PRE_DEPLOY_ROLLBACKING, text: '待部署回滚', value: ClusterStatusType.PRE_DEPLOY_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.DEPLOY_ROLLBACKING, text: '部署回滚中', value: ClusterStatusType.DEPLOY_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.DEPLOY_ROLLBACK_FAILED, text: '部署回滚失败', value: ClusterStatusType.DEPLOY_ROLLBACK_FAILED, klass: 'error'},
    {alias: ClusterStatusType.PRE_REBOOTING, text: '待重启', value: ClusterStatusType.PRE_REBOOTING, klass: 'rolling'},
    {alias: ClusterStatusType.REBOOTING, text: '重启中', value: ClusterStatusType.REBOOTING, klass: 'rolling'},
    {alias: ClusterStatusType.PRE_REBOOT_ROLLBACKING, text: '待重启回滚', value: ClusterStatusType.PRE_REBOOT_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.REBOOT_ROLLBACKING, text: '重启回滚中', value: ClusterStatusType.REBOOT_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.REBOOT_ROLLBACK_FAILED, text: '重启回滚失败', value: ClusterStatusType.REBOOT_ROLLBACK_FAILED, klass: 'error'},
    {alias: ClusterStatusType.PRE_UPDATING, text: '待变更', value: ClusterStatusType.PRE_UPDATING, klass: 'rolling'},
    {alias: ClusterStatusType.UPDATING, text: '变更中', value: ClusterStatusType.UPDATING, klass: 'rolling'},
    {alias: ClusterStatusType.PRE_UPDATE_ROLLBACKING, text: '待变更回滚', value: ClusterStatusType.PRE_UPDATE_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.UPDATE_ROLLBACKING, text: '变更回滚中', value: ClusterStatusType.UPDATE_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.UPDATE_ROLLBACK_FAILED, text: '变更回滚失败', value: ClusterStatusType.UPDATE_ROLLBACK_FAILED, klass: 'error'},
    {alias: ClusterStatusType.PRE_SUSPENDING, text: '待停服', value: ClusterStatusType.PRE_SUSPENDING, klass: 'rolling'},
    {alias: ClusterStatusType.SUSPENDING, text: '停服中', value: ClusterStatusType.SUSPENDING, klass: 'rolling'},
    {alias: ClusterStatusType.SUSPENDED, text: '已停服', value: ClusterStatusType.SUSPENDED, klass: 'warning'},
    {alias: ClusterStatusType.PRE_SUSPEND_ROLLBACKING, text: '待停服回滚', value: ClusterStatusType.PRE_SUSPEND_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.SUSPEND_ROLLBACKING, text: '停服回滚中', value: ClusterStatusType.SUSPEND_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.SUSPEND_ROLLBACK_FAILED, text: '停服回滚失败', value: ClusterStatusType.SUSPEND_ROLLBACK_FAILED, klass: 'error'},
    {alias: ClusterStatusType.PRE_RELEASING, text: '待释放', value: ClusterStatusType.PRE_RELEASING, klass: 'rolling'},
    {alias: ClusterStatusType.PRE_RESUMING, text: '待恢复', value: ClusterStatusType.PRE_RESUMING, klass: 'rolling'},
    {alias: ClusterStatusType.RESUMING, text: '恢复中', value: ClusterStatusType.RESUMING, klass: 'rolling'},
    {alias: ClusterStatusType.PRE_RESUME_ROLLBACKING, text: '待恢复回滚', value: ClusterStatusType.PRE_RESUME_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.RESUME_ROLLBACKING, text: '恢复回滚中', value: ClusterStatusType.RESUME_ROLLBACKING, klass: 'rolling'},
    {alias: ClusterStatusType.RESUME_ROLLBACK_FAILED, text: '恢复回滚失败', value: ClusterStatusType.RESUME_ROLLBACK_FAILED, klass: 'error'},
);

export const ConfigStatus = new Enum(
    {alias: ConfigStatusType.USED, text: '使用中', value: ConfigStatusType.USED, klass: 'normal'},
    {alias: ConfigStatusType.UNUSED, text: '未使用', value: ConfigStatusType.UNUSED, klass: 'fail'},
);

// 任务状态配置
export const ClusterTaskStatus = new Enum(
    {alias: ClusterTaskStatusType.PREPARE, text: '预备', value: ClusterTaskStatusType.PREPARE, klass: 'prepare', taskKlass: 'prepare'},
    {alias: ClusterTaskStatusType.NEW, text: '待执行', value: ClusterTaskStatusType.NEW, klass: 'warning', taskKlass: 'warning'},
    {alias: ClusterTaskStatusType.PENDING, text: '执行中', value: ClusterTaskStatusType.PENDING, klass: 'waiting', taskKlass: 'normal'},
    {alias: ClusterTaskStatusType.RUNNING, text: '执行中', value: ClusterTaskStatusType.RUNNING, klass: 'waiting', taskKlass: 'normal'},
    {alias: ClusterTaskStatusType.FINISHED, text: '成功', value: ClusterTaskStatusType.FINISHED, klass: 'normal', taskKlass: 'success'},
    {alias: ClusterTaskStatusType.FAILED, text: '失败', value: ClusterTaskStatusType.FAILED, klass: 'error', taskKlass: 'error'},
    {alias: ClusterTaskStatusType.SUSPENDED, text: '暂停', value: ClusterTaskStatusType.SUSPENDED, klass: 'fail', taskKlass: 'fail'},
    {alias: ClusterTaskStatusType.CANCELED, text: '取消', value: ClusterTaskStatusType.CANCELED, klass: 'fail', taskKlass: 'fail'},
);

// 任务类型
export const ClusterTask = new Enum(
    {alias: '', text: '全部类型', value: ''},
    {alias: ClusterTaskType.INCREASE_BROKER_COUNT, text: '增加节点数量', value: ClusterTaskType.INCREASE_BROKER_COUNT},
    {alias: ClusterTaskType.DECREASE_BROKER_COUNT, text: '减少节点数量', value: ClusterTaskType.DECREASE_BROKER_COUNT},
    {alias: ClusterTaskType.UPGRADE_BROKER_NODE_TYPE, text: '升级节点规格', value: ClusterTaskType.UPGRADE_BROKER_NODE_TYPE},
    {alias: ClusterTaskType.DOWNGRADE_BROKER_NODE_TYPE, text: '降低节点规格', value: ClusterTaskType.DOWNGRADE_BROKER_NODE_TYPE},
    {alias: ClusterTaskType.UPDATE_BROKER_NODE_TYPE, text: '变更节点规格', value: ClusterTaskType.UPDATE_BROKER_NODE_TYPE},
    {alias: ClusterTaskType.EXPAND_BROKER_DISK_CAPACITY, text: '扩容节点磁盘', value: ClusterTaskType.EXPAND_BROKER_DISK_CAPACITY},
    {alias: ClusterTaskType.REASSIGN_PARTITION, text: '主题重新分区', value: ClusterTaskType.REASSIGN_PARTITION},
    {alias: ClusterTaskType.UPDATE_ACCESS_CONFIG, text: '变更集群访问', value: ClusterTaskType.UPDATE_ACCESS_CONFIG},
    {alias: ClusterTaskType.RESTART_BROKER, text: '重启集群节点', value: ClusterTaskType.RESTART_BROKER},
    {alias: ClusterTaskType.UPDATE_KAFKA_CONFIG, text: '更新集群配置', value: ClusterTaskType.UPDATE_KAFKA_CONFIG},
    {alias: ClusterTaskType.UPGRADE_KAFKA_VERSION, text: '升级集群版本', value: ClusterTaskType.UPGRADE_KAFKA_VERSION},
    {alias: ClusterTaskType.RESUME_CLUSTER, text: '启动集群服务', value: ClusterTaskType.RESUME_CLUSTER},
    {alias: ClusterTaskType.SUSPEND_CLUSTER, text: '停止集群服务', value: ClusterTaskType.SUSPEND_CLUSTER},
    {alias: ClusterTaskType.RESIZE_PUBLIC_IP_BANDWIDTH, text: '变更公网带宽', value: ClusterTaskType.RESIZE_PUBLIC_IP_BANDWIDTH},
    {alias: ClusterTaskType.ENABLE_PUBLIC_IP, text: '开启公网', value: ClusterTaskType.ENABLE_PUBLIC_IP},
    {alias: ClusterTaskType.DISABLE_PUBLIC_IP, text: '关闭公网', value: ClusterTaskType.DISABLE_PUBLIC_IP},
    {alias: ClusterTaskType.UPDATE_STORAGE_POLICY, text: '变更容量策略', value: ClusterTaskType.UPDATE_STORAGE_POLICY},
    {alias: ClusterTaskType.UPDATE_CLUSTER_SECURITY_GROUP, text: '更新集群安全组', value: ClusterTaskType.UPDATE_CLUSTER_SECURITY_GROUP},
    {alias: ClusterTaskType.UPDATE_KAFKA_TOPIC_CONFIG, text: '更新主题配置', value: ClusterTaskType.UPDATE_KAFKA_TOPIC_CONFIG},
    {alias: ClusterTaskType.ENABLE_INTRANET_IP, text: '开启产品间转储', value: ClusterTaskType.ENABLE_INTRANET_IP},
    {alias: ClusterTaskType.DISABLE_INTRANET_IP, text: '关闭产品间转储', value: ClusterTaskType.DISABLE_INTRANET_IP},
    {alias: ClusterTaskType.RESTART_CLUSTER, text: '重启集群', value: ClusterTaskType.RESTART_CLUSTER},
    {alias: ClusterTaskType.UPDATE_CLUSTER, text: taskType.UPDATE_CLUSTER, value: ClusterTaskType.UPDATE_CLUSTER},
);

// 节点状态
export const ClusterNodeClientStatus = new Enum(
    {alias: NodeClientStatusType.NEW, text: '等待部署', value: NodeClientStatusType.NEW, klass: 'rolling'},
    {alias: NodeClientStatusType.ALIVE, text: '服务中', value: NodeClientStatusType.ALIVE, klass: 'normal'},
    {alias: NodeClientStatusType.DEAD, text: '异常', value: NodeClientStatusType.DEAD, klass: 'error'},
    {alias: NodeClientStatusType.LOST, text: '丢失', value: NodeClientStatusType.LOST, klass: 'error'}
);

// 付费类型数组
export const Payments = new Enum(
    {alias: PaymentType.PREPAID, text: '预付费', value: PaymentType.PREPAID},
    {alias: PaymentType.POSTPAID, text: '后付费', value: PaymentType.POSTPAID}
);

// 预付费时长数组
export const Timechoices = new Enum(
    {alias: '1个月', text: '1个月', value: PaymentTime.oneMon},
    {alias: '2', text: '2', value: PaymentTime.twoMon},
    {alias: '3', text: '3', value: PaymentTime.thrMon},
    {alias: '4', text: '4', value: PaymentTime.fourMon},
    {alias: '5', text: '5', value: PaymentTime.fiveMon},
    {alias: '6', text: '6', value: PaymentTime.sixMon},
    {alias: '7', text: '7', value: PaymentTime.sevMon},
    {alias: '8', text: '8', value: PaymentTime.eightMon},
    {alias: '9', text: '9', value: PaymentTime.nineMon},
    {alias: '1年', text: '1年', value: PaymentTime.oneYear},
    {alias: '2年', text: '2年', value: PaymentTime.twoYear},
    {alias: '3年', text: '3年', value: PaymentTime.thrYear},
);

// 自动付费按月时间选择框
export const autoLengthOfMon = new Enum(
    {alias: '1', text: '1', value: 1},
    {alias: '2', text: '2', value: 2},
    {alias: '3', text: '3', value: 3},
    {alias: '4', text: '4', value: 4},
    {alias: '5', text: '5', value: 5},
    {alias: '6', text: '6', value: 6},
    {alias: '7', text: '7', value: 7},
    {alias: '8', text: '8', value: 8},
    {alias: '9', text: '9', value: 9}
);

// 自动付费按年时间选择框
export const autoLengthOfYear = new Enum(
    {alias: '1', text: '1', value: 1},
    {alias: '2', text: '2', value: 2},
    {alias: '3', text: '3', value: 3}
);

// 监控页面时间选择器选项
// 时间选取
export const TIME_LIST = new Enum(
    {alias: '1H', text: '近1小时', value: '1h'},
    {alias: '3H', text: '近3小时', value: '3h'},
    {alias: '6H', text: '近6小时', value: '6h'},
    {alias: '1D', text: '近1天', value: '1d'},
    {alias: '3D', text: '近3天', value: '3d'},
    {alias: '7D', text: '近7天', value: '7d'},
    {alias: '14D', text: '近14天', value: '14d'},
    {alias: '30D', text: '近30天', value: '30d'},
    {alias: 'selfConfig', text: '自定义', value: 'selfConfig'}
);

// 节点配置
export const ClusterDefaultConf = new Enum(
    {alias: 'default', text: '默认配置', value: 'default'},
    {alias: 'selfDefine', text: '自定义配置', value: 'selfDefine'},
);

export const ClusterAuthorityPatternType = new Enum(
    {alias: 'LITERAL', text: '精确匹配', value: 'LITERAL'},
    {alias: 'PREFIXED', text: '前缀匹配', value: 'PREFIXED'}
);

export const ClusterAuthorityResourceType = new Enum(
    {alias: AuthorityResourceType.TOPIC, text: '主题', value: AuthorityResourceType.TOPIC},
    {alias: AuthorityResourceType.GROUP, text: '消费组', value: AuthorityResourceType.GROUP},
    {alias: AuthorityResourceType.CLUSTER, text: '集群', value: AuthorityResourceType.CLUSTER},
    {alias: AuthorityResourceType.TRANSACTIONAL_ID, text: '事务ID', value: AuthorityResourceType.TRANSACTIONAL_ID},
);

export const ClusterAuthorityOptionType = new Enum(
    {alias: OperationType.CONSUME, text: '订阅', value: OperationType.CONSUME},
    {alias: OperationType.PRODUCE, text: '发布', value: OperationType.PRODUCE},
    {alias: OperationType.IDEMPOTENT_WRITE, text: '幂等写', value: OperationType.IDEMPOTENT_WRITE},
    {alias: OperationType.WRITE, text: '写', value: OperationType.WRITE},
);

// 查询类型数组
export const QueryType = new Enum(
    {alias: MessageQueryType.byTime, text: '根据时间查询', value: MessageQueryType.byTime},
    {alias: MessageQueryType.byOffset, text: '根据位点查询', value: MessageQueryType.byOffset}
);

export const diskNums4 = new Enum(
    {value: 1, text: '1', alias: '1'},
    {value: 2, text: '2', alias: '2'},
    {value: 4, text: '4', alias: '4'},
);

export const diskNums2 = new Enum(
    {value: 1, text: '1', alias: '1'},
    {value: 2, text: '2', alias: '2'}
);

export const diskNums8 = new Enum(
    {value: 1, text: '1', alias: '1'},
    {value: 2, text: '2', alias: '2'},
    {value: 4, text: '4', alias: '4'},
    {value: 8, text: '8', alias: '8'}
);

export const storagePolicyEnum = new Enum(
    {alias: storagePolicyType.AUTO_DELETE, text: '自动删除', value: storagePolicyType.AUTO_DELETE},
    {alias: storagePolicyType.DYNAMIC_RETENTION, text: '动态调整', value: storagePolicyType.DYNAMIC_RETENTION},
    {alias: storagePolicyType.AUTO_EXPAND, text: '自动扩容', value: storagePolicyType.AUTO_EXPAND},
);

export const routeTypeEnum = new Enum(
    {alias: '标准型BGP', text: '标准型BGP', value: 'BGP'},
    {alias: '增强型BGP', text: '增强型BGP', value: 'BGP_S'},
    {alias: '电信单线', text: '电信单线', value: 'ChinaTelcom'},
    {alias: '联通单线', text: '联通单线', value: 'ChinaUnicom'},
    {alias: '移动单线', text: '移动单线', value: 'ChinaMobile'},
    {alias: '回源IP', text: '回源IP', value: 'BackToOrigin'},
    {alias: '定制线路', text: '定制线路', value: 'Custom'},
);

export const ClusterEntityType = new Enum(
    {alias: EntityType.USER, text: '用户', value: EntityType.USER},
    {alias: EntityType.CLINET, text: '客户端ID', value: EntityType.CLINET},
    {alias: EntityType.USER_CLIENT, text: '用户-客户端ID', value: EntityType.USER_CLIENT},
);



