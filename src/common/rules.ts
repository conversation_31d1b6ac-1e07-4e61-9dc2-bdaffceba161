/**
 * @file 规则
 * <AUTHOR>
 */
import _ from 'lodash';
import {ClusterStatusType, PaymentType, ConfigStatusType} from '@/common/enums/constant';
import {ClusterItem} from '../pages/cluster/list';

export const TopicNameRegTip = '大小写字母、数字以及_.-特殊字符，长度1-125';
export const RULES = {
    // 大小写字母、数字以及_.-特殊字符，长度1-125
    topicName: /^[\w-\.]{1,125}$/,
    consumerName: /^[\w-\.]{1,125}$/,
    // 大小写字母、数字以及_-特殊字符，长度1-125
    clusterTopicName: /^[\w-]{1,125}$/
};

export const VAILDITE_ITEMS = {
    requiredInput: {required: true, message: '请输入'},
    requiredSelect: {required: true, message: '请选择'},
    requiredCheckGroup: {required: true, message: '请选择至少一项'}
};

export const FORBID_HANDLER = {
    checkClusterDelete(value: {status: string, payment: string}) {
        const postCanDel = _.includes([
            ClusterStatusType.ACTIVE,
            ClusterStatusType.DEPLOY_FAILED
        ], value.status) && (value.payment === PaymentType.POSTPAID);
        const preCanDel = _.includes([
            ClusterStatusType.DEPLOY_FAILED
        ], value.status) && (value.payment === PaymentType.PREPAID);
        return postCanDel || preCanDel;
    },

    checkConfigDelete(value: {state: string}) {
        return value.state !== ConfigStatusType.USED;
    },
    checkClusterUpgrage(status: string) {
        return _.includes([ClusterStatusType.ACTIVE], status);
    },
    forbidByUpgrage(status: string) {
        return status !== ClusterStatusType.ACTIVE;
    },
    disableRestart(status: ClusterStatusType) {
        return status !== ClusterStatusType.ACTIVE;
    },

    checkPaymentUpgrage(cluster: ClusterItem) {
        return cluster.payment === PaymentType.POSTPAID && _.includes(['ACTIVE'], cluster.status);
    }
};

export const TOPIC_RESPLIT_VALIDATE = {
    batchSize: [{required: true, message: '请输入'}],
    interBrokerThrottle: [{required: true, message: '请输入'}],
    startTime: [{required: true, message: '请选择时间'}]
};

export const LIST_OPERATION_SETTING = {
    showStart(status: string) {
        return _.includes([ClusterStatusType.PRE_SUSPENDING,
            ClusterStatusType.SUSPENDED,
            ClusterStatusType.SUSPENDING,
            ClusterStatusType.SUSPEND_ROLLBACKING,
            ClusterStatusType.SUSPEND_ROLLBACK_FAILED], status);
    }
};
