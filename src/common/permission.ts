/**
 * @file 权限
 * <AUTHOR>
 */

import _ from 'lodash';
import {TopicStatus, ClusterStatus} from './enums';

import {FORBID_HANDLER} from './rules';

export default {
    TOPIC: {
        canCreate() {
            return true;
        },
        canRemove(status: string) {
            return status === TopicStatus.Normal;
        },
        canAadPartition(status: string) {
            return status === TopicStatus.Normal;
        },
        canViewCert(status: string) {
            return status === TopicStatus.Normal;
        }
    },
    CONSUMERGROUP: {
        canResetSite(topicCount: number) {
            return topicCount > 0;
        }
    },
    CLUSTER: {
        TOPIC: {
            canCreate(status: string) {
                return status === ClusterStatus.ACTIVE;
            },
            disabledDelete: FORBID_HANDLER.forbidByUpgrage,
            disabledEditQuote: FORBID_HANDLER.forbidByUpgrage,
            disabledEditHigherConf: FORBID_HANDLER.forbidByUpgrage,
            disabledEditUser: FORBID_HANDLER.forbidByUpgrage,
            disabledAuthorityCreate: FORBID_HANDLER.forbidByUpgrage,
            disabledAuthorityDelete: FORBID_HANDLER.forbidByUpgrage,
            disabledResplit(status: string) {
                return status !== ClusterStatus.ACTIVE;
            },
        },
        CONSUMERGROUP: {
            disabledReset: FORBID_HANDLER.forbidByUpgrage,
            disabledDelete: FORBID_HANDLER.forbidByUpgrage,
        },
        AUTHORITY: {
            canCreate(status: string) {
                return status === ClusterStatus.ACTIVE;
            },
            disabledDelete: FORBID_HANDLER.forbidByUpgrage,
        },
        USER: {
            canCreate(status: string) {
                return status === ClusterStatus.ACTIVE;
            },
            disabledReset: FORBID_HANDLER.forbidByUpgrage,
            disabledDelete: FORBID_HANDLER.forbidByUpgrage,
        }
    }
};
