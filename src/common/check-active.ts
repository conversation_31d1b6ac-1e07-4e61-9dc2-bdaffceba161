/**
 * 检测是否激活
 *
 * @file check.ts
 * <AUTHOR>
 */
import api from '@/common/client';
import {kafkaRoleInfo} from '@/common/config';
import {router} from 'san-router';

const checkActive = async () => {
    // 如果是激活页，直接打开
    if (window.location.hash === '#/kafka/cluster/activate') {
        return;
    }
    // 专享版页面开通校验
    if (/#\/kafka\/cluster\//.test(window.location.href) || /#\/kafka\/config\//.test(window.location.href)) {
        const data = await api.kafkaRoleQuery({
            roleName: kafkaRoleInfo.roleName
        });
        window.$storage.set('kafkaRoleQuery', !!(data && data.id));
        if (!window.$storage.get('kafkaRoleQuery')) {
            router.locator.redirect('/kafka/cluster/activate');
            return;
        }
    }
    // 共享版页面判断是否白名单用户，不是的话跳转专享版集群列表页
    else {
        const featureTypes = ['ShareKafkaAccessToUse'];
        const {isExist: isWhiteUser} = await api.getUserAcls({featureTypes});
        if (!isWhiteUser) {
            router.locator.redirect('/kafka/cluster/list');
            return;
        }
        else {
            const data = await api.bpsOrderCreateCheck({});
            // 校验是否已开通，没开通，跳到激活页
            if (data && !data.checked) {
                router.locator.redirect('/kafka/order/create');
                return;
            }
            // 已开通
            // 如果是激活页，跳到 /kafka/cluster/list集群列表页
            // 如果非激活页，不做变化
            else if (/#\/kafka\/order\/create/.test(window.location.href)) {
                router.locator.redirect('/kafka/cluster/list');
                return;
            }
        }
    }
};

export default checkActive;
