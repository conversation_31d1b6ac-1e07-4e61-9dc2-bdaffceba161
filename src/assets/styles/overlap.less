/**
 * 一些样式重叠（针对sui、sui-biz等样式覆盖）
 *
 * @file overlap.less
 * <AUTHOR>
 */

/* 全局页面样式调整 */
.app-view .biz-app {
    overflow: auto;
}

/* bcm-sdk 弹框样式优化 */
.bui-dialog-body {
    .bcm-chart-panel {
        width: 100%;

        .filter {
            margin-bottom: 20px;
        }
    }
}

/* mfa 短信认证模块 */
.bui-mfacheck {
    .bui-dialog-body {
        .bui-mfacheck-row:first-child {
            margin-top: 10px;
        }

        .bui-smscodebox {
            .bui-button {
                margin-left: 15px;
                height: 30px;
            }
        }
    }

    .bui-dialog-foot {
        .bui-button {
            float: right;
            margin-right: 0;
            margin-left: 20px;

            &:not(:last-child) {
                margin-right: 0;
            }
        }
    }
}

/* sui checkbox 样式覆盖 */
.s-checkbox {
    // display: flex;
    // align-items: center;

    .s-checkbox-input {
        margin-right: 2px;
        min-width: 14px;
    }
}

/* sui select 多选 checkbox 下拉样式覆盖(垂直居中) */
.s-selectdropdown {
    &.is-multiple {
        .s-select-check-all {
            display: flex;
        }
    }
}

/* sui checkbox 在 table 下样式约束 */
.s-table {
    .s-checkbox {
        display: flex;
    }
    .s-table-cell-text {
        word-break: break-all;
    }
}

/* sui dialog 样式约束 */
.s-dialog.s-dialog-instance-custom {
    > .s-dialog-wrapper {
        .s-dialog-content {
            padding: 24px;
        }
    }
}

/* sui formitem 必选样式约束 */
.s-form-item {
    .s-form-item-label-required {
        label {
            position: relative;

            &::before {
                position: absolute;
                left: 0px;
            }
        }
    }
}

/* sui-biz searchbox 样式约束 */
.s-search-box {
    height: 32px;

    .s-cascader {
        display: inline-block;
        vertical-align: top;

        &-value {
            min-height: 30px;

            .s-cascader-multiple-items {
                width: auto;
            }
        }
    }
}

/* sui-biz ClipBoard 样式优化 */
.clipboard-default {
    fill: var(--defaultColor);
}

/* sui notification 弹框限制 */
.s-notification-container {
    top: 70px !important;
}

.status {
    &:before {
        margin: 2px 6px 2px 2px;
    }

    &.error:before {
        color: var(--errorColor);
    }

    &.normal:before {
        color: var(--successColor);
    }

    &.warning:before {
        color: var(--warningColor);
    }

    &.process:before {
        color: var(--defaultColor);
    }

    &.fail:before {
        color: var(--failColor);
    }
    &.prepare:before {
        color: #fad000;
    }
}

/* 集群监控方法弹窗左右内边距 */
.bcm-detail-main {
    .bcm-detail-main-func,
    .bcm-detail-main-chart,
    .bcm-detail-main-table {
        padding-left: 24px;
        padding-right: 24px;
    }

}

.app-sidebar {
    border: none;
}
