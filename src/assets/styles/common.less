/**
 * @file common.less
 * <AUTHOR>
 */
@tabSelectBgColor: #e6f0ff;
@tabBorderColor: rgba(242, 242, 244, 1);
@titleColor: #151b26;

/* 表格按钮 */
.table-btn-slim {
    padding: 0 10px 0 0 !important;

    &:first-child {
        padding-left: 2px !important;
    }
}

.s-table-filter-menu {
    max-height: 300px;
    overflow-y: auto;
}

/* 表单行水平居中 */
.form-item-center {
    line-height: 30px;
}

/* 单行省略 */
.ellipsis-single {
    display: inline-block;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}

/* 表格固定label长度 */
.form-format {
    .s-form-item {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }

        &-label {
            text-align: left;
        }

        .s-radio-button-group {
            margin-right: 0;
        }

        .s-transfer-wrapper-left {
            margin: 0px;
        }
    }
}

/* 描述 */
.desc {
    color: var(--descColor);
    font-size: 12px;
    line-height: 20px;
    font-weight: 400;
}

.inline-desc {
    margin-left: 12px;
    color: var(--descColor3)!important;

    &:hover {
        color: #528eff;
    }
    &:active {
        color: #144bcc;
    }
}

/* 飘红 */
.err {
    color: var(--formErrColor);
}

/* 公用列表 */
.bms-list-page {
    padding-bottom: 10px;

    > .s-list-page {
        padding: 0;
        height: 100%;

        > .s-list-content {
            height: 100%;
            display: flex;
            flex-direction: column;

            > .table-full-wrap {
                flex: 1;

                > .operation-wrap {
                    height: auto;
                }
            }
        }
    }

    .searchbox {
        input {
            padding: 0 5px 0 10px;
        }
    }

    .title {
        font-weight: 500;
        font-size: 14px;
        color: var(--blackColor);
    }
}

/* 公用详情页 */
.bms-detail-page {
    .s-detail-page-title {
        height: 48px;
        margin: 0;
        padding: 0 16px;
        border: 0;
    }

    .s-detail-page-content {
        margin: 15px;
        padding: 0;
    }

    &__content {
        width: 100%;
        height: 100%;
    }

    .bms-detail-page__wrap {
        padding: 24px;
    }
}

/* 公用详情tab切换 */
.bms-tab-page {
    padding: 0 !important;
    border-radius: 6px;

    > .s-tabs {
        border: 0 !important;

        > .s-tabnav {
            flex: 0 0 160px;
            width: 160px;
            margin: 0 !important;

            > .s-tabnav-scroll {
                width: 100%;
                position: relative;

                &::after {
                    position: absolute;
                    content: "";
                    top: 0;
                    right: 0;
                    height: 100%;
                    width: 1px;
                    background: @tabBorderColor;
                }

                > .s-tabnav-nav {
                    border: 0 !important;
                    padding-top: 15px;

                    .s-tabnav-nav-item {
                        margin-bottom: 0;
                        padding: 0 16px;
                        width: 160px;
                        height: 40px;
                        line-height: 40px;
                        text-align: left !important;
                        font-size: 14px;

                        &::after {
                            display: none;
                            width: 4px;
                        }
                    }

                    .s-tabnav-nav-selected {
                        background-color: @tabSelectBgColor !important;
                    }
                }
            }
        }

        > .s-tabpane-wrapper {
            width: 100%;
            height: 100%;

            .s-tab-page-panel {
                height: 100%;

                .s-tabpane {
                    height: 100%;
                    padding: 0;

                    .s-list-page {
                        padding: 0;
                    }

                    .bms-tab-info {
                        padding: 24px;

                        .bms-infotile:first-child {
                            .s-legend {
                                label {
                                    line-height: unset;
                                }
                            }
                        }
                    }

                    .bms-list-page {
                        height: 100%;
                        padding: 0;

                        .s-list-page {
                            height: 100%;
                            background-color: var(--whiteColor);

                            .s-list-content {
                                height: 100%;
                                padding: 24px;

                                .title {
                                    border: 0;
                                    text-indent: 0;
                                    height: auto;
                                    line-height: unset;
                                    padding-bottom: 16px;
                                    font-size: 16px;
                                    font-weight: 500;
                                }

                                .table-full-wrap {
                                    height: 100%;
                                    margin: 0;
                                    padding: 0;
                                    border: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/* 公用创建页模块 */
.bms-form-panel {
    margin: 16px;
    padding: 24px;
    background: var(--whiteColor);
    border-radius: 6px;

    .s-form {
        .s-form-item {
            &-label {
                width: 116px;
                text-align: left;
            }

            &:last-child {
                margin-bottom: 0;
            }

            .s-radio-button-group {
                margin-right: 0;
            }
        }
    }

    &:first-child {
        margin-top: 0;
    }
}

/* sui select组件对 tooltip 组件样式约束 */
.select-tooltip {
    width: auto;
    font-size: 0;

    .s-trigger-container {
        width: auto;
        display: block;
    }
    .s-tooltip-body {
        font-size: 14px;
    }
}

/* sui dialog 样式优化 宽度为800px情况下 dialog宽度和input宽度适应 */
.ue-dialog {
    .s-dialog-wrapper {
        min-width: 400px;
        .s-dialog-header {
            > h3 {
                font-weight: 500;
                font-size: 16px;
            }
        }
        .s-dialog-content {
            padding: 24px;
            .s-form-item-label {
                text-align: left;
            }
        }
    }
}

/* sui dialog 样式优化 宽度为520px情况下 dialog宽度和input宽度适应 */
.ue-dialog-shot {
    .s-dialog-wrapper {
        width: 520px;
        .s-dialog-header {
            > h3 {
                font-weight: 500;
                font-size: 16px;
            }
        }
        .s-dialog-content {
            min-width: 520px !important;
            padding: 24px;
        }
    }
}

/* 标签按钮样式 */
.a-btn {
    color: var(--defaultColor);

    &:visited,
    &:hover,
    &:active {
        color: var(--defaultColor);
    }
}

/* dialog 提示弹窗优化 */
.s-dialog {
    &.s-dialog-instance-custom {
        & > .s-dialog-wrapper {
            width: 520px;

            .s-dialog-header-icon {
                font-size: 16px;
                color: @titleColor;
                font-weight: 500;
            }

            .s-dialog-content {
                & > .s-dialog-text {
                    &.show-icon {
                        margin: 0 32px;
                    }
                }
            }
        }
    }
}

.s-transfer .s-transfer-wrapper-left .s-transfer-content .s-transfer-content-wrapper .s-transfer-content-item .check-box {
    width: 20px;
}

.switch-with-tip {
    line-height: 30px;
}

.radio-with-tip {
    display: flex;
    align-items: flex-start;
    .s-radio-button-group {
        margin-top: 3px;
    }
}

.yellow-text {
    color: #FFAB52;
}
