.bms-config-detail {
    .s-detail-page-content {
        background-color: transparent;
        .detail-ceil-card {
            padding: 24px;
            background-color: #fff;

            .version-list-operation {
                display: flex;
                justify-content: space-between;
            }

            .latest-tag {
                margin-left: 16px;
                background: #EEF3FE;
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 12px;
                color: #2468F2;
                line-height: 20px;
                font-weight: 400;
            }
        }
    }
}

.config-revision-detail {
    width: 850px;
    &__bottom {
        position: absolute;
        right: 40px;
        bottom: 20px;
    }
    .cluster-table {
        overflow-x: auto;
    }
    .title {
        font-size: 14px;
        color: #151B26;
        font-weight: 500;
        display: inline-block;
    }
}

.cluster-search-box {
    .s-cascader-column {
        max-height: 300px;
        overflow-y: scroll;
    }

    .s-cascader-multiple-icon {
        width: 16px;
        height: 16px;
    }
}

.version-diff {
    .s-dialog-content {
        display: flex;
        flex-direction: column;

        .version-diff-title {
            display: flex;

            .version-title {
                width: 50%;
                padding-left: 24px;
                .latest-tag {
                    margin-right: 16px;
                    background: #EEF3FE;
                    border-radius: 4px;
                    padding: 4px 6px;
                    font-size: 12px;
                    color: #2468F2;
                    line-height: 20px;
                    font-weight: 400;
                    line-height: 30px;
                }
                .s-input {
                    border: 0;
                }
            }
        }

        .panel-content {
            display: flex;
            border: 1px solid rgba(232,233,235,1);
            border-radius: 4px;
            max-height: 400px;
            overflow: auto;
            position: relative;

            .diff-panel {
                height: 100%;
                padding: 24px;
                &:first-child {
                    border-right: 0;
                }
                &:last-child {
                    border-left: 0;
                }

                .add-line {
                    .s-table-cell {
                        background-color: #d1f2c7;
                    }
                }

                .delete-line {
                    .s-table-cell {
                        background-color: #ffdbd9;
                    }
                }

                .alter-line {
                    .s-table-cell {
                        background-color: #ffecd4;
                    }
                }
            }
            .split-line {
                width: 0;
                position: sticky;
                top: 0;
                bottom: 0;
                left: 50%;
                border-left: 1px solid rgba(232,233,235,1);
            }
        }
    }
}
