/**
 * 配置详情
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {AppDetailPage, AppLegend, AppDetailCell} from '@baidu/sui-biz';
import {Button, Loading, Table, Search, Pagination} from '@baidu/sui';
import DetailNav from '@/components/nav/detail';
import {ConfigStatus, AllEnum} from '@/common/enums';
import {renderStatus} from '@/common/html';
import CreateBtn from '@/components/create-btn';
import EllipsisTip from '@/components/ellipsis-tip';
import api from '@/common/client';
import {ConfigStatusType} from '@/common/enums/constant';
import {formatTime, pickEmpty} from '@/common/util';
import {ROUTE_PATH, PAGER_SUI} from '@/common/config';
import VersionDetail from './version-detail';
import VersionDiff from './components/version-diff';
import './index.less';
const klass = 'bms-config-detail';
const allEnum = AllEnum.toArray();

export interface ConfigDetail {
    configId: string;
    name: string;
    createTime: string;
    state: string;
    description: string;
};

export interface RevisionItem {
    revisionId: string;
    createTime: string;
    state: string;
    description: string;
}

const TEXT_MAP = {
    configId: '配置ID：',
    createTime: '创建时间：',
    description: '配置描述：',
    name: '配置名称：',
    state: '状态：',
};
@decorators.asPage(ROUTE_PATH.configDetail)
export default class extends AppDetailPage {

    pageTitle = '配置详情';

    static template = html`
    <div class="${klass}">
        <s-detail-page class="bms-detail-page">
            <nav
                slot="pageTitle"
                title="{{detail.name}}"
                options="{{options}}"
                back="#${ROUTE_PATH.configList}"
            >
                <div slot="right">
                    <s-button
                        width="46"
                        loading="{{isLoading}}"
                        on-click="onRefresh">
                        刷新
                    </s-button>
                </div>
            </nav>
            <s-loading class="detail-loading" loading="{{loading}}" size="large">
                <div class="detail-ceil-card">
                    <s-legend label="基本信息" noHighlight/>
                    <s-detail-cell datasource="{{datasource}}" divide="3" labelWidth="60px">
                    </s-detail-cell>
                </div>
                <div class="detail-ceil-card mt16">
                    <s-legend label="版本信息" noHighlight/>
                    <div class="version-list-operation mb16">
                        <create-btn text="新增版本" on-click="onCreate" />
                    </div>
                    <s-table
                        columns="{{columns}}"
                        datasource="{{tablesource}}"
                        loading="{{loading}}"
                        on-sort="onSort"
                        on-filter="onFilter"
                    >
                        <div slot="c-revisionId" style="cursor: pointer">
                            <a class="a-btn" on-click="viewRevisionDetail(row.revisionId)">
                                {{row.revisionId}}
                            </a>
                            <span s-if="{{rowIndex === 0 && pager.page === 1}}" class="latest-tag">最新版本</span>
                        </div>
                        <ellipsis-tip slot="c-description" text="{{row.description || '-'}}" placement="top" />
                        <div slot="c-state">{{row.state | filterStatus | raw}}</div>
                        <div slot="c-operation">
                            <s-button
                                skin="stringfy"
                                class="table-btn-slim"
                                on-click="onDiff(row)">
                                版本对比
                            </s-button>
                        </div>
                    </s-table>
                    <div style="display: flex;justify-content: flex-end;margin-top: 16px">
                        <s-pagination
                            s-if="{{total > 10}}"
                            layout="{{'total, pageSize, pager, go'}}"
                            total="{{total}}"
                            pageSize="{{pager.pageSize}}"
                            page="{{pager.page}}"
                            max-item="{{7}}"
                            on-pagerChange="onPageChange"
                            on-pagerSizeChange="onPageSizeChange"
                        />
                    </div>
                </div>
            </s-loading>
        </s-detail-page>
    </div>`;

    static components = {
        's-detail-page': AppDetailPage,
        'nav': DetailNav,
        's-button': Button,
        's-loading': Loading,
        's-table': Table,
        's-detail-cell': AppDetailCell,
        's-legend': AppLegend,
        'create-btn': CreateBtn,
        's-searchbox': Search,
        'ellipsis-tip': EllipsisTip,
        's-pagination': Pagination,
    };

    static filters: SanFilterProps = {
        filterStatus(status: ConfigStatusType) {
            return renderStatus(ConfigStatus.fromValue(status));
        },
    };

    initData() {
        return {
            pager: {...PAGER_SUI},
            isLoading: false,
            loading: true,
            options: {},
            datasource: [],
            tablesource: [],
            placeholder: '请输入关键字搜索',
            keyword: '',
            columns: [
                {name: 'revisionId', label: '版本号'},
                {name: 'state', label: '状态', filter: {
                    options: [
                        ...allEnum,
                        ...ConfigStatus.toArray()
                    ],
                    value: allEnum[0].value
                }},
                {name: 'description', label: '描述'},
                {
                    name: 'createTime',
                    label: '创建时间',
                    render: (item: RevisionItem) => formatTime(item.createTime)
                },
                {name: 'operation', label: '操作'},
            ]
        };
    }

    attached() {
        this.getDetail();
        this.getConfigVersions();
    }

    onPageChange(event: {value: {page: number, pageSize: number}}) {
        const {page, pageSize} = event.value;
        this.data.set('pager', {page, pageSize});
        this.getConfigVersions();

    }

    onPageSizeChange(event: {value: {page: number, pageSize: number}}) {
        const {pageSize, page} = event.value;
        this.data.set('pager', {page, pageSize});
        this.getConfigVersions();
    }

    onCreate() {
        const configId = this.data.get('detail.configId');
        redirect(`#${ROUTE_PATH.configCreate}?pageType=new&configId=${configId}`);
    }

    // 过滤项设置
    onFilter(args: {field: {name: string}, filter: {value: string | number}}) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', 10);
        this.data.set(args.field.name, args.filter.value);
        this.getConfigVersions();
    }

    // 排序
    onSort(args: {value: {order: number, orderBy: number}}) {
        this.data.set('orderBy', args.value.orderBy);
        this.data.set('order', args.value.order);
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', 10);
        this.getConfigVersions();
    }

    getConfigVersions() {
        const {pager, state, order, orderBy} = this.data.get('');
        this.data.set('loading', true);
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            state,
            order,
            orderBy
        });
        return api.getConfigVersions(this.data.get('route.query.configId'), params)
            .then((res: {result: RevisionItem[], totalCount: number}) => {
                this.data.set('tablesource', res.result);
                this.data.set('total', res.totalCount);
                this.data.set('loading', false);
                return Promise.resolve();
            });
    }

    // 获取详情信息
    getDetail(): Promise<void> {
        return api.getConfigDetail(this.data.get('route.query.configId'), {})
            .then((detail: ConfigDetail) => {
                this.data.set('options', ConfigStatus.fromValue(detail.state));
                this.data.set('detail', detail);
                const temp = Object.keys(detail);
                this.data.set('datasource', temp.map(i => ({
                    label: TEXT_MAP[i],
                    value: i === 'state'
                        ? ConfigStatus.getTextFromValue(detail.state)
                        : i === 'createTime'
                            ? formatTime(detail.createTime)
                            : detail[i]
                })));
                this.data.set('loading', false);
                return Promise.resolve();
            });
    }

    // 刷新
    async onRefresh() {
        this.getDetail();
        this.getConfigVersions();
    }

    viewRevisionDetail(revisionId: string) {
        event.stopPropagation();
        const {configId} = this.data.get('detail');
        const dialog = new VersionDetail({data: {configId, revisionId}});
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.refreshInfo();
        });
    }

    onDiff(item: RevisionItem) {
        const {revisionId} = item;
        const {configId} = this.data.get('detail');
        const dialog = new VersionDiff({data: {configId, revisionId}});
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    onRegionChange() {
        redirect(`#${ROUTE_PATH.configList}`);
    }
}
