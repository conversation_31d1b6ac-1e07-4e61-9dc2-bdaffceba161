/**
 * 配置创建
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {
    Button,
    Table,
    Notification
} from '@baidu/sui';
import {AppCreatePage} from '@baidu/sui-biz';
import {ROUTE_PATH} from '@/common/config';
import CreateNav from '@/components/nav/create';
import api from '@/common/client';
import Basic from '../components/basic';
import Param from '../components/param-config';
import ConfigCreateFooter from '../components/config-create-footer';
import './index.less';

const klass = 'bms-config-create';

@decorators.asPage(ROUTE_PATH.configCreate)
export default class extends Component {
    pageTitle = '创建配置';

    static template = html`
    <s-page class="${klass}" pageTitle="{{pageTitle}}" backTo="{{backTo}}" backToLabel="返回">
        <basic-info s-ref="basic-info" pageType="{{pageType}}" configId="{{configId}}"/>
        <param-info
            s-ref="param-info"
            pageType="{{pageType}}"
            configId="{{configId}}"
            on-table-loaded="handleTableLoaded"
        />
        <div slot="pageFooter" class="page-footer-slot">
            <config-footer
                pageType="{{pageType}}"
                confirmDisabled="{{confirmDisabled}}"
                on-confirm="onConfirm"
                on-reset="reset"
                on-cancel="cancel"
            />
        </div>
    </s-page>`;

    static components = {
        's-button': Button,
        'create-nav': CreateNav,
        's-table': Table,
        'basic-info': Basic,
        'param-info': Param,
        'config-footer': ConfigCreateFooter,
        's-page': AppCreatePage
    };

    initData() {
        return {
            pageType: 'create',
            confirmDisabled: true
        };
    }

    static computed = {
        pageTitle(): string {
            const pageType = this.data.get('route.query.pageType');
            return pageType === 'new' ? '新增版本' : '创建配置';
        },
        backTo(): string {
            const pageType = this.data.get('route.query.pageType');
            const configId = this.data.get('route.query.configId');
            const url =  pageType === 'new'
                ? `${ROUTE_PATH.configDetail}?configId=${configId}`
                : `${ROUTE_PATH.configList}`;
            return url;
        }
    };

    inited() {
        const {pageType, configId} = this.data.get('route.query');
        this.data.set('pageType', pageType);
        pageType === 'new' && this.data.set('configId', configId);
    }

    handleTableLoaded() {
        this.data.set('confirmDisabled', false);
    }
    async onConfirm() {
        const {pageType, configId} = this.data.get('route.query');
        const basicInfo = await this.ref('basic-info').getConfirmData();
        const paramInfo = this.ref('param-info').getConfirmData();
        const confirmData = {
            ...basicInfo,
            ...paramInfo
        };
        this.data.set('isSubmiting', true);
        if (pageType !== 'new') {
            api.createConfig(confirmData).then((target: {configId: string}) => {
                this.data.set('isSubmiting', false);
                Notification.success('创建成功');
                redirect(`#${ROUTE_PATH.configList}`);
            }).catch(() => this.data.set('isSubmiting', false));
        }
        else {
            api.createConfigVersion(configId, confirmData).then((target) => {
                this.data.set('isSubmiting', false);
                Notification.success('新建版本成功');
                redirect(`#${ROUTE_PATH.configDetail}?configId=${configId}`);
            }).catch(() => this.data.set('isSubmiting', false));
        }

    }

    async reset() {
        this.ref('basic-info').setData();
        await this.ref('param-info').setData();
    }

    cancel() {
        window.location.href = `#${ROUTE_PATH.configList}`;
    }

    onRegionChange() {
        redirect(`#${ROUTE_PATH.configList}`);
    }
}
