/**
 * 集群配置版本详情
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import CommonTable from '@/components/common-table';
import {html} from '@baiducloud/runtime';
import {Drawer, Table, Button, Pagination} from '@baidu/sui';
import {SearchBox} from '@baidu/sui-biz';
import {pickEmpty, formatTime, TagFilter} from '@/common/util';
import {columns} from './components/param-config';
import {ClusterItem} from '../cluster/list/index';
import {ClusterStatusType, PaymentType, VersionTypeEnum} from '@/common/enums/constant';
import {ClusterStatus, AllEnum, Payments} from '@/common/enums';
import Tip from '@/components/tip';
import {ROUTE_PATH, PAGER_SUI} from '@/common/config';
import EllipsisTip from '@/components/ellipsis-tip';
import {UtilHandler} from '../cluster/util/util';
import {renderStatus} from '@/common/html';
import {BackSelect, versionType} from '../cluster/list/index';
import api from '@/common/client';

import './index.less';
const allEnum = AllEnum.toArray();
const klass = 'config-revision-detail';
export default class VersionDetail extends CommonTable {
    static template = html`
    <template>
        <drawer-common
            title="版本详情"
            class="${klass}"
            open="{{open}}"
            maskClose="{{true}}"
            otherClose="{{true}}"
            on-close="onClose"
            getContainer="{{getContainer}}"
            size="{{850}}"
        >
            <span class="title mb24">参数信息</span>
            <s-table
                columns="{{columns}}"
                datasource="{{source}}"
                loading="{{tableLoading}}"
            >
            <div slot="h-updateMode">
                更新模式
                <tip-cmpt placement="right" type="question">
                    <p>静态模式：需要重启集群生效</p>
                    <p>动态模式：无需重启集群生效</p>
                </tip-cmpt>
            </div>
                <div slot="c-updateMode">{{row.updateMode === 'STATIC' ? '静态模式' : '动态模式'}}</div>
            </s-table>
            <span class="title mt24 mb24">关联集群</span>
            <div style="display: flex; justify-content: flex-end;margin-bottom: 16px">
                <s-searchbox
                    class="cluster-search-box"
                    placeholder="{{searchbox.placeholder}}"
                    on-keywordTypeChange="onKeywordTypeChange"
                    text-datasource="{{searchbox.textDataSource}}"
                    value="{= searchbox.keyword =}"
                    keyword-type="{= searchbox.keywordType =}"
                    datasource="{{searchbox.keywordTypes}}"
                    on-search="onSearch"
                    width="170"
                />
            </div>
            <s-table
                class="cluster-table"
                width="1000"
                columns="{{clusterColumns}}"
                datasource="{{clusterSource}}"
                loading="{{clusterLoading}}"
                on-sort="onSort"
                on-filter="onFilter"
            >
                <div slot="c-name" class="${klass}__instant">
                    <div>
                        <a href="#${ROUTE_PATH.clusterDetailInfo}?name={{row.name}}&clusterId={{row.clusterId}}"
                            class="a-btn" target="_blank">
                            {{row.name}}
                        </a>
                        <instant-editor
                            value="{{row.name}}"
                            info="{{rowIndex}}"
                            request="{{editName}}"
                            check="{{check}}"
                            placeholder="请输入集群名称"
                            desc="不超过65个字符，仅支持数字/字母/特殊符号(_/-.)"
                        />
                    </div>
                    <div>{{row.clusterId}}</div>
                </div>
                <div slot="c-payment" class="${klass}__instant">
                    <div>{{row.payment | filterPayment}}</div>
                    <div s-if="row.payment === 'Prepaid'">{{row.expiration}}</div>
                </div>
                <div slot="c-status">{{row.status | filterStatus | raw}}</div>
                <back-select
                    slot="h-version-filter"
                    on-filter="onVersionFilter"
                />
                <div slot="c-tags">
                    <span class="tag-text" s-if="{{!row.tags || row.tags.length < 1}}">-</span>
                    <s-tooltip s-else class="tag-tip">
                        <div slot="content" class="tag-tip-content">
                            {{row | tagTip | raw}}
                        </div>
                        <div class="tag-text">{{row | tagText | raw}}</div>
                    </s-tooltip>
                </div>
            </s-table>
            <div style="display: flex;justify-content: flex-end;margin-top: 16px">
                <s-pagination
                    s-if="{{totalCount > 10}}"
                    layout="{{'pageSize, pager, go'}}"
                    total="{{totalCount}}"
                    pageSize="{{pager.pageSize}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    max-item="{{7}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </div>
            <div class="${klass}__bottom">
                <s-button
                    skin="primary"
                    class="ml12"
                    on-click="onClose">
                    确定
                </s-button>
            </div>
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-table': Table,
        's-searchbox': SearchBox,
        's-button': Button,
        'tip-cmpt': Tip,
        'ellipsis-tip': EllipsisTip,
        's-pagination': Pagination,
        'back-select': BackSelect,
    };

    static filters: SanFilterProps = {
        filterStatus(status: ClusterStatusType) {
            let temp = renderStatus(ClusterStatus.fromValue(status));
            if (_.includes([ClusterStatusType.DEPLOYING, ClusterStatusType.UPDATING, ClusterStatusType.REBOOTING, ClusterStatusType.PRE_REBOOTING, ClusterStatusType.PRE_UPDATING], status)) {
                temp += html`<div class="desc">预计需要10~30分钟</div>`;
            }
            return temp;
        },
        filterPayment(payment: PaymentType) {
            switch (payment) {
                case PaymentType.PREPAID:
                    return '预付费';
                case PaymentType.POSTPAID:
                    return '后付费';
                default:
                    return '-';
            }
        },
        ...TagFilter
    };

    initData() {
        return {
            open: true,
            columns: columns,
            source: [],
            clusterColumns: [
                {
                    name: 'name',
                    label: '集群名称/ID',
                    minWidth: 260,
                    fixed: 'left'
                },
                {
                    name: 'status',
                    label: '状态',
                    filter: {
                        options: [
                            ...allEnum,
                            ...ClusterStatus.toArray()
                        ],
                        value: allEnum[0].value
                    },
                    minWidth: 130
                },
                {
                    name: 'payment',
                    label: '付费方式',
                    filter: {
                        options: [
                            ...allEnum,
                            ...Payments.toArray()
                        ],
                        value: allEnum[0].value
                    },
                    minWidth: 150
                },
                {
                    name: 'version',
                    label: '版本',
                    filter: {},
                    minWidth: 100
                },
                {
                    name: 'logicalZones',
                    label: '可用区',
                    minWidth: 100,
                    render: (item: ClusterItem) => UtilHandler.logicalZones(item.logicalZones),
                },
                {
                    name: 'createTime',
                    label: '创建时间',
                    minWidth: 150,
                    sortable: true,
                    render: (item: ClusterItem) => formatTime(item.createTime)
                },
                {
                    name: 'tags',
                    minWidth: 100,
                    label: '集群标签'
                }
            ],
            getContainer: document.getElementById('main'),
            pager: {...PAGER_SUI},
            clusterLoading: true,
            searchbox: {
                keyword: '',
                keywordType: ['NAME'],
                keywordTypes: [
                    {
                        value: 'NAME',
                        text: '集群名称'
                    },
                    {
                        value: 'ID',
                        text: '集群ID'
                    }
                ],
                allTextDataSource: []
            },
        };
    }

    inited() {

    }

    async attached() {
        await this.getRevisionDetail();
        await this.getTableList();
        this.getVersions();
    }

    onSearch(args?: {value: string}): void {
        this.data.set('pager.page', 1);
        this.getTableList();
    }

    // 获取版本
    getVersions() {
        this.data.set('loading', true);
        api.listAvailableVersion({})
            .then((target: {versions: versionType[]}) => {
                this.data.set('options',
                    [
                        ...allEnum,
                        ..._.map(target.versions, i => ({
                            value: i.version,
                            text: i.version
                        }))
                    ]);
            })
            .finally(() => this.data.set('loading', false));
    }

    // 版本过滤
    onVersionFilter(target: {version: string}) {
        this.data.set('version', target.version);
        this.getTableList();
    }

    async getTableList() {
        this.data.set('clusterLoading', true);
        const {status, orderBy, order, version, payment, configId, revisionId, pager, searchbox} = this.data.get('');
        let params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            status,
            payment,
            order,
            orderBy,
            version,
            configId,
            revisionId,
            keywordType: searchbox.keywordType[0],
            keyword: searchbox.keyword
        });
        const {result, totalCount} = await api.listCluster(params);
        this.data.set('clusterSource', result);
        this.data.set('totalCount', totalCount);
        this.data.set('clusterLoading', false);
    }

    async getRevisionDetail() {
        const {configId, revisionId} = this.data.get('');
        this.data.set('tableLoading', true);
        await api.getConfigVersionDetail(configId, revisionId, {})
            .then((target: {context: any[]}) => {
                this.data.set('source', target.context.map(item => ({
                    name: item.name,
                    description: item.description,
                    updateMode: item.updateMode,
                    scope: item.scope,
                    defaultValue: item.defaultValue,
                    currentValue: item.currentValue,
                    type: item.type,
                    unit: item.unit,
                    category: item.category,
                    sourceList: item.type === 'ENUM' ? this.computeEnumSource(item.scope) : []
                })));
                this.data.set('tableLoading', false);
            });
    }

    computeEnumSource(scope: any[]) {
        const res = scope.map(item => ({
            value: item,
            label: item
        }));
        return res;
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
