/**
 * 版本对比面板
 *
 * @file diff-panel.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Table} from '@baidu/sui';

export default class diffPanel extends Component {
    static template = html`
    <template>
        <slot name="filter"></slot>
        <s-table
            columns="{{columns}}"
            rowClassName="{{rowClassName}}"
            datasource="{{datasource}}"
            loading="{{loading}}"
        >
        </s-table>
    </template>
    `;
    static components = {
        's-table': Table
    };

    initData() {
        return {
            rowClassName(item: any, index: number) {
                switch (item.class) {
                    case 'add':
                        return 'add-line';
                    case 'delete':
                        return 'delete-line';
                    case 'alter':
                        return 'alter-line';
                    default:
                        break;
                }
            }
        };
    }
}
