/**
 * @file index.ts 创建配置底部footer
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {Button} from '@baidu/sui';

import './index.less';

export default class ConfigCreateFooter extends Component {
    static template = html`
    <template>
        <div  class="footer-btn">
            <s-button
                class="mr20"
                skin="primary"
                disabled="{{confirmDisabled}}"
                on-click="onConfirm"
                size="large"
            >
                确定
            </s-button>
            <s-button
                class="mr20"
                on-click="onReset"
                size="large"
            >
                重置
            </s-button>
            <s-button
                on-click="onCancel"
                size="large"
                loading="{{loading.nextStep}}"
            >
                取消
            </s-button>
        </div>
    </template>
    `;

    static components = {
        's-button': Button
    };

    onCancel() {
        this.fire('cancel', {});
    }

    onReset() {
        this.fire('reset', {});
    }

    onConfirm() {
        this.fire('confirm', {});
    }
}
