/**
 * 创建配置-参数配置模块
 *
 * @file param=config.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {OutlinedSetting} from '@baidu/sui-icon';
import Tip from '@/components/tip';
import {Input, Tooltip, Table, Select, Switch, InputNumber, Button} from '@baidu/sui';
import ParamSet from './param-set';
import api from '@/common/client';

const klass = 'bms-config-create-param';

const TextMap = {
    title: '参数设置',
    name: '配置名称：',
    desc: '配置描述：',
};

export const columns = [
    {name: 'name', label: '参数名称', width: 200},
    {name: 'description', label: '描述', width: 200},
    {name: 'updateMode', label: '更新模式', width: 100},
    {name: 'scope', label: '参数范围', width: 200},
    {name: 'defaultValue', label: '默认值', width: 80},
    {name: 'currentValue', label: '当前值', width: 120},
];


export default class Param extends Component {
    static template = html`
    <div class="${klass} bms-form-panel">
            <s-append noHighlight label="${TextMap.title}" style="margin-bottom: 24px">
                <div slot="extra" style="position: absolute; right: 40px;" on-click="setParamList">
                    <s-tooltip content="参数筛选" placement="top" class="right">
                        <s-icon-config class="button-icon" is-button="{{false}}" />
                    </s-tooltip>
                </div>
            </s-append>
            <s-table
                columns="{{columns}}"
                datasource="{{source}}"
                loading="{{tableLoading}}"
                maxHeight="350"
            >
                <div slot="h-updateMode">
                    更新模式
                    <tip-cmpt placement="right" type="question">
                        <p>静态模式：需要重启集群生效</p>
                        <p>动态模式：无需重启集群生效</p>
                    </tip-cmpt>
                </div>
                <div slot="c-updateMode">{{row.updateMode === 'STATIC' ? '静态模式' : '动态模式'}}</div>
                <div slot="c-currentValue">
                    <s-switch
                        s-if="row.type === 'BOOLEAN'"
                        checked="{{row.currentValue}}"
                        on-change="changeCurrentValue($event, row.name)"
                    ></s-switch>
                    <s-input-number
                        s-if="{{row.type === 'INT' || row.type == 'LONG'}}"
                        value="{{row.currentValue}}"
                        min="{{row.scope[0]}}"
                        max="{{row.scope[1]}}"
                        width="200"
                        on-change="changeCurrentValue($event, row.name)"
                    ></s-input-number>
                    <s-select
                        s-if="row.type === 'ENUM'"
                        width="200"
                        on-change="changeCurrentValue($event, row.name)"
                        value="{{row.currentValue}}"
                        datasource="{{row.sourceList}}"
                    ></s-select>
                </div>
            </s-table>
        </div>`;

    static components = {
        's-append': AppLegend,
        's-input': Input,
        's-input-number': InputNumber,
        's-textarea': Input.TextArea,
        's-table': Table,
        's-switch': Switch,
        's-select': Select,
        's-button': Button,
        's-tooltip': Tooltip,
        's-icon-config': OutlinedSetting,
        'tip-cmpt': Tip,
    };

    initData() {
        return {
            columns: columns,
            datasource: [],
            source: [],
            tableLoading: true
        };
    }

    async attached() {
        const pageType = this.data.get('pageType');
        if (pageType === 'new') {
            await this.getRevisionDetail();
        }
        await this.getParams();
    }

    changeSource() {
        const datasource = this.data.get('datasource');
        const source = _.filter(datasource, i => i.defaultSelect);
        this.data.set('source', source);
    }

    changeCurrentValue(target: {value: string}, name: string) {
        const datasource = this.data.get('datasource');
        const index = _.findIndex(datasource, i => i.name === name);
        this.data.set(`datasource[${index}].currentValue`, target.value);
        this.nextTick(() => {
            this.changeSource();
        });
    }

    async getRevisionDetail() {
        const configId = this.data.get('configId');
        const {result} = await api.getConfigVersions(configId, {pageNo: 1, pageSize: 10});
        const revisionId = result[0]?.revisionId;
        this.data.set('revisionId', revisionId);
        await api.getConfigVersionDetail(configId, revisionId, {})
            .then((target: {context: any[]}) => {
                this.data.set('originalData', target.context.map(i => ({
                    ...i,
                    defaultSelect: true,
                    sourceList: i.type === 'ENUM' ? this.computeEnumSource(i.scope) : []
                })));
            });
    }


    async getParams() {
        const {pageType, originalData} = this.data.get('');
        this.data.set('tableLoading', true);
        this.data.set('datasource', null);
        this.data.set('source', null);
        const result = await api.listConfigParams({});
        const datasource = result.map(item => ({
            name: item.name,
            description: item.description,
            updateMode: item.updateMode,
            scope: item.scope,
            defaultValue: item.defaultValue,
            currentValue: item.currentValue,
            type: item.type,
            unit: item.unit,
            category: item.category,
            overrideMode: item.overrideMode,
            defaultSelect: item.overrideMode === 'REQUIRED',
            sourceList: item.type === 'ENUM' ? this.computeEnumSource(item.scope) : []
        }));
        const temp = [];
        if (pageType === 'new') {
            datasource.forEach(item => {
                const index = _.findIndex(originalData, i => i.name === item.name);
                index === -1 && temp.push({
                    ...item,
                    defaultSelect: false
                });
            });
        }
        this.nextTick(() => {
            this.data.set('datasource', pageType !== 'new' ? datasource : originalData.concat(temp));
            this.changeSource();
            this.data.set('tableLoading', false);
        });
        this.fire('table-loaded', {});
    }

    computeEnumSource(scope: any[]) {
        const res = scope.map(item => ({
            value: item,
            label: item
        }));
        return res;
    }

    setParamList() {
        const {datasource} = this.data.get('');
        const typeList = _.map(datasource, 'category');
        const dialog = new ParamSet({data: {
            paramList: datasource.map(i => ({
                name: i.name,
                category: i.category,
                description: i.description,
                defaultSelect: i.defaultSelect,
                overrideMode: i.overrideMode
            })),
            typeList: Array.from(new Set(typeList)).map(i => ({label: i, value: i}))
        }});
        dialog.attach(document.body);
        dialog.on('confirm', ({result}) => {
            const {datasource} = this.data.get('');
            this.data.set('tableLoading', true);
            this.data.set('datasource', null);
            datasource.forEach((item, index) => {
                const temp = _.find(result, i => i.name === item.name);
                datasource[index].defaultSelect = temp?.defaultSelect;
            });
            this.nextTick(() => {
                this.data.set('tableLoading', false);
                this.data.set('datasource', datasource);
                this.changeSource();
            });
        });
    }

    // 获取创建配置的参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const {source, pageType, revisionId} = this.data.get('');
        let context = {};
        if (source) {
            for (const item of source) {
                if (item.hasOwnProperty('name') && item.hasOwnProperty('currentValue')) {
                    context[item.name] = item.currentValue;
                }
            }
        }
        return pageType !== 'new' ? {
            context
        } : {
            revisionId: revisionId + 1,
            context
        };
    }

    async setData() {
        const {pageType} = this.data.get('');
        if (pageType === 'new') {
            await this.getRevisionDetail();
        }
        await this.getParams();
    }
}
