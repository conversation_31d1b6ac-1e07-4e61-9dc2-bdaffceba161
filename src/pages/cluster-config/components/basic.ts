/**
 * 配置基本信息模块
 *
 * @file basic.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Input, Form} from '@baidu/sui';
import {VAILDITE_ITEMS} from '@/common/rules';
import api from '@/common/client';
import {ConfigItem} from '../list/index';
const klass = 'bms-config-create-basic';

const TextMap = {
    title: '基本信息',
    name: '配置名称：',
    description: '配置描述：',
};

export default class Basic extends Component {
    static template = html`
    <div class="${klass} bms-form-panel">
        <s-append noHighlight label="${TextMap.title}" style="margin-bottom: 24px"></s-append>
            <s-form
                data="{{formData}}"
                rules="{{rules}}"
                s-ref="form"
            >
                <s-formitem
                    label="${TextMap.name}"
                    prop="name"
                    help="不超过65个字符，仅支持数字/字母/特殊符号(_/-.)，必须以字母开头"
                >
                    <s-input
                        value="{= formData.name =}"
                        width="200"
                        disabled="{{pageType === 'new'}}"
                        placeholder="请输入配置名称"
                        on-input="onNameInputChange"
                    />
                </s-formitem>
                <s-formitem label="${TextMap.description}">
                    <s-textarea
                        placeholder="请输入描述信息"
                        width="400"
                        height="100"
                        value="{= formData.description =}"
                        maxLength="120"
                    />
                </s-formitem>
            </s-form>
        </div>`;

    static components = {
        's-append': AppLegend,
        's-input': Input,
        's-textarea': Input.TextArea,
        's-form': Form,
        's-formitem': Form.Item,
    };

    initData() {
        return {
            formData: {
                description: '',
                name: '',
            },
            rules: {
                name: [
                    VAILDITE_ITEMS.requiredInput,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (value.length > 65) {
                                return callback('不能超过65个字符');
                            }
                            if (!/^[a-zA-Z][a-zA-Z()_\/\-.\d]{0,}$/.test(value)) {
                                return callback('输入字符格式有误');
                            }
                            callback();
                        }
                    }
                ]
            }
        };
    }

    attached() {
        const pageType = this.data.get('pageType');
        if (pageType === 'new') {
            this.getDetail();
        }
    }

    getDetail() {
        const configId = this.data.get('configId');
        return api.getConfigDetail(configId, {}).then((result: ConfigItem) => {
            this.data.set('originalData', {
                name: result?.name,
                description: result?.description,
            });
            this.setData();
        });
    }

    // 获取创建配置的参数(注意，这个是给予父组件使用)，不能随便删除
    async getConfirmData() {
        await this.ref('form')?.validateFields();
        const {name, description} = this.data.get('formData');
        return {
            name,
            description
        };
    }

    setData() {
        const pageType = this.data.get('pageType');
        if (pageType === 'new') {
            const {name, description} = this.data.get('originalData');
            this.data.merge('formData', {name, description});
        }
        else {
            this.data.merge('formData', {name: '', description: ''});
        }
    }
}
