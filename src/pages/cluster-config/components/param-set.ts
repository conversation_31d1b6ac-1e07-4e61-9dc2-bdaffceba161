/**
 * 指标筛选弹窗组件
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Radio, Checkbox} from '@baidu/sui';
import './index.less';

const klass = 'config-filter';

export default class extends Component {
    static template = html`
    <template>
        <s-dialog
            title="参数筛选"
            open="{{open}}"
            mask="{{true}}"
            width="1100"
            class="${klass}"
            on-close="onClose">
            <s-radio-group
                on-change="handleTypeChange"
                value="{{type}}"
                radioType="button"
                datasource="{{typeList}}"
            >
            </s-radio-group>
            <s-checkbox
                label="全选"
                class="mt16"
                checked="{=checkBoxValue=}"
                indeterminate="{{true}}"
                on-change="onAllSelectChange"
            />
            <div class="checkbox-area">
                <s-checkbox
                    s-for="item, index in checkList"
                    checked="{=item.defaultSelect=}"
                    disabled="{{item.overrideMode === 'REQUIRED' && item.defaultSelect}}"
                    on-change="onCheckBoxChange($event, index)"
                >
                    {{item.name}}
                    <p class="alias">{{item.description}}</p>
                </s-checkbox>
            </div>
            <div slot="footer">
                <s-button on-click="onConfirm" skin="primary">确定</s-button>
                <s-button on-click="onClose">取消</s-button>
            </div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup
    };

    initData() {
        return {
            open: true,
            type: '',
            typeList: [],
            checkList: [],
            showCheckList: true,
            checkBoxValue: false
        };
    }

    attached() {
        const {paramList, typeList} = this.data.get('');
        this.data.set('typeList', typeList);
        this.data.set('type', typeList[0].value);
        this.data.set('checkList', _.filter(paramList, item => item.category === typeList[0].value));
        const hasNotSelect = this.data.get('checkList').some(item => !item.defaultSelect);
        this.data.set('checkBoxValue', !hasNotSelect);
    }

    handleTypeChange(target: {value: string}) {
        const {paramList, checkList, type} = this.data.get('');
        const otherList = _.filter(paramList, item => item.category !== type);
        this.data.set('paramList', checkList.concat(otherList));
        this.data.set('type', target.value);
        this.data.set('checkList', _.filter(this.data.get('paramList'), item => item.category === target.value));
        const hasNotSelect = this.data.get('checkList').some(item => !item.defaultSelect);
        this.data.set('checkBoxValue', !hasNotSelect);
    }

    onCheckBoxChange(target: {value: string}, index: number) {
        const hasNotSelect = this.data.get('checkList').some(item => !item.defaultSelect);
        this.data.set('checkBoxValue', !hasNotSelect);
    }

    onAllSelectChange(target: {value: string}) {
        const {checkList} = this.data.get('');
        checkList.forEach((item, index: number) => {
            if (target.value || item.overrideMode !== 'REQUIRED') {
                this.data.set(`checkList[${index}].defaultSelect`, target.value);
            }
        });
    }

    // 确认
    async onConfirm() {
        const {paramList, type, checkList} = this.data.get('');
        const otherList = _.filter(paramList, item => item.category !== type);
        this.data.set('paramList', checkList.concat(otherList));
        this.fire('confirm', {result: [...this.data.get('paramList')]});
        this.onClose();
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
