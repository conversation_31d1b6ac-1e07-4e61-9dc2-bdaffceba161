/**
 * 配置版本对比
 *
 * @file version-diff.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Table, Select} from '@baidu/sui';
import diffPanel from './diff-panel';
import api from '@/common/client';

const klass = 'version-diff';

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="${klass} ue-dialog"
            title="版本对比"
            okText="确定"
            width="785"
            open="{{open}}"
            on-confirm="onClose"
            on-close="onClose">
            <div class="mb16 ${klass}-title">
                <p class="version-title">
                    <span class="latest-tag" style="background-color: #ECFFE6; color: #30BF13">历史版本</span>
                    <s-select
                        value="{{version}}"
                        on-change="handleVersionChange"
                        disabled="{{loading1 || loading2}}"
                        width="80"
                    >
                        <s-option
                            s-for="item in versionList"
                            disabled="{{item.disabled}}"
                            value="{{item.value}}"
                            label="{{item.text}}"
                        >
                        </s-option>
                    </s-select>
                </p>
                <p class="version-title">
                    <span class="latest-tag">当前版本</span>
                    <span>版本{{revisionId}}</span>
                </p>
            </div>
            <div class="panel-content">
                <diff-panel
                    columns="{{columns}}"
                    datasource="{{prevSource}}"
                    loading="{{loading2}}"
                    class="diff-panel"
                >
                </diff-panel>
                <div class="split-line"></div>
                <diff-panel
                    columns="{{columns}}"
                    datasource="{{currentSource}}"
                    loading="{{loading1}}"
                    class="diff-panel"
                >
                </diff-panel>
            </div>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-table': Table,
        'diff-panel': diffPanel,
        's-select': Select,
        's-option': Select.Option
    };

    initData() {
        return {
            open: true,
            columns: [
                {
                    name: 'name',
                    label: '参数名称',
                    width: '220'
                },
                {
                    name: 'currentValue',
                    label: '参数值',
                    width: '100'
                }
            ],
            source1: [],
            source2: [],
            loading1: true,
            loading2: true,
            version: '',
            versionList: []
        };
    }

    async attached() {
        const {revisionId} = this.data.get('');
        const {result: versionList} = await this.getVersionList();
        // const idx = _.findIndex(versionList, version => version.revisionId === revisionId);
        // this.data.set('versionList', versionList.slice(idx).map(item => ({
        //     value: item.revisionId,
        //     text: item.revisionId
        // })));
        // const version = versionList.length > 1 ? revisionId - 1 : versionList[0]?.revisionId;
        this.data.set('versionList', versionList.map(item => ({
            value: item.revisionId,
            text: '版本' + item.revisionId,
            disabled: item.revisionId === revisionId
        })));
        const version = revisionId > 1 ? revisionId - 1 : revisionId;
        this.data.set('version', version);
        const {context: source1} = await this.getRevisionDetail(revisionId);
        const {context: source2} = await this.getRevisionDetail(version);
        this.data.set('source1', source1);
        this.data.set('source2', source2);
        this.computeDiff();
    }

    computeDiff() {
        const {source1, source2, version, revisionId} = this.data.get('');
        const currentSource = [...source1];
        const prevSource = [...source2];
        prevSource.forEach((item, index) => {
            const idx = _.findIndex(currentSource, item2 => item2.name === item.name);
            if (idx === -1) {
                prevSource[index].class = version < revisionId ? 'delete' : 'add';
            }
            else if (item.currentValue !== currentSource[idx].currentValue) {
                prevSource[index].class = 'alter';
            }
            else {
                prevSource[index].class = 'normal';
            }
        });
        currentSource.forEach((item, index) => {
            const idx = _.findIndex(prevSource, item2 => item2.name === item.name);
            if (idx === -1) {
                currentSource[index].class = version > revisionId ? 'delete' : 'add';
            }
            else if (item.currentValue !== prevSource[idx].currentValue) {
                currentSource[index].class = 'alter';
            }
            else {
                currentSource[index].class = 'normal';
            }
        });
        this.data.set('prevSource', prevSource);
        this.data.set('currentSource', currentSource);
        this.data.set('loading1', false);
        this.data.set('loading2', false);
    }

    async getVersionList() {
        const params = {
            pageNo: 1,
            pageSize: 999,
        };
        const configId = this.data.get('configId');
        return api.getConfigVersions(configId, params);
    }

    async getRevisionDetail(revisionId: number) {
        const {configId} = this.data.get('');
        return api.getConfigVersionDetail(configId, revisionId, {});
    }

    async handleVersionChange(target: {value: number}) {
        this.data.set('version', target.value);
        this.data.set('loading2', true);
        this.data.set('loading1', true);
        const {context: source2} = await this.getRevisionDetail(target.value);
        this.data.set('source2', source2);
        this.computeDiff();
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
