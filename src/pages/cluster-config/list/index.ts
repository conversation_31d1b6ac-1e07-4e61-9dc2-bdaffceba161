/**
 * 配置列表
 *
 * @file index.ts
 * <AUTHOR>
 */
import {defineComponent} from 'san';
import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {
    Pagination,
    Table,
    Button,
    Dialog,
    Notification,
    Tooltip
} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {ConfigStatusType} from '@/common/enums/constant';
import {TABLE_SUI, PAGER_SUI, ROUTE_PATH} from '@/common/config';
import {pickEmpty, formatTime, getTimer} from '@/common/util';
import {FORBID_HANDLER} from '@/common/rules';
import {ConfigStatus, AllEnum} from '@/common/enums';
import CommonTable from '@/components/common-table';
import {renderStatus} from '@/common/html';
import CreateBtn from '@/components/create-btn';
import ListTitle from '@/components/list-title';
import EllipsisTip from '@/components/ellipsis-tip';
import {OutlinedPlus} from '@baidu/sui-icon';
import api from '@/common/client';
import './index.less';

const klass = 'bms-config-list';

export type ConfigItem = {
    name: string;
    configId: string;
    description: string;
    state: string;
    createTime: string;
};
let timeCount: number;
const allEnum = AllEnum.toArray();
@decorators.asPage(ROUTE_PATH.configList)
@decorators.withSidebar({active: ROUTE_PATH.configList})
export default class ConfigList extends CommonTable {
    static template = html`
    <div class="${klass} bms-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <list-title
                    title="配置列表"
                    hasOnlineAlert="{{false}}"
                    hideHelpDoc="{{true}}"
                />
            </div>
            <div slot="bulk">
                <create-btn text="创建配置" on-click="onCreate" />
            </div>
            <div slot="filter">
                <s-searchbox
                    class="cluster-search-box"
                    placeholder="{{searchbox.placeholder}}"
                    on-keywordTypeChange="onKeywordTypeChange"
                    text-datasource="{{searchbox.textDataSource}}"
                    value="{= searchbox.keyword =}"
                    keyword-type="{= searchbox.keywordType =}"
                    datasource="{{searchbox.keywordTypes}}"
                    on-search="onSearch"
                    width="170"
                />
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-sort="onSort"
                on-filter="onFilter">
                <div slot="c-name" class="${klass}__instant">
                    <div>
                        <a href="#${ROUTE_PATH.configDetail}?name={{row.name}}&configId={{row.configId}}"
                            class="a-btn">
                            {{row.name}}
                        </a>
                    </div>
                    <div>{{row.configId}}</div>
                </div>
                <div slot="c-description">
                    <ellipsis-tip text="{{row.description}}" placement="top" />
                </div>
                <div slot="c-state">{{row.state | filterStatus | raw}}</div>
                <div slot="c-operation">
                    <s-tooltip s-if="{{!row.canDelete}}" placement="top" content="集群配置使用中，暂不支持删除">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{true}}"
                            on-click="onDelete(row)">
                            删除
                        </s-button>
                    </s-tooltip>
                    <s-button
                        s-else
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onDelete(row)">
                        删除
                    </s-button>
                </div>
                <div slot="empty">
                    <s-empty vertical emptyText="您还没有创建任何集群配置">
                        <s-button skin="stringfy" slot="action" on-click="onCreate">
                            <outlined-plus class="create-icon" />
                            创建配置
                        </s-button>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    initData() {
        return {
            searchbox: {
                keyword: '',
                keywordType: ['CONFIG_NAME'],
                keywordTypes: [
                    {
                        value: 'CONFIG_NAME',
                        text: '配置名称'
                    },
                    {
                        value: 'CONFIG_ID',
                        text: '配置ID'
                    }
                ],
                allTextDataSource: []
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'name',
                        label: '配置名称/ID',
                        minWidth: 70
                    },
                    {
                        name: 'state',
                        label: '状态',
                        filter: {
                            options: [
                                ...allEnum,
                                ...ConfigStatus.toArray()
                            ],
                            value: allEnum[0].value
                        },
                        width: 130
                    },
                    {
                        name: 'description',
                        label: '描述'
                    },
                    {
                        name: 'createTime',
                        label: '创建时间',
                        sortable: true,
                        render: (item: ConfigItem) => formatTime(item.createTime)
                    },
                    {
                        name: 'operation',
                        label: '操作',
                        width: 90
                    }
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        'ellipsis-tip': EllipsisTip,
        'create-btn': CreateBtn,
        's-tooltip': Tooltip,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty,
        'list-title': ListTitle,
        'outlined-plus': OutlinedPlus
    };

    static filters: SanFilterProps = {
        filterStatus(status: ConfigStatusType) {
            return renderStatus(ConfigStatus.fromValue(status));
        },
    };

    attached() {
        this.getComList();
        timeCount = getTimer(() => this.getComList(false));
    }

    detached() {
        clearInterval(timeCount);
    }

    onRefresh() {
        this.getComList();
        clearInterval(timeCount);
        timeCount = getTimer(() => this.getComList(false));
    }

    /**
     * 获取表格，重写是为了支持自动刷新不展示loading
     */
    async getComList(loadStatus = true) {
        this.data.set('table.loading', loadStatus);
        this.data.set('table.error', false);
        // 清除多选
        this.data.set('selection.selectedIndex', []);
        try {
            await this.getTableList();
            this.data.set('table.loading', false);
        }
        catch (error) {
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    /**
     * 分页，重写是为了支持换页后自动刷新定时器重置
     * @param {Number} page page
     * @param {Number} pageSize pageSize
     */
    onPageChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', args.value.page);
        this.onRefresh();
    }

    /**
     * 分页 pageSize 设置，重写是为了支持改变页面大小后自动刷新定时器重置
     */
    onPageSizeChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', 1);
        this.onRefresh();
    }

    /**
     * 搜索，重写是为了支持搜索后自动刷新定时器重置
     */
    onSearch(args?: {value: string}): void {
        this.data.set('pager.page', 1);
        this.onRefresh();
    }

    async getTableList() {
        const {searchbox, pager, state, orderBy, order} = this.data.get('');
        const param = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            state,
            order,
            orderBy,
            keywordType: searchbox.keywordType[0],
            keyword: searchbox.keyword
        });
        const {totalCount, result} = await api.listConfig(param);
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', _.map(result as ConfigItem[], item => ({
            ...item,
            canDelete: FORBID_HANDLER.checkConfigDelete(item),
        })));
    }

    // 创建按钮点击
    async onCreate() {
        redirect(`#${ROUTE_PATH.configCreate}`);
    }

    // 删除
    onDelete(row: ConfigItem) {
        Dialog.warning({
            content: defineComponent({
                template: html`
                <div>
                    <div>配置：${row.name}</div>
                    <div>删除后配置无法恢复，您确定删除此配置吗？</div>
                </div>`
            }),
            okText: '确定',
            onOk: async () => {
                await api.deleteConfig(row.configId, {});
                Notification.success('删除成功');
                this.onRefresh();
            }
        });
    }

    // region 切换
    onRegionChange() {
        this.data.set('searchbox.keyword', '');
        this.data.merge('pager', {...PAGER_SUI});
        this.onRefresh();
    }
}
