/**
 * 批量启动弹窗
 *
 * @file index.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Table} from '@baidu/sui';
import {simpleColumns} from '../list/config';
import {debounce} from '@/common/decorator';

import './index.less';
const klass = 'show-batchstart';

export default class extends Component {
    static template = html`
    <template>
        <s-dialog
            open="{{open}}"
            title="批量启动任务"
            type="info"
            class="${klass}"
            width="1000"
        >
            <div class="${klass}_subtitle">已选{{count}}个任务，确定要批量启动任务么？</div>
             <s-table
                columns="{{columns}}"
                datasource="{{taskItem}}"
                on-filter="onFilter"
                class="${klass}_content">
            </s-table>
            <div slot="footer">
                <s-button on-click="onClose" >取消</s-button>
                <s-button on-click="onStart" skin="primary">启动</s-button>
            </div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-table': Table,

    };

    initData() {
        return {
            open: true,
            columns: simpleColumns,
            count: 0, // 父组件传递
            taskItem: [], // 父组件传递
        };
    }

    @debounce(500)
    onStart() {
        this.fire('confirm', {});
        this.onClose();
    }

    // 控制弹窗关闭
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
