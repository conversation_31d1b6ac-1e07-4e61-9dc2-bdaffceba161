import {AllEnum} from '@/common/enums';
import {formatTime} from '@/common/util';
import {taskItem} from '../../types';

const allEnum = AllEnum.toArray();

export const columns: ToggleTableColumn[] = [
    {
        name: 'name',
        label: '任务名称/ID',
        fixed: 'left',
        width: 200
    },
    {
        name: 'status',
        label: '任务状态',
        filter: {
            options: [
                ...allEnum,
            ],
            value: allEnum[0].value
        },
        width: 100
    },
    {
        name: 'version',
        label: '版本',
        width: 80
    },
    {
        name: 'taskType',
        label: '任务类型',
        width: 100
    },
    {
        name: 'sourceName',
        label: '源端集群',
        width: 160
    },
    {
        name: 'targetName',
        label: '目标端集群',
        width: 160
    },
    {
        name: 'taskTags',
        label: '任务标签',
        width: 80
    },
    {
        name: 'createTime',
        label: '创建时间',
        sortable: true,
        render: (item: taskItem) => formatTime(item.createTime),
        width: 172
    },
    {
        name: 'operation',
        label: '操作',
        fixed: 'right',
        width: 124,
    }
];

// 批量启动去掉的标签
const excludeNames = ['taskTags', 'operation'];

export const simpleColumns = columns.reduce((arr, {name, label, width, render}) => {
    if (!excludeNames.includes(name)) {
        arr.push({name, label, width, render});
    }
    return arr;
}, [] as ToggleTableColumn[]);

