import {CONFIG_TYPE, PAGER_SUI, ROUTE_PATH, TABLE_SUI} from '@/common/config';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {
    Button,
    Search,
    Table,
    Pagination,
    Dropdown,
    Menu,
    Tooltip,
    Drawer,
    Notification,
    Link
} from '@baidu/sui';
import {AppListPage, Empty} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';
import InstantEditor from '@/components/instant-editor';
import ListTitle from '@/components/list-title';
import CommonTable from '@/components/common-table';
import CreateBtn from '@/components/create-btn';
import BatchStart from '../batch-start';
import ConfirmDialog from '../../components/confirm-dialog';
import EllipsisTip from '@/components/ellipsis-tip';
import {getTimer} from '@/common/util';
import {columns} from './config';
import {taskItem} from '../../types';
// import api from '@/common/client';

import './index.less';

const klass = 'kafka-connect-task';

let timeCount: number = 0;
@decorators.asPage(ROUTE_PATH.connectTask)
@decorators.withSidebar({active: ROUTE_PATH.connectTask})
export default class ConnectTask extends CommonTable {
    static template = html`
    <div class="${klass} kafka-connect-task">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <list-title
                    title="连接器任务"
                    type="${CONFIG_TYPE.VIP}"
                    hasOnlineAlert
                />
            </div>
            <div slot="bulk">
                <create-btn text="创建任务" on-click="onCreate" />
                <s-button
                    class="ml8"
                    disabled="{= !selection.selectedIndex.length =}"
                    on-click="onBatchStart"
                >
                    批量启动
                </s-button>
            </div>
            <div slot="filter" class="task-filter">
                <s-search
                    class="task-search-box"
                    placeholder="{{searchbox.placeholder}}"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="204"
                    clearable
                />
                <s-button on-click="onRefresh" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                selection="{{selection}}"
                on-selected-change="onSelectChange"
                on-sort="onSort"
                on-filter="onFilter">
                <div slot="c-name" class="${klass}__instant">
                    <div>
                        <s-link
                            href="#${ROUTE_PATH.connectDetailInfo}?name={{row.name}}"
                            skin="primary"
                            >
                            {{row.name}}
                        </s-link>
                        <instant-editor
                            value="{{row.name}}"
                            info="{{rowIndex}}"
                            disabled="{{row.status !== 'ACTIVE'}}"
                            request="{{editName}}"
                            check="{{check}}"
                            placeholder="请输入任务名称"
                            desc="不超过65个字符，仅支持数字/字母/特殊符号(_/-.)"
                        />
                    </div>
                    <ellipsis-tip placement="bottom" text="{{row.connectorId}}" showTip="{{true}}" />
                </div>
                <div slot="c-operation">
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onPause(row)">
                        暂停
                    </s-button>
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onStart(row)">
                        启动
                    </s-button>
                    <s-dropdown class="operation-dropdown">
                        <s-menu slot="overlay">
                            <s-menu-item  s-for="operation,index in moreOperationData" key="{{index}}">
                                <s-tooltip content="{{operation | tipText(row)}}" placement="left">
                                    <s-button
                                        on-click="handleMenuItemClick(row, operation)"
                                        skin="normal-stringfy"
                                        style="padding: 0;"
                                    >
                                        {{operation}}
                                    </s-button>
                                </s-tooltip>
                            </s-menu-item>
                        </s-menu>
                        <s-button skin="stringfy">更多 <s-icon-down /></s-button>
                    </s-dropdown>
                </div>
                <div slot="empty">
                    <s-empty vertical on-click="onCreate"/>
                </div>
            </s-table>
            <!--分页显示及页面跳转-->
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
        <!--修改规则抽屉页-->
        <s-drawer
            open="{=changeRules=}"
            title="数据处理"
            class="drawer"
            direction="right"
        >
            <div>This is drawer content</div>
        </s-drawer>
    </div>`;

    initData() {
        return {
            searchbox: {
                keyword: '',
                placeholder: '请输入任务名称搜索',
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: columns,
                datasource: // TODO:假数据要删除
                [
                    {
                        connectorId: 'connect_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                        name: 'xxxxxxxx',
                        status: 'Running',
                        version: '3.9.1',
                        taskType: '集群迁移',
                        sourceName: 'source_kafka',
                        sourceIp: '********',
                        targetName: 'target_kafka',
                        targetIp: '********',
                        createTime: 1752570191000
                    },
                    {
                        connectorId: 'connect_aaaa',
                        name: '任务A',
                        status: 'Stopped',
                        version: '3.9.1',
                        taskType: '集群迁移',
                        sourceName: 'source_mysql',
                        sourceIp: '********1',
                        targetName: 'target_hive',
                        targetIp: '*********',
                        createTime: 1752570192000
                    },
                    {
                        connectorId: 'connect_bbbb',
                        name: '任务B',
                        status: 'Running',
                        version: '3.9.1',
                        taskType: '集群迁移',
                        sourceName: 'source_es',
                        sourceIp: '*********',
                        targetName: 'target_elasticsearch',
                        targetIp: '*********',
                        createTime: 1752570193000
                    }
                ],
            },
            selection: {
                mode: 'multi',
                selectedIndex: [],
                selectedItems: []
            },
            moreOperationData: ['删除', '停止', '恢复', '修改规则'],
            pager: {...PAGER_SUI},
            editName: this.editName.bind(this),
            check: this.check.bind(this),
            changeRules: false,
        };
    }

    static components = {
        'app-list-page': AppListPage,
        'list-title': ListTitle,
        's-search': Search,
        's-table': Table,
        's-button': Button,
        'create-btn': CreateBtn,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty,
        's-pagination': Pagination,
        'instant-editor': InstantEditor,
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-tooltip': Tooltip,
        's-drawer': Drawer,
        's-link': Link,
        'ellipsis-tip': EllipsisTip
    };


    async attached() {
        await this.getComList();
        timeCount = getTimer(() => this.getTableList());
    }

    onRefresh() {
        this.getComList();
        clearInterval(timeCount);
        timeCount = getTimer(() => this.getTableList());
    }

    detached() {
        clearInterval(timeCount);
    }

    /**
     * 获取表格数据
     */
    async getTableList() {
        // 等待 API 实现
        // const {searchbox, pager} = this.data.get('');
        // const params = {
        //     pageNo: pager.page,
        //     pageSize: pager.pageSize,
        //     keyword: searchbox.keyword,
        //     keywordType: searchbox.keywordType[0]
        // };
        // const {totalCount, result} = await api.getConnectTaskList(params);
        // this.data.set('pager.count', totalCount);
        // this.data.set('table.datasource', result);

        // 空数据
        this.data.set('pager.count', 0);
        // this.data.set('table.datasource', []);
    }

    /**
     * 创建任务，重定向到创建界面
     */
    async onCreate() {
        redirect(`#${ROUTE_PATH.connectCreate}`);
    }

    /**
     * 编辑连接器任务名称
     */
    async editName() {
        // TODO
    }

    /**
     * 连接器任务暂停
     */
    onPause(row: taskItem) {
        const dialog = new ConfirmDialog(
            {
                data:
                {
                    iconType: 'warning',
                    okText: '暂停',
                    title: '暂停任务',
                    content: `确定暂停任务“${row.name}”？`,
                }
            });
        dialog.attach(document.body);
        dialog.on('confirm', async () => {
            try {
                // await api.xxx(row);
                Notification.success('操作成功', {width: 400});
            }
            catch {
                Notification.error('操作失败，请重试', {title: '操作失败', width: 400});
            }

        });
    }

    /**
     * 连接器任务停止
     */
    onStop(row: taskItem) {
        const dialog = new ConfirmDialog(
            {
                data:
                {
                    iconType: 'warning',
                    okText: '停止',
                    title: '停止任务',
                    content: `确定停止任务“${row.name}”？`,
                }
            });
        dialog.attach(document.body);
        dialog.on('confirm', async () => {
            try {
                // TODO  await api.xxx(row);
                Notification.success('操作成功', {width: 400});
            }
            catch {
                Notification.error('操作失败，请重试', {title: '操作失败', width: 400});
            }

        });
    }

    /**
     * 连接器任务删除
     */
    onDelete(row: taskItem) {
        const dialog = new ConfirmDialog(
            {
                data:
                {
                    iconType: 'warning',
                    okText: '删除',
                    title: '删除任务',
                    content: `任务删除后不可恢复，确定删除任务“${row.name}”？`,
                }
            });
        dialog.attach(document.body);
        dialog.on('confirm', async () => {
            try {
                // TODO  await api.xxx(row);
                Notification.success('操作成功', {width: 400});
            }
            catch {
                Notification.error('操作失败，请重试', {title: '操作失败', width: 400});
            }

        });
    }

    /**
     * 连接器任务启动
     */
    onStart(row: taskItem) {
        const dialog = new ConfirmDialog(
            {
                data:
                {
                    iconType: 'info',
                    okText: '启动',
                    title: '启动任务',
                    content: `确定启动任务“${row.name}”？`,
                }
            });
        dialog.attach(document.body);
        dialog.on('confirm', async () => {
            try {
                // TODO  await api.xxx(row);
                Notification.success('操作成功', {width: 400});
            }
            catch {
                Notification.error('操作失败，请重试', {title: '操作失败', width: 400});
            }

        });
    }

    /**
     * 连接器任务恢复
     */
    async onRestart(row: taskItem) {
        const dialog = new ConfirmDialog(
            {
                data:
                {
                    iconType: 'info',
                    okText: '恢复',
                    title: '恢复任务',
                    content: `确定恢复任务“${row.name}”？`,
                }
            });
        dialog.attach(document.body);
        dialog.on('confirm', async () => {
            try {
                // TODO  await api.xxx(row);
                Notification.success('操作成功', {width: 400});
            }
            catch {
                Notification.error('操作失败，请重试', {title: '操作失败', width: 400});
            }

        });
    }

    /**
     * 连接器任务批量启动
     */
    onBatchStart() {
        const dialog = new BatchStart(
            {
                data:
                {
                    count: this.data.get('selection.selectedIndex.length'),
                    taskItem: this.data.get('selection.selectedItems')
                }
            });
        dialog.attach(document.body);
        dialog.on('confirm', async () => {
            try {
                // await api.startConnectTask(param);
                Notification.success('操作成功', {width: 400});
            }
            catch {
                Notification.error('操作失败，请重试', {title: '操作失败', width: 400});
            }

        });
    }

    /**
     * 任务搜索，支持搜索后自动刷新定时器重置
     */
    onSearch(): void {
        // 重置分页
        this.data.set('pager.page', 1);
        // 刷新数据
        this.onRefresh();
    }

    /**
     * 名称输入校验
     */
    check(name: string, callback: (str: string) => void) {
        if (!name) {
            return callback('请输入');
        }
        if (name.length > 65) {
            return callback('不能超过65个字符');
        }
        if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(name)) {
            return callback('输入字符格式有误');
        }
        callback('');
    }

    /**
     * 更多操作处理
     */
    handleMenuItemClick(row: taskItem, operation: string) {
        switch (operation) {
            case '修改规则':
                // 规则变更(控制抽屉页弹出)
                this.data.set('changeRules', true);
                break;
            case '恢复':
                this.onRestart(row);
                break;
            case '停止':
                this.onStop(row);
                break;
            case '删除':
                this.onDelete(row);
                break;
        }
    }

    /**
     * 多选框选择处理
     */
    onSelectChange(event: {value: {selectedIndex: number[], selectedItems: taskItem}}) {
        this.data.set('selection.selectedIndex', event.value.selectedIndex);
        this.data.set('selection.selectedItems', event.value.selectedItems);
    }

    /**
     * 分页处理
     */
    onPageChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', args.value.page);
        this.onRefresh();
    }

    /**
     * 分页页面显示条数更改处理
     */
    onPageSizeChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', 1);
        this.onRefresh();
    }
}
