/**
 * 二次确认弹窗
 *
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button} from '@baidu/sui';
import {debounce} from '@/common/decorator';

import './index.less';
const klass = 'confirm-dialog';

export default class extends Component {
    static template = html`
    <template>
        <s-dialog
            open="{{open}}"
            title="{{title}}"
            type="{{iconType}}"
            class="${klass}"
            width="350"
        >
            <div class="${klass}_subtitle">{{content}}</div>
            <div slot="footer">
                <s-button on-click="onClose" >取消</s-button>
                <s-button on-click="onOk" skin="primary">{{okText}}</s-button>
            </div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': <PERSON><PERSON>,
    };

    initData() {
        return {
            open: true,
            iconType: '',
            okText: '确定',
            title: '',
            content: '',
        };
    }

    @debounce(500)
    onOk() {
        this.fire('confirm', {});
        this.onClose();
    }

    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
