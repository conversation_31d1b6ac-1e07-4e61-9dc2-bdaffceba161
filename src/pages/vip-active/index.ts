/**
 * 专享版激活页
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {Notification, Alert} from '@baidu/sui';
import {AppOrderPage} from '@baidu/sui-biz';
import {Component} from 'san';
import {kafkaRoleInfo} from '@/common/config';
import {DOCS_LINK, ROUTE_PATH} from '@/common/config';
import api from '@/common/client';

import './index.less';
const klass = 'bms-active';

const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
const isPrivate = $flag.KafkaPublicTopicMonitor;

@decorators.asPage(ROUTE_PATH.vipActive)
export default class extends Component {
    static template = html`
    <div class="${klass}">
        <div class="${klass}-order-container">
            <div class="${klass}_verify_tip" s-if="{{!isVerify && !isPrivate && !loadingAccountInfo}}">
                <s-alert skin="warning">
                    产品开通提示：如需要开通消息服务，请先进行<a
                        href="/qualify/#/qualify/index"
                        target="blank">实名认证</a>，本产品开通并授权后才会开始服务计费。
                </s-alert>
            </div>
            <s-order-page
                class="${klass}-s-order-page"
                title="{{title}}"
                bgImgSrc="{{bgImgSrc}}"
                advantage="{{advantage}}"
                useNewVersion="{{true}}"
                openBtnDisabled="{{openBtnDisabled}}"
                on-click="onOrderCreate"
            >
                <div slot="desc" class="desc">{{descText | raw}}</div>
            </s-order-page>
        </div>
    </div>`;

    static components = {
        's-order-page': AppOrderPage,
        's-alert': Alert
    };

    static computed: SanComputedProps = {
        activeText() {
            return this.data.get('accountType') === 'enterprise' ? '企业认证' : '个人认证';
        },
        openBtnDisabled() {
            const notVerify = !this.data.get('isVerify') && !isPrivate;
            const loadingAccountInfo = this.data.get('loadingAccountInfo');
            return notVerify && !loadingAccountInfo;
        },
        descText() {
            const des1 = `消息服务for Kafka 是全兼容Apache Kafka的分布式、高可扩展、高通量的托管消息队列服务，您可以直接享用Kafka带来的先进功能而无需考虑集群运维，并按照使用量付费、即插即拔的方式，让您用最低的成本，享受最优质的消息服务。详请查看<a href="${DOCS_LINK.kafkaVipHelp}" class="ml4">帮助文档<a/>`;
            const des2 = '百度' + des1;
            return isXushang ? des1 : des2;
        },
        title() {
            const title1 = '消息服务 for Kafka 高吞吐、可扩展的分布式消息服务';
            const title2 = '百度' + title1;
            return isXushang ? title1 : title2;
        }
    };

    initData() {
        return {
            isVerify: false,
            loadingAccountInfo: false,
            bgImgSrc: require('../../static/background.png'),
            advantage: {
                content: [
                    {
                        title: "开箱即用",
                        desc: "100%兼容开源社区Kafka，开通服务后便能创建Kafka主题，并利用Kafka客户端与消息服务通讯。",
                        imgSrc: require('../../static/vip1.png'),
                    },
                    {
                        title: "实时处理",
                        desc: "您可以连续收集海量数据，以方便对事件、设备、业务等做出实时反应。",
                        imgSrc: require('../../static/vip2.png'),
                    },
                    {
                        title: "弹性计算",
                        desc: "消息服务可以按需扩容，在灵活方便的同时大大降低了用户使用成本。",
                        imgSrc: require('../../static/vip3.png'),
                    },
                    {
                        title: "高可用性",
                        desc: "消息服务 提供SLA保证服务的可用性。此外，数据提供多副本的方式保证数据的持久性。",
                        imgSrc: require('../../static/vip4.png'),
                    }
                ]
            },
        };
    }

    inited() {
        this.getAccount();
    }


    // 获取用户实名认证信息
    getAccount() {
        this.data.set('loadingAccountInfo', true);
        api.iamAccountDetail()
            .then((target: {accountType: 'individual' | 'enterprise'}) => {
                    this.data.set('accountType', target.accountType);
                    this.data.set('isVerify',
                        target.accountType === 'enterprise'
                            ? this.$context.isEnterprise()
                            : this.$context.isVerifyUser());
                    this.data.set('loadingAccountInfo', false);
            })
            .catch((e: Error) => this.data.set('loadingAccountInfo', false));
    }

    // 服务开通
    async onOrderCreate() {
        try {
            const params = {
                roleName: kafkaRoleInfo.roleName,
                accountId: window.$context.getUserId(),
                policyId: kafkaRoleInfo.policyId,
                serviceId: kafkaRoleInfo.serviceId
            };
            await api.kafkaRoleActivate(params);
            Notification.success('开通成功');
            redirect(`#${ROUTE_PATH.clusterList}`);
        }
        catch (e) {
            Notification.error('开通失败');
        }
    }
}
