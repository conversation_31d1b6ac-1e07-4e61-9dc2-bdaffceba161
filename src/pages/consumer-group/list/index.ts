/**
 * 消费组管理
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {Dialog, Pagination, Table, Button, Notification} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import {OutlinedPlus, OutlinedRefresh} from '@baidu/sui-icon';

import api from '@/common/client';
import {TABLE_SUI, PAGER_SUI, ROUTE_PATH, CONFIG_TYPE} from '@/common/config';
import {pickEmpty, formatTime} from '@/common/util';
import checkActive from '@/common/check-active';
import Permission from '@/common/permission';
import DrawerTable from '@/components/common-table/drawer-table';
import ViewCert from '@/components/view-cert';
import CreateBtn from '@/components/create-btn';
import ListTitle from '@/components/list-title';

import CreateConsumer from './create-consumer';
import ResetSite from './reset-site';
import Subscription from './subscription';
import HelpDoc from '@/components/help-doc';

const klass = 'bms-consumergroup-list';

type OpsType = 'show-cert' | 'reset-site' | 'show-subscription' | 'delete';

type ConsumerItem = {
    clusterId?: string;
    consumerGroupName: string;
    createAt: string;
    operation: string;
};

@decorators.asPage(ROUTE_PATH.consumergroup)
@decorators.withSidebar({active: ROUTE_PATH.consumergroup})
export default class extends DrawerTable {
    static template = html`
    <div class="${klass} bms-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <list-title title="消费组列表" type="${CONFIG_TYPE.ORDINARY}" hasOnlineAlert="{{false}}" />
            </div>
            <div slot="bulk">
                <create-btn text="创建消费组" on-click="onCreate" />
            </div>
            <div slot="filter">
                <s-searchbox
                    class="searchbox"
                    placeholder="请输入消费组名称进行搜索"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="{{170}}"
                >
                    <div slot="prefix"></div>
                </s-searchbox>
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
            >
                <div slot="c-operation">
                    <span class="operation">
                        <s-button on-click="onOperate('show-cert', row, $event)" skin="stringfy" class="table-btn-slim">
                            查看证书
                        </s-button>
                        <s-button
                            on-click="onOperate('show-subscription', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim"
                        >
                            订阅详情
                        </s-button>
                        <s-button
                            on-click="onOperate('reset-site', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{row.topicCount | canResetSite}}"
                        >
                            重置位点
                        </s-button>
                        <s-button on-click="onOperate('delete', row, $event)" skin="stringfy" class="table-btn-slim">
                            删除消费组
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty vertical on-click="onCreate" />
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        'help-doc': HelpDoc,
        'create-btn': CreateBtn,
        's-icon-plus': OutlinedPlus,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty,
        'list-title': ListTitle
    };

    initData() {
        return {
            searchbox: {
                keyword: '',
                keywordType: ['ConsumerGroupName']
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {name: 'consumerGroupName', label: '消费组名称'},
                    {
                        name: 'createAt',
                        label: '创建时间',
                        render: (item: ConsumerItem) => formatTime(item.createAt)
                    },
                    {name: 'operation', label: '操作', width: 270}
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        canResetSite(topicCount: number) {
            return !Permission.CONSUMERGROUP.canResetSite(topicCount);
        }
    };

    attached() {
        this.getComList();
    }

    async getTableList() {
        const {searchbox, pager} = this.data.get('');
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keyword: searchbox.keyword,
            keywordType: searchbox.keywordType[0]
        });
        const {totalCount, result} = await api.bpsConsumerGroupList(params);
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result);
    }

    // 操作
    onOperate(type: OpsType, row: ConsumerItem, event: Event) {
        const baseParam = {
            consumerGroupName: row.consumerGroupName,
            clusterId: row.clusterId
        };
        switch (type) {
            case 'delete':
                this.deleteConsumer(row);
                break;
            case 'show-subscription': {
                event.stopPropagation();
                this.drawerDialog = new Subscription({data: baseParam});
                this.drawerDialog.attach(document.body);
                break;
            }
            case 'reset-site': {
                const dialog = new ResetSite({data: baseParam});
                dialog.attach(document.body);
                dialog.on('success', () => this.getComList());
                break;
            }
            case 'show-cert': {
                event.stopPropagation();
                this.drawerDialog = new ViewCert({
                    data: {
                        ...baseParam,
                        name: baseParam.consumerGroupName,
                        type: 'consumer'
                    }
                });
                this.drawerDialog.attach(document.body);
                break;
            }
        }
    }

    // 创建消费组
    onCreate() {
        const dialog = new CreateConsumer();
        dialog.attach(document.body);
        dialog.on('success', () => this.resetTable());
    }

    // 删除消费组
    deleteConsumer(row: ConsumerItem) {
        Dialog.warning({
            content: `选中的消费组删除后无法恢复，请确定是否要删除消费组"${row.consumerGroupName}"?`,
            okText: '删除',
            title: '确定删除消费组吗？',
            // @ts-ignore
            onOk: async () => {
                await api.bpsConsumerGroupDelete({
                    consumerGroupName: row.consumerGroupName,
                    cluster: row.clusterId
                });
                Notification.success('删除成功');
                this.resetTable();
            },
            showCancel: true
        });
    }

    // region 切换
    onRegionChange() {
        checkActive().then(() => this.getComList());
    }
}
