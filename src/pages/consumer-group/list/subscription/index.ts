/**
 * 订阅详情
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {Dialog, Form, Table} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz'
import {ServiceFactory} from '@baiducloud/runtime';
import api from '@/common/client';
import {TABLE_COLUMNS} from '@/common/config';
import DrawerCommon from '@/components/drawer-common';
import EllipsisTip from '@/components/ellipsis-tip';

import './index.less';
const $flag = ServiceFactory.resolve('$flag');
const KafkaPublicTopicMonitor = $flag.KafkaPublicTopicMonitor;
// 这里需要用到深克隆
let partitionColumns = _.cloneDeep(TABLE_COLUMNS.partitionColumns);
partitionColumns[0].width = '70';
partitionColumns[1].width = '110';
partitionColumns[2].width = '80';
partitionColumns[3].width = '70';
partitionColumns[4].width = '70';

export default class extends Component {
    static template = // html
    `<template>
        <drawer-common
            title="订阅详情"
            class="subscription-detail"
            on-close="onClose"
            showEdit="{{false}}"
            size="{{600}}"
            showTip="{{false}}"
        >
            <div class="topic-title">主题信息</div>
            <s-table
                s-ref="table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-exprow-collapse="onRowCollapse"
                on-exprow-expand="onRowExpand">
                <div slot="c-topicName">
                    <ellipsis-tip text="{{row.topicName}}" placement="top" />
                </div>
                <div slot="c-operation">
                    <a class="link a-btn" on-click="onDetail(row, rowIndex, $event)">查看详情</a>
                </div>
                <div slot="sub-detail">
                    <s-table
                        loading="{{row.subTable.loading}}"
                        columns="{{row.subTable.columns}}"
                        datasource="{{row.subTable.datasource}}"
                    />
                </div>
                <div slot="empty">
                    <s-empty
                        actionText=""
                        vertical
                    />
                </div>
            </s-table>
            <div slot="footer" />
        </s-dialog>
    </template>`

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-table': Table,
        'drawer-common': DrawerCommon,
        'ellipsis-tip': EllipsisTip,
        's-empty': Empty
    }

    initData() {
        return {
            open: true,
            confirming: false,
            table: {
                loading: true,
                datasource: [],
                columns: [
                    {name: 'topicName', label: '主题名称'},
                    {name: 'partition', label: '分区数', width: 80},
                    {name: 'operation', label: '操作', width: 80}
                ]
            }
        };
    }

    attached() {
        this.getTopicList();
    }

    /**
     * 表格的折叠
     */
    onRowCollapse(target: {value: {expandedIndex: number, rowIndex: number}}) {
        this.data.set(`table.datasource[${target.value.rowIndex}].subTable.datasource`, []);
    }

    /**
     * 表格的展开
     */
    onRowExpand(target: {value: {expandedIndex: number, rowIndex: number}}) {
        this.getTopicDetail(target.value.rowIndex);
    }

    /**
     * 获取消费组主题
     */
    async getTopicList() {
        try {
            const data = await api.bpsConsumerGroupTopics({consumerGroupName: this.data.get('consumerGroupName')});
            let topics: Array<{topicName: string, partition: number}> = [];
            if (KafkaPublicTopicMonitor) {
                _.each(data.topics, item => {
                    topics.push({topicName: item.topicName, partition: item.partitionCount});
                });
            }
            else {
                topics = Object.keys(data).map(key => ({
                    topicName: key,
                    partition: data[key]
                }));
            }
            this.data.set('table.datasource', _.map(topics, item => ({
                ...item,
                subTable: {
                    datasource: [],
                    loading: false,
                    columns: partitionColumns
                },
                subSlot: 'sub-detail'
            })));
            this.data.set('table.loading', false);
        } catch (error) {
            this.data.set('table.loading', false);
        }
    }

    /**
     * 获取主题详情信息
     */
    async getTopicDetail(rowIndex: number) {
        try {
            this.data.set(`table.datasource[${rowIndex}].subTable.loading`, true);
            const data = await api.bpsGroupPart({
                topic: this.data.get(`table.datasource[${rowIndex}].topicName`),
                groupId: this.data.get('consumerGroupName')
            });
            this.data.merge(`table.datasource[${rowIndex}].subTable`, {
                datasource: data,
                loading: false
            });
        } catch (error) {
            this.data.set(`table.datasource[${rowIndex}].subTable.loading`, false);
        }
    }

    /**
     * 详情点击
     */
    onDetail(row: Object, rowIndex: string, e: Event) {
        // @ts-ignore
        this.ref('table')?.toggleExpandRow(e, rowIndex);
    }

    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
