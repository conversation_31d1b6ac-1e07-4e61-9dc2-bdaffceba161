/**
 * 重置位点
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {
    Dialog,
    Form,
    Notification,
    Select,
    Radio,
    InputNumber,
    DatePicker,
    Tooltip
} from '@baidu/sui';
import moment from 'moment';
import {ServiceFactory} from '@baiducloud/runtime';
import api from '@/common/client';
import {VAILDITE_ITEMS} from '@/common/rules';
import {pickEmpty} from '@/common/util';
import {PAGER_MAX} from '@/common/config';

import './index.less';
const $flag = ServiceFactory.resolve('$flag');
const KafkaPublicTopicMonitor = $flag.KafkaPublicTopicMonitor;
const klass = 'reset-site';

const template =
`<template>
    <s-dialog
        title="重置位点"
        okText="确定"
        class="${klass} ue-dialog"
        open="{= open =}"
        on-confirm="onConfirm"
        on-close="onClose"
        confirming="{{confirming}}"
        loadingAfterConfirm="{{false}}">
        <s-form
            s-ref="form"
            data="{= formData =}"
            rules="{{rules}}">
            <s-form-item prop="topicName" label="主题列表：">
                <s-select
                    width="400"
                    value="{= formData.topicName =}"
                    on-change="onTopicChange"
                    filterable
                    width="320"
                >
                    <s-tooltip
                        s-for="item in topics"
                        content="{{item.tip}}"
                        placement="right">
                        <s-option
                            value="{{item.value}}"
                            label="{{item.label}}"
                        />
                    </s-tooltip>
                </s-select>
            </s-form-item>
            <s-form-item prop="partitions" label="分区列表：">
                <s-select
                    width="400"
                    multiple
                    checkAll
                    datasource="{{partitions}}"
                    value="{= formData.partitions =}"
                />
            </s-form-item>
            <s-form-item prop="resetStrategy" label="重置策略：" class="${klass}--reset-strategy">
                 <s-radio-group
                    value="{= formData.resetStrategy =}"
                    radioType="radio"
                    name="reset-strategy"
                    on-change="onPreCheckResetStrategy"
                >
                    <s-radio label="重置到最早位点" value="toearliest" />
                    <s-radio label="重置到最新位点" value="tolatest" />
                    <s-radio value="tooffset">
                        重置到指定位点
                        <s-input-number
                            disabled="{{formData.resetStrategy !== 'tooffset'}}"
                            class="${klass}--reset-strategy_tooffset-input"
                            value="{= formData.tooffset =}"
                            min="1"
                        />
                    </s-radio>
                    <s-radio value="todatetime">
                        重置到指定时间点
                        <s-date-picker
                            disabled="{{formData.resetStrategy !== 'todatetime'}}"
                            class="${klass}--reset-strategy_time"
                            value="{= formData.todatetime =}"
                            on-change="onTodatetimeChange"
                            mode="second"
                            getPopupContainer="{{getPopupContainer}}"
                        />
                    </s-radio>
                    <s-radio value="shiftby">
                        位点向前或向后移动若干条
                        <s-input-number
                            disabled="{{formData.resetStrategy !== 'shiftby'}}"
                            class="${klass}--reset-strategy_shiftby"
                            value="{= formData.shiftby =}"
                            on-change="onShiftbyChange"
                            on-input="onShiftbyChange"
                        />
                    </s-radio>
                </s-radio-group>
                <p class="error" s-if="{{error.resetStrategy}}">{{error.resetStrategy}}</p>
            </s-form-item>
        </s-form>
    </s-dialog>
</template>`;

export default class extends Component {
    static template = template;

    static components = {
        's-dialog': Dialog,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-input-number': InputNumber,
        's-date-picker': DatePicker,
        's-option': Select.Option,
        's-tooltip': Tooltip
    };

    initData() {
        return {
            open: true,
            topics: [],
            partitions: [],
            formData: {
                resetStrategy: 'toearliest',
                tooffset: 1,
                shiftby: 1,
                todatetime: null
            },
            rules: {
                topicName: [VAILDITE_ITEMS.requiredSelect],
                partitions: [VAILDITE_ITEMS.requiredSelect],
                resetStrategy: [VAILDITE_ITEMS.requiredSelect]
            },
            getPopupContainer: () => document.body
        };
    }

    attached() {
        this.getTopicList();
    }

    // 时间筛选改变
    onTodatetimeChange() {
        // 使用nextTick等待时间值设置完成后做校验
        this.nextTick(() => this.onCheckResetStrategy({value: this.data.get('formData.resetStrategy')}));
    }

    // 重置策略校验预处理
    onPreCheckResetStrategy(target: {value: string}) {
        if (target.value === 'todatetime') {
            return;
        }
        this.onCheckResetStrategy(target);
    }

    // 这里校验可以稍微完善下
    onCheckResetStrategy(target: {value: string}) {
        const {todatetime} = this.data.get('formData');
        // value 等于 resetStrategy 是针对 value 为空情况
        const bool = target.value === 'todatetime' && !todatetime;
        const tip = bool ? '请选择时间' : '';
        this.data.set('error.resetStrategy', tip);
        if (bool) {
            throw tip;
        }
        return Promise.resolve();
    }

    // 校验
    async validate() {
        await Promise.all([
            (this.ref('form') as unknown as Form).validateFields(),
            this.onCheckResetStrategy({value: this.data.get('formData.resetStrategy')})
        ]);
    }

    async onConfirm() {
        try {
            await this.validate();
            this.data.set('confirming', true);
            const {consumerGroupName, clusterId, formData, mode} = this.data.get('');
            let {
                topicName,
                partitions,
                resetStrategy,
                todatetime
            } = this.data.get('formData');
            let resetValue: number | string = '';
            switch (resetStrategy) {
                case 'todatetime':
                    todatetime.setMilliseconds(0);
                    resetValue = moment(todatetime, 'YYYY-MM-DDTHH:mm:ss.SSS');
                    break;
                case 'tooffset':
                case 'shiftby':
                    resetValue = +(formData[resetStrategy]);
                    break;
            }
            const topic = _.find(this.data.get('topics'), item => (
                item.value === formData.topicName
            ));
            let params = {
                consumerGroupName,
                topicName,
                partitions,
                resetStrategy,
                ...(resetValue ? {resetValue} : {}),
            };
            // 独享版
            if (mode === 'cluster') {
                delete params.consumerGroupName;
                await api.resetClusterOffset(clusterId, consumerGroupName, params);
            }
            // 共享版
            else {
                if (KafkaPublicTopicMonitor) {
                    params = {
                        ...params,
                        cluster: topic?.cluster,
                    };
                }
                else {
                    params = {
                        ...params,
                        clusterId
                    };
                }
                await api.bpsConsumerGroupResetOffset(pickEmpty(params));
            }

            Notification.success('重置位点成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (err) {
            this.data.set('confirming', false);
        }
    }

    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    // 位点移动值变化
    onShiftbyChange(target: {value: number | string}) {
        target.value = +(target.value);
        const oldValue = this.data.get('formData.shiftby');
        const isBig = target.value > oldValue;
        if (target.value === 0) {
            this.nextTick(() => this.data.set('formData.shiftby', isBig ? 1 : -1));
        }
    }

    // 获取主题信息
    async getTopicList() {
        const {mode, clusterId, consumerGroupName} = this.data.get('');
        let topics: Array<{text: string, value: string, cluster: string, tip: string, partitionCount: number}> = [];
        if (mode === 'cluster') {
            const {result} = await api.getClusterConsumerGroupDetail(clusterId, consumerGroupName, {...PAGER_MAX});
            topics = _.map(result, item => ({text: item, value: item, tip: item}));
        }
        else if (!KafkaPublicTopicMonitor) {
            const data = await api.bpsConsumerGroupTopics({consumerGroupName});
            _.each(data, (item, key) => {
                topics.push({text: key, value: key, tip: key});
            });
            this.data.set('partitionsMap', data);
        }
        else {
            const data = await api.bpsConsumerGroupTopics({consumerGroupName});
            _.each(data.topics, item => {
                topics.push({text: item.topicName, value: item.topicName, cluster: item.cluster, tip: '集群：' + item.cluster, partitionCount: item.partitionCount});
            });
        }

        this.data.set('topics', topics);
    }

    // 主题切换
    async onTopicChange(target: {value: string}) {
        let partitions;
        const {mode, clusterId, consumerGroupName} = this.data.get('');
        // 独享版
        if (mode === 'cluster') {
            const data = await api.listClusterSubscribedTopicPartitions(clusterId, consumerGroupName, target.value, {});
            partitions = _.map(data, i => ({value: i, text: i}));
        }
        else if (!KafkaPublicTopicMonitor) {
            const partitionsMap = this.data.get('partitionsMap');
            const num = partitionsMap[target.value];
            let arr = [];
            let index = 0;
            while (index < num) {
                arr.push(index++);
            }
            partitions = _.map(arr, i => ({value: i, text: i}));
        }
        else {
            const topic = _.find(this.data.get('topics'), item => (
                item.value === target.value
            ));
            const num = topic?.partitionCount || 0;
            let arr = [];
            let index = 0;
            while (index < num) {
                arr.push(index++);
            }
            partitions = _.map(arr, i => ({value: i, text: i}));
        }
        this.data.set('partitions', partitions);
        // nextTick 解决设置 partitions，由于立即设置 formData.partitions 的全选问题
        this.nextTick(() => this.data.set('formData.partitions', []));
    }
};
