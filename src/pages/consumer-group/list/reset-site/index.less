/**
 * @file index.less
 * <AUTHOR>
 */

.reset-site {

    &--reset-strategy {
        margin-bottom: 0;
        .s-form-item-label {
            line-height: inherit;
        }

        .s-radio-group {

            .s-radio {
                display: block;
                margin-bottom: 10px;
                height: 30px;
                cursor: pointer;
                margin-right: 20px;
                line-height: 30px;
            }
            .s-radio:last-child {
                margin-bottom: 0;
            }
        }


        &_tooffset-input,
        &_time,
        &_shiftby {
            margin-left: 10px !important;
        }

        .error {
            color: #d0021b;
        }
    }

    .s-select {

        .s-trigger-container {
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .s-checkbox {
            display: inline-block;
        }
    }
}
