/**
 * 创建消费组
 *
 * @file create-consumer.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Input, Form, Notification} from '@baidu/sui';
import {Tip} from '@baidu/sui-biz';

import {DIALOG_INPUT_WIDTH} from '@/common/config';
import {VAILDITE_ITEMS, RULES} from '@/common/rules';
import api from '@/common/client';

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="create-consumer ue-dialog"
            title="创建消费组"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <s-form
                s-ref="form"
                rules="{{rules}}"
                class="form-format"
                required="{{true}}"
                data="{= formData =}">
                <s-form-item
                    class="form-item-center"
                    label="Account ID：">
                    {{userId}}
                </s-form-item>
                <s-form-item
                    label="消费组名称："
                    prop="consumerGroupName">
                    <s-input
                        value="{= formData.consumerGroupName =}"
                        width="{{inputWidth}}"
                        placeholder="请输入消费组名称"
                    />
                    <p class="desc mt4">大小写字母、数字以及_.-特殊字符，长度1-125</p>
                </s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-input': Input,
        's-form': Form,
        's-form-item': Form.Item
    };

    initData() {
        return {
            rules: {
                consumerGroupName: [
                    VAILDITE_ITEMS.requiredInput,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (!RULES.consumerName.test(value)) {
                                return callback('输入格式错误');
                            }
                            callback();
                        }
                    }
                ]
            },
            open: true,
            userId: this.$context.getUserId(),
            inputWidth: DIALOG_INPUT_WIDTH,
        };
    }

    // 确认
    async onConfirm() {
        try {
            await (this.ref('form') as unknown as Form).validateFields();
            this.data.set('confirming', true);
            const {formData} = this.data.get('');
            // 共享版
            await api.bpsConsumerGroupCreate(formData);
            Notification.success('保存成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
