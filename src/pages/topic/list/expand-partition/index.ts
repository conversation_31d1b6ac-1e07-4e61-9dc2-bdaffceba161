/**
 * 扩容分区
 *
 * @file expand-partition.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Button, Notification, InputNumber, Alert} from '@baidu/sui';

import {CILENT_SILENT} from '@/common/config';
import api from '@/common/client';
import Tip from '@/components/tip';
import {getName} from '@/common/util';

import './index.less';

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="expand-partition ue-dialog-shot"
            title="扩容分区"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <s-form class="form-format">
                <s-alert skin="warning">
                    温馨提示：主题分区增加后不能缩减，请谨慎操作。
                </s-alert>
                <s-form-item label="主题名称：" class="form-item-center">{{name | getName}}</s-form-item>
                <s-form-item label="新增个数：" class="form-item-center">
                    <span slot="label">
                        新增个数：
                        <tip-cmpt>
                            分区个数决定了主题的吞吐，生产者发生的消息将被写入不同分区，被若干消费者同时处理。
                            <br />
                            <a href="https://cloud.baidu.com/doc/Kafka/index.html" target="blank">
                                了解更多
                            </a>
                        </tip-cmpt>
                    </span>
                    <s-input-number value="{= partitionCount =}" max="{{max}}" min="{{1}}" />
                </s-form-item>
                <s-form-item label="所属集群：" class="form-item-center">{{cluster}}</s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-input-number': InputNumber,
        'tip-cmpt': Tip,
        's-alert': Alert
    };

    initData() {
        return {
            rules: {
                topicName: [],
                cluster: []
            },
            open: true,
            partitionCount: 1,
            max: 1
        }
    }

    static filters = {
        getName
    }

    attached() {
        this.getQuote();
    }

    // 获取分区个数
    async getQuote() {
        const {partition} = await api.bpsQuota({}, CILENT_SILENT);
        const currentPartitionCount = this.data.get('currentPartitionCount');
        const count = partition - currentPartitionCount;
        this.data.set('max', count > 0 ? count : 1);
    }


    // 确认
    async onConfirm() {
        try {
            this.data.set('confirming', true);
            const {partitionCount, cluster, name} = this.data.get();
            await api.bpsPartitionAdd({
                cluster,
                name,
                partitionCount
            });
            Notification.success('保存成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
