/**
 * 创建主题
 *
 * @file create-topic.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Input, Form, Select, Button, Notification, InputNumber} from '@baidu/sui';

import Tip from '@/components/tip';
import {DIALOG_INPUT_WIDTH, CILENT_SILENT} from '@/common/config';
import {VAILDITE_ITEMS, RULES, TopicNameRegTip} from '@/common/rules';
import api from '@/common/client';

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="create-topic ue-dialog"
            title="创建主题"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <s-form
                s-ref="form"
                rules="{{rules}}"
                class="form-format"
                required="{{true}}"
                data="{= formData =}">
                <s-form-item
                    class="form-item-center"
                    label="主题前缀：">
                    {{userId}}
                </s-form-item>
                <s-form-item
                    prop="topicName"
                    label="主题名称：">
                    <s-input
                        value="{= formData.topicName =}"
                        placeholder="请输入主题名称"
                        width="{{inputWidth}}"
                    />
                    <p class="desc mt4">${TopicNameRegTip}</p>
                </s-form-item>
                <s-form-item
                    prop="partitionCount"
                    class="form-item-center">
                    <span slot="label" >
                        分区个数：
                        <tip-cmpt>
                            <div>
                                分区个数决定了主题的吞吐，生产者发生的消息将被写入不同分区，被若干消费者同时处理。
                                <br />
                                <a class="a-btn" href="https://cloud.baidu.com/doc/Kafka/index.html" target="blank">
                                    了解更多
                                </a>
                            </div>
                        </tip-cmpt>
                    </span>
                    <s-input-number value="{= formData.partitionCount =}" max="{{max}}" min="{{1}}" />
                </s-form-item>
                <s-form-item
                    prop="cluster"
                    label="所属集群：">
                    <s-select width="180" datasource="{{clusterList}}" value="{= formData.cluster =}" />
                </s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-input-number': InputNumber,
        'tip-cmpt': Tip
    };

    initData() {
        return {
            rules: {
                topicName: [
                    VAILDITE_ITEMS.requiredInput,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (!RULES.topicName.test(value)) {
                                return callback('输入格式错误');
                            }
                            callback();
                        }
                    }
                ]
            },
            open: true,
            userId: this.$context.getUserId(),
            inputWidth: DIALOG_INPUT_WIDTH,
            formData: {
                partitionCount: 1
            },
            max: 1,
            clusterList: []
        }
    }

    attached() {
        this.getQuote();
        this.getClusterList();
    }

    // 获取最大分区数
    async getQuote() {
        const {partition} = await api.bpsQuota({}, CILENT_SILENT);
        this.data.set('max', partition);
    }

    // 获取集群列表
    async getClusterList() {
        const data = await api.bpsTopicCluster({}, CILENT_SILENT);
        const defaultCluster = 'KAFKA_01';
        const defalutList = [{value: defaultCluster, text: defaultCluster}];
        const arr = data && data.length
            ? _.map(data, i => ({value: i.cluster, text: i.cluster}))
            : defalutList;
        this.data.set('clusterList', arr);
        this.data.set('formData.cluster', arr[0].value);
    }

    // 确认
    async onConfirm() {
        try {
            await this.ref('form')?.validateFields();
            this.data.set('confirming', true);
            const formData = this.data.get('formData');
            await api.bpsTopicCreate(formData);
            Notification.success('保存成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}