/**
 * 主题列表
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {Dialog, Pagination, Table, Button, Notification} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';

import api from '@/common/client';
import {TABLE_SUI, PAGER_SUI, ROUTE_PATH, CONFIG_TYPE} from '@/common/config';
import {pickEmpty, formatTime, getName} from '@/common/util';
import checkActive from '@/common/check-active';
import DrawerTable from '@/components/common-table/drawer-table';
import {renderStatus} from '@/common/html';
import {TopicStatus} from '@/common/enums';
import ViewCert from '@/components/view-cert';
import CreateBtn from '@/components/create-btn';
import ListTitle from '@/components/list-title';

import CreateTopic from './create-topic';
import ExpandPartition from './expand-partition';
import EllipsisTip from '@/components/ellipsis-tip';
import HelpDoc from '@/components/help-doc';

const klass = 'bms-topic-list';

type OpsType = 'expand' | 'delete' | 'show-cert';

type TopicItem = {
    name: string
    status: string
    cluster: string
    createdAt: string
    partitionCount: number
    operation: string
};

@decorators.asPage(ROUTE_PATH.topic)
@decorators.withSidebar({active: ROUTE_PATH.topic})
export default class TopicList extends DrawerTable {

    static template = html`
    <div class="${klass} bms-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <list-title
                    title="主题列表"
                    type="${CONFIG_TYPE.ORDINARY}"
                    hasOnlineAlert
                />
            </div>
            <div slot="bulk">
                <create-btn text="创建主题" on-click="onCreate" />
            </div>
            <div slot="filter">
                <s-searchbox
                    class="searchbox"
                    placeholder="请输入主题名称进行搜索"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="{{170}}"
                >
                    <div slot="prefix"></div>
                </s-searchbox>
                <s-button on-click="getComList" class="s-icon-button ml5">
                    <s-icon-refresh class="button-icon" />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}">
                <div slot="c-name">
                    <a
                        href="#${ROUTE_PATH.monitor}?topicName={{row.name}}"
                        class="a-btn">
                        <ellipsis-tip text="{{row.name}}" placement="bottom" />
                    </a>
                </div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            on-click="onOperate('expand', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{row.status !== 'Normal'}}">
                            扩容分区
                        </s-button>
                        <s-button
                            on-click="onOperate('delete', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{row.status !== 'Normal'}}">
                            删除主题
                        </s-button>
                        <s-button
                            on-click="onOperate('show-cert', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{row.status !== 'Normal'}}">
                            查看证书
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty vertical on-click="onCreate"/>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        'ellipsis-tip': EllipsisTip,
        'help-doc': HelpDoc,
        'create-btn': CreateBtn,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty,
        'list-title': ListTitle
    };

    initData() {
        return {
            searchbox: {
                keyword: '',
                keywordType: ['TopicName']
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {name: 'name', label: '主题名称', minWidth: 150},
                    {
                        name: 'status',
                        label: '状态',
                        render: (item: TopicItem) => {
                            return renderStatus(TopicStatus.fromValue(item.status));
                        }
                    },
                    {name: 'cluster', label: '所属集群'},
                    {name: 'partitionCount', label: '分区', width: 70},
                    {
                        name: 'createdAt',
                        label: '创建时间',
                        width: 150,
                        render: (item: TopicItem) => formatTime(item.createdAt)
                    },
                    {name: 'operation', label: '操作', width: 200}
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        getName
    };

    attached() {
        this.getComList();
    }

    async getTableList() {
        const {searchbox, pager} = this.data.get('');
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keyword: searchbox.keyword,
            keywordType: searchbox.keywordType[0],
        });
        const {totalCount, result} = await api.bpsTopicList(params);
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result);
    }

    // 操作
    onOperate(type: OpsType, row: TopicItem, event: Event) {
        switch (type) {
            case 'delete':
                this.deleteTopic(row);
                break;
            case 'expand':
                this.expand(row);
                break;
            case 'show-cert':
                this.showCert(row, event);
                break;
        }
    }

    // 创建主题
    async onCreate() {
        this.showCreateTopicDialog();
    }

    // 创建主题弹窗
    showCreateTopicDialog() {
        const dialog = new CreateTopic();
        dialog.attach(document.body);
        dialog.on('success', () => this.resetTable());
        return dialog;
    }

    // 删除主题
    deleteTopic(row: TopicItem) {
        Dialog.warning({
            content: `选中的主题删除后无法恢复，请确定是否要删除主题"${row.name}"?`,
            okText: '删除',
            title: '确定删除主题吗？',
            onOk: async () => {
                await api.bpsTopicDelete({
                    cluster: row.cluster,
                    topicName: row.name
                });
                Notification.success('删除成功');
                this.resetTable();
            },
            showCancel: true,
        });
    }

    // 扩容
    expand(row: TopicItem) {
        const dialog = new ExpandPartition({
            data: {
                cluster: row.cluster,
                name: row.name,
                currentPartitionCount: row.partitionCount
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.getComList());
        return dialog;
    }

    // 查看证书
    showCert(row: TopicItem, event: Event) {
        event.stopPropagation();
        this.drawerDialog = new ViewCert({
            data: {
                cluster: row.cluster,
                name: row.name
            }
        });
        this.drawerDialog.attach(document.body);
    }

    // region 切换
    onRegionChange() {
        checkActive().then(() => {
            this.getComList();
        });
    }
}
