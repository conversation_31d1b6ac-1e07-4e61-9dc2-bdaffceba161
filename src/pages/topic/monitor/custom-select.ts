/**
 * 自定义下拉框
 *
 * @file custom-select.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component, SanComponent} from 'san';
import {Select, Tooltip} from '@baidu/sui';
import {html} from '@baiducloud/runtime';

export default class extends Component {
    _onEnter?: EventListenerOrEventListenerObject;
    _enterAction: Boolean = false;
    _temp: string | undefined;

    static template = html`
    <span class="custom-select">
        <s-select
            s-ref="select"
            filterable
            placeholder="{{placeholder}}"
            value="{= value =}"
            noMatchText="{{noMatchText}}"
            noDataText="{{noDataText}}"
            filterPlaceholder="{{filterPlaceholder}}"
            width="{{width}}"
            height="{{height}}"
            on-change="onChange"
            on-visible-change="onVisibleChange"
            on-query-change="onqueryChange">
            <s-tooltip
                class="select-tooltip"
                s-for="item in newSource"
                content="{{item.text}}"
                placement="right">
                <s-option
                    value="{{item.value}}"
                    label="{{item.text}}"
                />
            </s-tooltip>
        </s-select>
    </span>`;

    static components = {
        's-select': Select,
        's-option': Select.Option,
        's-tooltip': Tooltip,
    }

    inited() {
        this.data.set('newSource', this.data.get('datasource'));
        this.watch('datasource', v => this.data.set('newSource', this.data.get('datasource')));
    }

    attached() {
        this._onEnter = this.onEnter.bind(this) as EventListenerOrEventListenerObject;
        document.addEventListener('keydown', this._onEnter);
    }

    detached() {
        this._onEnter && document.removeEventListener('keydown', this._onEnter);
        this._onEnter = undefined;
    }

    onEnter(e: KeyboardEvent) {
        const selectNode = this.ref('select') as SanComponent<{}>;
        if (e.keyCode === 13 && selectNode.data.get('visible')) {
            selectNode.data.set('visible', false);
            this._enterAction = true;
            this.data.set('value', this._temp);
            this.fire('change', {value: this._temp});
        }
    }

    onVisibleChange(target: {value: boolean}) {
        const datasource = this.data.get('datasource');
        if (target.value) {
            this._temp = '';
            this._enterAction = false;
        }
        else if (!this._enterAction) {
            this.data.set('newSource', [...datasource]);
        }
    }

    onqueryChange(target: {value: string}) {
        const datasource = this.data.get('datasource');
        this._temp = target.value;
        const temSource = _.filter(datasource , i => i.text.indexOf(target.value) > -1);
        this.data.set('newSource', !target.value ? _.cloneDeep(datasource) : _.cloneDeep(temSource));
    }

    onChange(target: {value: string}) {
        this.fire('change', {value: target.value});
    }
}