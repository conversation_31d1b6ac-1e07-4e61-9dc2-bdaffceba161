/**
 * 主题监控
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {AppDetailPage, AppLegend} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {Button, Select, Table, Loading, Tooltip, Pagination} from '@baidu/sui';
import checkActive from '@/common/check-active';
import Chart from '@/components/monitor/chart';
import {MonitorInfo} from '@/components/monitor/info';

import DetailNav from '@/components/nav/detail';
import api from '@/common/client';
import {TopicStatus, TIME_LIST} from '@/common/enums';
import {CILENT_SILENT, SCOPE} from '@/common/config';

import CustomSelect from './custom-select';

import {
    TABLE_SUI,
    TABLE_COLUMNS,
    SELECT_SEARCH_HEIGHT,
    ROUTE_PATH,
    PAGER_MAX,
    PAGER_SUI
} from '@/common/config';

import './index.less';

const klass = 'bms-topic-monitor';

type KafkaChartRef = Chart | null;


export const MONITOR_METRIC = {
    traffic: {
        name: '数据量',
        unit: '字节/秒',
        unitArray: ['字节/秒', 'KB/s', 'MB/s', 'GB/s'],
        metrics: [
            {
                name: '写入',
                value: 'BytesInPerSec'
            },
            {
                name: '读出',
                value: 'BytesOutPerSec'
            }
        ]
    },
    message: {
        name: '消息量',
        unit: '个/秒',
        metrics: [
            {
                name: '写入',
                value: 'MessagesInPerSec'
            }
        ]
    },
    lag: {
        name: '消费滞后统计',
        unit: '偏移量',
        metrics: [
            {
                name: '读滞后量',
                value: 'Lag'
            }
        ]
    }
};

const TIME_FILTER = TIME_LIST.toArray();

const RECENT = TIME_FILTER[0].value;

@decorators.asPage(ROUTE_PATH.monitor)
export default class extends AppDetailPage {

    pageTitle = '主题监控';

    static template = html`
    <div class="${klass}">
        <s-detail-page class="bms-detail-page">
            <nav
                slot="pageTitle"
                title="{{topicName}}"
                options="{{options}}"
                back="#${ROUTE_PATH.topic}"
            />
            <div class="bms-detail-page__wrap">
                <div class="${klass}__content">
                    <div class="${klass}__content_pie">
                        <div class="inline-head">
                            <s-applend noHighlight label="主题监控：" />
                        </div>
                        <div class="${klass}__header">
                            <monitor-info
                                alarmSubfix="dimensions={{dimensionsNormal}}"
                                okStateCount="{{info.okStateCount}}"
                                alarmStateCount="{{info.alarmStateCount}}"
                                insufficientStateCount="{{info.insufficientStateCount}}"
                            />
                            <div class="${klass}__header_right">
                                最近：
                                <s-select
                                    class="ml20 mr5"
                                    value="{= recent =}"
                                    datasource="{{timeList}}"
                                    on-change="onRecentChange"
                                    height="{{height}}"
                                    width="{{100}}"
                                />
                                <s-button on-click="onRefresh" class="s-icon-button">
                                    <s-icon-refresh class="button-icon" />
                                </s-button>
                            </div>
                        </div>
                        <div class="${klass}__content_pie_wrap">
                            <div class="${klass}__content_pie_wrap_item"
                                s-for="item,i in chartNormals">
                                <chart
                                    s-ref="chart-normal-{{i}}"
                                    item="{{item}}"
                                    time="{{recent}}"
                                    dimensions="{{dimensionsNormal}}"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="${klass}__content_pie ${klass}__content_right">
                        <div>
                            <s-applend noHighlight label="消费监控：" />
                        </div>
                        <div class="mt10 mb10">
                            消费组：
                            <custom-select
                                class="ml8 mr8"
                                value="{= group =}"
                                datasource="{{groupList}}"
                                width="270"
                                height="{{selectSearchWidth}}"
                                placeholder="如列表无所需消费组，可通过搜索进行查看"
                                filterPlaceholder="按消费组名称搜索"
                                on-change="onGroupChange"
                                noDataText="可通过搜索进行查看"
                            />
                            <s-button on-click="onGroupRefresh" class="s-icon-button">
                                <s-icon-refresh class="button-icon" is-button="{{false}}"/>
                            </s-button>
                        </div>
                        <div class="${klass}__content_pie_wrap">
                            <div class="${klass}__content_pie_wrap_item" s-for="item,i in chartSpecials">
                                <chart
                                    s-if="{{showSpecial}}"
                                    s-ref="chart-special-{{i}}"
                                    item="{{item}}"
                                    time="{{recent}}"
                                    dimensions="{{dimensionsSpecial}}"
                                />
                                <s-loading
                                    class="loading"
                                    loading="{{true}}"
                                    s-else
                                />
                            </div>
                        </div>
                        <div>
                            <s-table
                                s-if="{{showTable}}"
                                columns="{{table.columns}}"
                                loading="{{table.loading}}"
                                error="{{table.error}}"
                                datasource="{{table.datasource}}"
                            />
                            <s-pagination
                                slot="pager"
                                s-if="{{tatolList.length > 0}}"
                                layout="{{'pageSize, pager, go'}}"
                                total="{{pager.count}}"
                                pageSize="{{pager.pageSize}}"
                                page="{{pager.page}}"
                                pageSizes="{{pager.pageSizes}}"
                                max-item="{{7}}"
                                on-pagerChange="onPageChange"
                                on-pagerSizeChange="onPageSizeChange"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </s-detail-page>
    </div>`;

    static components = {
        's-detail-page': AppDetailPage,
        'nav': DetailNav,
        's-button': Button,
        's-select': Select,
        's-option': Select.Option,
        's-tooltip': Tooltip,
        's-table': Table,
        's-pagination': Pagination,
        's-applend': AppLegend,
        's-loading': Loading,
        'chart': Chart,
        'custom-select': CustomSelect,
        'monitor-info': MonitorInfo,
        's-icon-refresh': OutlinedRefresh,
    }

    initData() {
        return {
            info: {},
            recent: RECENT,
            charts: [],
            showTable: false,
            selectSearchWidth: SELECT_SEARCH_HEIGHT,
            groupList: [],
            table: {
                ...TABLE_SUI,
                columns: TABLE_COLUMNS.partitionColumns
            },
            pager: {...PAGER_SUI},
            tatolList: [],
            timeList: TIME_FILTER,
            height: SELECT_SEARCH_HEIGHT
        };
    }

    static computed: SanComputedProps = {
        topicName(): string {
            return this.data.get('route.query.topicName');
        },
        dimensionsNormal(): string {
            return `Topic:${this.data.get('topicName')}`;
        },
        dimensionsSpecial(): string {
            return `Topic:${this.data.get('topicName')};GroupId:${this.data.get('group') || ''}`;
        }
    };

    attached() {
        this.getTopic();
        this.getInfo();
        this.getCharts();
        this.getConsumerSelectList();
    }

    async getTopic() {
        const {result} = await api.bpsTopicList({...PAGER_MAX});
        const item = _.find(result, r => r.name === this.data.get('topicName'));
        item && this.data.set('options', TopicStatus.fromValue(item.status));
    }

    // 获取报警信息
    async getInfo() {
        api.bcmAlarmStateSummary({scope: SCOPE, dimensions: this.data.get('dimensionsNormal')})
            .then((data: AlarmType) => this.data.set('info', data));
    }

    // 分页 page 设置
    onPageChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', args.value.page);
        this.renderTableByPage();
    }

    // 分页 pageSize 设置
    onPageSizeChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', 1);
        this.renderTableByPage();
    }

    // 前端分页函数
    renderTableByPage() {
        const {pager: {page, pageSize}, tatolList} = this.data.get();
        this.data.set('table.loading', true);
        this.data.set('table.datasource',
            tatolList.slice((page - 1) * pageSize, page * pageSize));
        setTimeout(() => this.data.set('table.loading', false));
    }

    // 获取图标信息
    getCharts() {
        const {traffic, message, lag} = MONITOR_METRIC;
        this.data.set('chartNormals', [traffic, message]);
        this.data.set('chartSpecials', [lag]);
    }

    // 获取主题相关charts信息
    getTopicChart() {
        _.each(this.data.get('chartNormals'), (item, index) => {
            const node = this.ref(`chart-normal-${index}`) as KafkaChartRef;
            node && node.refresh();
        });
    }

    // 获取消费组相关charts信息
    getConsumerChart() {
        _.each(this.data.get('chartSpecials'), (item, index) => {
            const node = this.ref(`chart-special-${index}`) as KafkaChartRef;
            node && node.refresh();
        });
    }

    // 获取消费组对应列表
    getConsumerSelectList() {
        api.bpsGroupList({topicName: this.data.get('topicName')}, CILENT_SILENT)
            .then((data: {code: string, success: boolean}) => {
                if (data.code && !data.success) {
                    this.data.set('group', '');
                    this.data.set('showSpecial', true);
                    return;
                }
                const list = _.map(data, d => ({value: d, text: d}));
                this.data.set('groupList', list);
                if (list.length) {
                    this.data.set('group', list[0].value);
                    this.getConsumerTable();
                }
                this.data.set('showSpecial', true);
            });
    }

    // 获取消费组表格信息
    getConsumerTable() {
        const {topicName, group} = this.data.get('');
        if (!group) {
            this.data.set('showTable', false);
            return;
        }
        this.data.set('showTable', true);
        api.bpsGroupPart({topic: topicName, groupId: group})
            .then((data: Array<Subscription>) => {
                this.data.set('tatolList', data);
                this.data.set('pager', {
                    ...PAGER_SUI,
                    count: data.length,
                });
                this.renderTableByPage();
            });
    }

    // 消费组改变
    onGroupChange(target: {value: string}) {
        this.data.set('group', target.value);
        this.getConsumerChart();
        this.getConsumerTable();
    }

    // 消费组刷新
    onGroupRefresh() {
        this.onGroupChange({value: this.data.get('group')});
    }

    // 最近时间改变
    onRecentChange(target: {value: string}) {
        this.data.set('recent', target.value);
        this.nextTick(() => {
            this.getTopicChart();
            this.getConsumerChart();
        });
    }

    // 刷新
    onRefresh() {
        this.onRecentChange({value: this.data.get('recent')});
    }

    // region 切换
    onRegionChange() {
        checkActive().then(() => redirect(`#${ROUTE_PATH.topic}`));
    }
}
