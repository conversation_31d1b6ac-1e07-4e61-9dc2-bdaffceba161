/**
 * 证书列表
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {Pagination, Table, Button, Notification, Dialog} from '@baidu/sui';
import {AppListPage, SearchBox, ClipBoard} from '@baidu/sui-biz';
import {OutlinedPlus, OutlinedDownload, OutlinedRefresh} from '@baidu/sui-icon';

import api from '@/common/client';
import {TABLE_SUI, PAGER_SUI, ROUTE_PATH, COLOR_CONF, CONFIG_TYPE} from '@/common/config';
import {formatTime, formatEmpty, pickEmpty, downloadFile} from '@/common/util';
import checkActive from '@/common/check-active';
import DrawerTable from '@/components/common-table/drawer-table';
import EllipsisTip from '@/components/ellipsis-tip';
import InstantEditor from '@/components/instant-editor';
import CreateBtn from '@/components/create-btn';
import ListTitle from '@/components/list-title';

import CreateCert from './create-cert';
import ViewTopic from './view-topic';
import ViewConsumer from './view-consumer';

import './index.less';

const klass = 'bms-certificate-list';

type OpsType = 'recreate' | 'delete' | 'show-topic' | 'show-consumer';

@decorators.asPage(ROUTE_PATH.certificate)
@decorators.withSidebar({active: ROUTE_PATH.certificate})
export default class extends DrawerTable {

    static template = html`
    <div class="${klass} bms-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <list-title
                    title="证书列表"
                    type="${CONFIG_TYPE.ORDINARY}"
                    hasOnlineAlert="{{false}}"
                />
            </div>
            <div slot="bulk">
                <create-btn text="创建证书" on-click="onCreate" />
            </div>
            <div slot="filter">
                <s-searchbox
                    class="searchbox"
                    placeholder="请输入证书名称进行搜索"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="{{170}}"
                >
                    <div slot="prefix"></div>
                </s-searchbox>
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}">
                <div slot="c-description" class="${klass}__instant">
                    {{row.description | formatEmpty}}
                    <instant-editor
                        value="{{row.description}}"
                        info="{{rowIndex}}"
                        request="{{editDesc}}"
                        placeholder="请输入证书名称"
                        canEmpty="{{true}}"
                    />
                </div>
                <div slot="c-uuid" class="${klass}__ellipsis">
                    <ellipsis-tip placement="top" text="{{row.uuid}}" showTip="{{false}}" />
                    <s-clip-board class="ml5 clipboard-default" text="{{row.uuid}}" />
                </div>
                <div slot="c-secret" class="${klass}__download">
                    <a href="javascript:void(0);" on-click="onDownload(row)" class="a-btn">
                    kafka-key.zip
                    <s-outlined-download class="key-download" color="${COLOR_CONF.defaultColor}" width="18" />
                </a>
                </div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            on-click="onOperate('recreate', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim">
                            重新生成
                        </s-button>
                        <s-button
                            on-click="onOperate('delete', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim">
                            删除证书
                        </s-button>
                        <s-button
                            on-click="onOperate('show-topic', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim">
                            查看主题
                        </s-button>
                        <s-button
                            on-click="onOperate('show-consumer', row, $event)"
                            skin="stringfy"
                            class="table-btn-slim">
                            查看消费组
                        </s-button>
                    </span>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        's-clip-board': ClipBoard,
        's-icon-plus': OutlinedPlus,
        'create-btn': CreateBtn,
        's-outlined-download': OutlinedDownload,
        'ellipsis-tip': EllipsisTip,
        'instant-editor': InstantEditor,
        's-icon-refresh': OutlinedRefresh,
        'list-title': ListTitle
    };

    initData() {
        return {
            searchbox: {
                keyword: '',
                keywordType: ['DescriptionName']
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'description',
                        label: '证书名称',
                    },
                    {name: 'uuid', label: '证书序列号'},
                    {
                        name: 'type',
                        label: '证书类型',
                        render: (item: {isPrivilege: boolean}) => item.isPrivilege ? '特权证书' : '普通证书'
                    },
                    {name: 'secret', label: '密钥', width: 130},
                    {
                        name: 'createAt',
                        label: '创建时间',
                        width: 170,
                        render: (item: {createAt: string}) => formatTime(item.createAt)
                    },
                    {name: 'operation', label: '操作', width: 266}
                ]
            },
            editDesc: this.editDesc.bind(this),
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        formatEmpty
    };

    attached() {
        this.getComList();
    }

    async getTableList() {
        const {searchbox, pager} = this.data.get('');
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            keyword: searchbox.keyword,
            keywordType: searchbox.keywordType[0],
        });
        const {result, totalCount} = await api.bpsCertificateList(params);
        this.data.set('table.datasource', result);
        this.data.set('pager.count', totalCount);
    }

    // 创建
    onCreate() {
        const dialog = new CreateCert({
            data: {}
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.resetTable());
    }

    // 下载证书
    async onDownload(row: {uuid: string}) {
        const url = await api.downloadCert(row.uuid);
        downloadFile(url);
    }

    // 操作
    onOperate(type: OpsType, row: {uuid: string, isPrivilege: boolean, description: string}, event: Event) {
        const baseParam = {
            isPrivilege: row.isPrivilege,
            uuid: row.uuid,
            description: row.description
        };
        switch (type) {
            case 'recreate':
                this.recreate(row.uuid);
                break;
            case 'delete':
                this.deleteCert(row.uuid, row);
                break;
            case 'show-topic': {
                event.stopPropagation();
                this.drawerDialog = new ViewTopic({data: baseParam});
                this.drawerDialog.attach(document.body);
                break;
            }
            case 'show-consumer': {
                event.stopPropagation();
                this.drawerDialog = new ViewConsumer({data: baseParam});
                this.drawerDialog.attach(document.body);
                break;
            }
        }
    }

    // 重新生成
    recreate(uuid: string) {
        Dialog.warning({
            showCancel: true,
            okText: '确定',
            content: '重新生成证书，原证书会失效，请您下载新证书使用！',
            title: "确定重新生成证书吗？",
            // @ts-ignore
            onOk: async () => {
                await api.bpsCertificateRegenerate({uuid});
                this.getComList();
            }
        });
    }

    // 删除
    deleteCert(uuid: string, row: any) {
        Dialog.warning({
            showCancel: true,
            okText: '删除',
            content: `选中的证书删除后无法恢复，请确定是否要删除证书"${row.description}"?`,
            title: '确定删除证书吗？',
            // @ts-ignore
            onOk: async () => {
                await api.bpsCertificateDelete({uuid});
                this.resetTable();
            }
        });
    }

    // 编辑描述
    async editDesc(desc: string, rowIndex: number) {
        const item = this.data.get(`table.datasource[${rowIndex}]`)
        await api.bpsCertificateUpdate({
            description: desc,
            uuid: item.uuid
        });
        Notification.success('修改成功');
        this.data.set(`table.datasource[${rowIndex}].description`, desc);
    }

    // region 切换
    onRegionChange() {
        checkActive().then(() => this.getComList());
    }
}
