/**
 * 创建证书
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Input, Form, Select, Button, Notification, InputNumber, Table, Radio, Checkbox} from '@baidu/sui';

import Tip from '@/components/tip';
import {DIALOG_INPUT_WIDTH, CILENT_SILENT, PAGER_MAX} from '@/common/config';
import {getName, getJoin} from '@/common/util';
import api from '@/common/client';

import './index.less';

const klass = 'create-cert';

const privilegeTopicSource = [{name: '我的所有主题', read: true, write: true}];
const privilegeConsumerSource = [{name: '我的所有消费组', use: true}];

const topicColumns = [
    {name: 'name', label: '主题名称', width: 80},
    {name: 'read', label: '读取权限', width: 80},
    {name: 'write', label: '写入权限', width: 80}
];

const consumerColumns = [
    {name: 'name', label: '消费组名称', width: 80},
    {name: 'use', label: '使用权限', width: 80}
];

class TopicTable extends Component {
    static template = html`
    <template>
        <s-table
            columns="{{columns}}"
            datasource="{{datasource}}"
            loading="{{loading}}"
        >
            <div slot="c-read">
                <s-checkbox
                    checked="{{row.read}}"
                    on-change="onChangeCheckBox('read', $event, rowIndex)"
                />
            </div>
            <div slot="c-write">
                <s-checkbox
                    checked="{{row.write}}"
                    on-change="onChangeCheckBox('write', $event, rowIndex)"
                />
            </div>
        </s-table>
    </template>`;

    static components = {
        's-table': Table,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup
    };

    onChangeCheckBox(type: string, $event: Event, rowIndex: number) {
        this.fire('change', {type, event: $event, rowIndex});
    }
};

class ConsumerTable extends Component {
    static template = html`
    <template>
        <s-table
            columns="{{columns}}"
            datasource="{{datasource}}"
            loading="{{loading}}"
        >
            <div slot="c-use">
                <s-checkbox
                    checked="{{row.use}}"
                    on-change="onUseChange($event, rowIndex)"
                />
            </div>
        </s-table>
    </template>`;

    static components = {
        's-table': Table,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup
    };

    onUseChange($event: Event, rowIndex: string) {
        this.fire('change', {type: 'use', event: $event, rowIndex});
    }
};

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="${klass} ue-dialog"
            title="创建证书"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <s-form
                class="form-format"
                required="{{true}}">
                <s-form-item
                    class="form-item-center"
                    label="证书名称：">
                    <s-input value="{= description =}" width="{{inputWidth}}" />
                </s-form-item>
                <s-form-item
                    class="form-item-center ${klass}__type"
                    label="证书类型：">
                    <span slot="label">证书类型：</span>
                    <s-radio-group
                        value="{= type =}"
                        radioType="button"
                        on-change="onTypeChange"
                    >
                        <s-radio label="特权证书" value="privilege" />
                        <s-radio label="普通证书" value="ordinary" />
                    </s-radio-group>
                    <tip-cmpt slot="label">
                        <p>特权证书的权限对我的所有主题生效，自动对后添加的主题添加权限。</p>
                        <p>普通证书的权限只对所选主题生效，想对后添加的主题授权需手动添加。</p>
                    </tip-cmpt>
                </s-form-item>
                <s-form-item
                    class="${klass}__table-fix"
                    label="主题权限：">
                    <s-topic-table
                        s-if="{{type === 'privilege'}}"
                        columns="{{topicColumns}}"
                        datasource="{{topicPrivilege}}"
                        on-change="onChange($event, 'topicPrivilege')"
                    />
                    <s-topic-table
                        s-if="{{type === 'ordinary'}}"
                        columns="{{topicColumns}}"
                        datasource="{{topicOrdinary}}"
                        loading="{{topicOrdinaryLoading}}"
                        on-change="onChange($event, 'topicOrdinary')"
                    />
                </s-form-item>
                <s-form-item
                    class="${klass}__table-fix"
                    label="消费组权限：">
                    <s-consumer-table
                        s-if="{{type === 'privilege'}}"
                        columns="{{consumerColumns}}"
                        datasource="{{consumerPrivilege}}"
                        on-change="onChange($event, 'consumerPrivilege')"
                    />
                    <s-consumer-table
                        s-if="{{type === 'ordinary'}}"
                        columns="{{consumerColumns}}"
                        datasource="{{consumerOrdinary}}"
                        loading="{{consumerOrdinaryLoading}}"
                        on-change="onChange($event, 'consumerOrdinary')"
                    />
                </s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-input-number': InputNumber,
        's-table': Table,
        's-topic-table': TopicTable,
        's-consumer-table': ConsumerTable,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        'tip-cmpt': Tip,
    };

    initData() {
        return {
            topicColumns,
            consumerColumns,
            topicPrivilege: privilegeTopicSource,
            consumerPrivilege: privilegeConsumerSource,
            open: true,
            inputWidth: DIALOG_INPUT_WIDTH,
            type: 'privilege'
        }
    }

    // checkbox 改变
    onChange(target: {event: {value: boolean}, rowIndex: number, type: string}, source: string) {
        this.data.set(`${source}[${target.rowIndex}].${target.type}`, target.event.value);
    }

    // 证书类型改变
    onTypeChange(target: {value: 'privilege' | 'ordinary'}) {
        if (target.value === 'ordinary') {
            const {
                getTopicOrdinaryFirst,
                getConsumerOrdinaryFirst,
                topicOrdinaryLoading,
                consumerOrdinaryLoading
            } = this.data.get();
            if (!getTopicOrdinaryFirst && !topicOrdinaryLoading) {
                this.getTopicList();
            }
            if (!getConsumerOrdinaryFirst && !consumerOrdinaryLoading) {
                this.getComsumerList();
            }
        }
    }

    // 获取主题列表
    getTopicList() {
        this.data.set('getTopicOrdinaryFirst', false);
        this.data.set('topicOrdinaryLoading', true);
        api.bpsTopicListAll({}, CILENT_SILENT)
            .then((data: Array<{name: string}>) => {
                this.data.set('topicOrdinary', _.map(data, i => ({name: getName(i.name)})));
                this.data.set('getTopicOrdinaryFirst', true);
            })
            .finally(() => this.data.set('topicOrdinaryLoading', false));
    }

    // 获取消费组列表
    getComsumerList() {
        this.data.set('getConsumerOrdinaryFirst', false);
        this.data.set('consumerOrdinaryLoading', true);
        api.bpsConsumerGroupList(PAGER_MAX, CILENT_SILENT)
            .then((data: {result: Array<{consumerGroupName: string}>}) => {
                this.data.set('consumerOrdinary', _.map(data.result, i => ({name: getName(i.consumerGroupName)})));
                this.data.set('getConsumerOrdinaryFirst', true);
            })
            .finally(() => this.data.set('consumerOrdinaryLoading', false));
    }

    // 格式化参数
    private formatParam() {
        const {
            type,
            description,
            topicPrivilege,
            topicOrdinary,
            consumerPrivilege,
            consumerOrdinary
        } = this.data.get();
        const isPrivilege = type === 'privilege';
        const base = {
            description: description || '',
            isPrivilege
        };
        return isPrivilege ? {
                ...base,
                authOfSelfTopics: this.getAuthList('topic', true, topicPrivilege),
                authOfSelfGroups: this.getAuthList('comsumer', true, consumerPrivilege)
            } : {
                ...base,
                authOfSelfTopics: this.getAuthList('topic', false, topicOrdinary),
                authOfSelfGroups: this.getAuthList('comsumer', false, consumerOrdinary)
            };
    }

    // 获取创建主题主题权限和消费组权限数据列表
    private getAuthList(type: string, isPrivilege: boolean, arr: Array<any>) {
        const accountIdPrefix = this.$context.getUserId() + '__';
        if (type === 'topic') {
            const arrList = _.filter(arr, i => (i.read || i.write));
            return _.map(arrList, i => ({
                topic: isPrivilege ? '*' : accountIdPrefix + i.name,
                topicOperation: getJoin(i.read, 'Read', i.write, 'Write')
            }));
        }
        if (type === 'comsumer') {
            const arrList = _.filter(arr, i => i.use);
            return _.map(arrList, i => ({
                consumerGroupName: isPrivilege ? '*' : accountIdPrefix + i.name,
                consumerGroupOperation: 'Use'
            }));
        }
    }

    // 确认
    async onConfirm() {
        try {
            this.data.set('confirming', true);
            await api.bpsCertificateCreate(this.formatParam(), CILENT_SILENT);
            Notification.success('保存成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
