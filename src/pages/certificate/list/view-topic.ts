/**
 * 查看主题
 *
 * @file view-topic.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Table, Checkbox, Notification} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz'

import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/drawer-common';
import api from '@/common/client';
import {CILENT_SILENT} from '@/common/config';
import EllipsisTip from '@/components/ellipsis-tip';

type TopicList = Array<{topic: string, topicCluster?: string, topicOperation?: string, uuid?: string}>;
type TopicItem = {read: boolean, write: boolean};

class TableCmpt extends Component {
    static template = html`
    <div>
        <div class="view-topic__title mb16">{{title}}</div>
        <s-table
            columns="{{columns}}"
            datasource="{{datasource}}"
            loading="{{loading}}">
            <ellipsis-tip slot="c-name" text="{{row.name}}" placement="bottom" />
            <div slot="c-read">
                <s-checkbox
                    checked="{{row.read}}"
                    disabled="{{disabled}}"
                    on-change="onChangeCheckBox('read', rowIndex, $event)"
                />
            </div>
            <div slot="c-write">
                <s-checkbox
                    checked="{{row.write}}"
                    disabled="{{disabled}}"
                    on-change="onChangeCheckBox('write', rowIndex, $event)"
                />
            </div>
            <div slot="empty">
                <s-empty
                    actionText=""
                    vertical
                />
            </div>
        </s-table>
    </div>`;

    static components = {
        's-table': Table,
        's-checkbox': Checkbox,
        'ellipsis-tip': EllipsisTip,
        's-empty': Empty
    };

    initData() {
        return {
            columns: [
                {name: 'name', label: '主题名称', width: 150},
                {name: 'read', label: '读取权限', width: 50},
                {name: 'write', label: '写入权限', width: 50}
            ]
        }
    }

    onChangeCheckBox(type: string, rowIndex: number, target: {value: boolean}) {
        this.data.set(`datasource[${rowIndex}].${type}`, target.value);
    }
}

export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="{{isPrivilege ? '特权证书：' : '普通证书：'}}"
            extraTitle="{{description || uuid}}"
            checkChange="{{checkChange}}"
            save="{{save}}"
            on-edit="onEdit"
            on-cancel="onCancel"
            on-close="onClose"
            showTip="{{false}}"
        >
            <table-cmpt
                title="我的主题"
                datasource="{= own =}"
                loading="{{ownLoading}}"
                disabled="{{mode === 'read'}}"
            />
            <table-cmpt
                class="mt15"
                title="他人主题"
                datasource="{= other =}"
                disabled
                loading="{{otherLoading}}"
            />
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': DrawerCommon,
        'table-cmpt': TableCmpt
    };

    initData() {
        return {
            mode: 'read',
            checkChange: this.checkChange.bind(this),
            save: this.onOk.bind(this)
        };
    }

    attached() {
        this.getList();
    }

    getList() {
        // 获取自己的主题
        this.data.set('ownLoading', true);
        api.bpsCertificateAuthList(this.getTopicParams(true))
            .then((data: {authorizedTopicList: TopicList}) => {
                const backup = this.handleFormat(data.authorizedTopicList);
                this.data.set('own', backup);
                this.data.set('ownBackup', backup);
            })
            .finally(() => this.data.set('ownLoading', false));

        // 获取他人主题
        this.data.set('otherLoading', true);
        api.bpsCertificateAuthList(this.getTopicParams(false))
            .then((data: {authorizedTopicList: TopicList}) => {
                const backup = this.handleFormat(data.authorizedTopicList);
                this.data.set('other', backup);
                this.data.set('otherBackup', backup);
            })
            .finally(() => this.data.set('otherLoading', false));
    }

    // 获取证书的参数
    getTopicParams(isSelfTopic: boolean) {
        const {uuid, isPrivilege} = this.data.get();
        return {
            accountId: this.$context.getUserId(),
            authOfSelfTopics: isSelfTopic ? 'true' : 'false',
            begin: null,
            certificateId: uuid,
            isPrivilegedCert: (!!isPrivilege).toString(),
            limit: null
        }
    }

    // 处理格式
    handleFormat(topicList: TopicList) {
        const isPrivilege = this.data.get('isPrivilege');
        return _.map(topicList, item => ({
            topic: item.topic,
            name: isPrivilege && item.topic === '*' ? '我的所有主题' : item.topic,
            cluster: item.topicCluster,
            read: !!(item.topicOperation && item.topicOperation.indexOf('Read') !== -1),
            write: !!(item.topicOperation && item.topicOperation.indexOf('Write') !== -1),
            uuid: item.uuid
        }));
    }

    // 检测内容是否有改变
    checkChange() {
        const data = this.filterData();
        return data.addAuthOfSelfTopics.length
            || data.updateAuthOfSelfTopics.length
            || data.deleteAuthIdOfSelfTopics.length;
    }

    // 编辑
    onEdit() {
        this.data.set('mode', 'edit');
    }

    // 取消
    onCancel() {
        this.data.set('mode', 'read');
        this.getList();
    }

    // 保存
    onOk() {
        return api.bpsCertificateAuthModify(this.filterData(), CILENT_SILENT)
            .then(() => {
                Notification.success('保存成功');
                this.data.set('mode', 'read');
                _.each(['own', 'other'], item => {
                    this.data.set(`${item}Backup`, this.data.get(`${item}`));
                });
                this.getList();
            });
    }

    onClose() {
        this.dispose();
    }

    // 在提交请求前统一处理数据内容和格式
    // 备注：ER处理数据逻辑
    filterData() {
        const toFind = ['own', 'other'];
        const changes = _.map(toFind, item => {
            let origin = this.data.get(`${item}Backup`);
            let target = this.data.get(`${item}`);
            return findChange(origin, target);
        });

        return {
            certificateId: this.data.get('uuid'),
            addAuthOfSelfTopics: changes[0] && changes[0].add,
            updateAuthOfSelfTopics: changes[0] && changes[0].update,
            deleteAuthIdOfSelfTopics: changes[0] && changes[0].del
        }
    }
}


// 获取源、目标数据列表的差异内容
// 备注：ER处理数据逻辑
function findChange(source: Array<any>, target: Array<any>) {
    // @ts-ignore
    let add = [];
    // @ts-ignore
    let update = [];
    // @ts-ignore
    let del = [];

    let tIdsMap = {};
    for (let i = 0, l = target.length; i < l; i++) {
        // @ts-ignore
        tIdsMap[target[i].topic] = target[i];
    }

    let sItem;
    let tItem;
    let itemChange;
    for (let i = 0, l = source.length; i < l; i++) {
        sItem = source[i];
        // @ts-ignore
        tItem = tIdsMap[sItem.topic];
        if (tItem) {
            itemChange = _getCertChange(sItem, tItem);
        }
        if (itemChange === -1) {
            continue;
        }
        tItem.topicOperation = `${tItem.read ? 'Read' : ''}${tItem.write ? 'Write' : ''}`;
        // add
        if (itemChange === 0) {
            add.push({
                topic: tItem.topic,
                topicCluster: tItem.cluster,
                topicOperation: tItem.topicOperation
            });
        }
        // update
        else if (itemChange === 1) {
            update.push({
                uuid: tItem.uuid,
                topicOperation: tItem.topicOperation
            });
        }
        // delete
        else if (itemChange === 2) {
            del.push(tItem.uuid);
        }
    }

    return {
        add,
        update,
        del
    };

    // 获取单个证书的需要提交的修改类型参数
    function _getCertChange(sItem: TopicItem, tItem: TopicItem) {
        // 没有变化
        if (sItem.read === tItem.read
            && sItem.write === tItem.write) {
            return -1;
        }
        // 删除
        let isSourceHasAuth = sItem.read || sItem.write;
        let isTargetHasAuth = tItem.read || tItem.write;
        if (isSourceHasAuth && !isTargetHasAuth) {
            return 2;
        }
        // 新增
        if (!isSourceHasAuth && isTargetHasAuth) {
            return 0;
        }
        // 修改
        return 1;
    }
}
