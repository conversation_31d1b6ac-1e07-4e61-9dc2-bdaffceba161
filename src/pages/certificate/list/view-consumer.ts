/**
 * 查看消费组
 *
 * @file view-consumer.js
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Table, Checkbox, Notification} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz'

import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/drawer-common';
import api from '@/common/client';
import {CILENT_SILENT} from '@/common/config';

type ConsumerList = Array<{consumerGroupName: string, consumerGroupOperation: string}>;
type ConsumerItem = {use: boolean};

class TableCmpt extends Component {
    static template = html`
    <div>
        <div class="view-consumer__title mb16">{{title}}</div>
        <s-table
            columns="{{columns}}"
            datasource="{{datasource}}"
            loading="{{loading}}">
            <div slot="c-use">
                <s-checkbox
                    checked="{{row.use}}"
                    disabled="{{disabled}}"
                    on-change="onChangeCheckBox('use', rowIndex, $event)"
                />
            </div>
            <div slot="empty">
                <s-empty
                    vertical
                    actionText=""
                />
            </div>
        </s-table>
    </div>`;

    static components = {
        's-table': Table,
        's-checkbox': Checkbox,
        's-empty': Empty
    };

    initData() {
        return {
            columns: [
                {name: 'name', label: '消费组名称', width: 150},
                {name: 'use', label: '使用权限', width: 80},
            ]
        }
    }

    onChangeCheckBox(type: string, rowIndex: number, target: {value: boolean}) {
        this.data.set(`datasource[${rowIndex}].${type}`, target.value);
    }
}

export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="{{isPrivilege ? '特权证书：' : '普通证书：'}}"
            extraTitle="{{description || uuid}}"
            checkChange="{{checkChange}}"
            save="{{save}}"
            on-edit="onEdit"
            on-cancel="onCancel"
            on-close="onClose"
            showTip="{{false}}"
        >
            <table-cmpt
                title="我的消费组"
                datasource="{= own =}"
                loading="{{ownLoading}}"
                disabled="{{mode === 'read'}}"
            />
            <table-cmpt
                class="mt15"
                title="他人消费组"
                datasource="{= other =}"
                disabled
                loading="{{otherLoading}}"
            />
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': DrawerCommon,
        'table-cmpt': TableCmpt
    };

    initData() {
        return {
            mode: 'read',
            checkChange: this.checkChange.bind(this),
            save: this.onOk.bind(this)
        };
    }

    attached() {
        this.getList();
    }

    getList() {
        // 获取自己的消费组
        this.data.set('ownLoading', true);
        api.bpsConsumerGroupListInAuthCert(this.getConsumerParams(true))
            .then((data: {authorizedConsumerGroups: ConsumerList}) => {
                const backup = this.handleFormat(data.authorizedConsumerGroups);
                this.data.set('own', backup);
                this.data.set('ownBackup', backup);
            })
            .finally(() => this.data.set('ownLoading', false));

        // 获取他人消费组
        this.data.set('otherLoading', true);
        api.bpsConsumerGroupListInAuthCert(this.getConsumerParams(false))
            .then((data: {authorizedConsumerGroups: ConsumerList}) => {
                const backup = this.handleFormat(data.authorizedConsumerGroups);
                this.data.set('other', backup);
                this.data.set('otherBackup', backup);
            })
            .finally(() => this.data.set('otherLoading', false));
    }

    // 获取消费组的参数
    getConsumerParams(isSelfGroup: boolean) {
        const {uuid, isPrivilege} = this.data.get();
        return {
            accountId: this.$context.getUserId(),
            authOfSelfGroups: isSelfGroup ? 'true' : 'false',
            certificateId: uuid,
            isPrivilegedCert: (!!isPrivilege).toString(),
        }
    }

    // 处理格式
    handleFormat(topicList: ConsumerList) {
        return _.map(topicList, item => ({
            consumerGroupName: item.consumerGroupName,
            name: this.data.get('isPrivilege') && item.consumerGroupName === '*'
                ? '我的所有消费组' : item.consumerGroupName,
            use: !!(item.consumerGroupOperation
                && item.consumerGroupOperation.indexOf('Use') !== -1)
        }));
    }

    // 检测内容是否有改变
    checkChange() {
        const data = this.filterData();
        return data.addAuthOfSelfGroups.length
            || data.updateAuthOfSelfGroups.length
            || data.deleteAuthIdOfSelfGroups.length;
    }

    // 编辑
    onEdit() {
        this.data.set('mode', 'edit');
    }

    // 取消
    onCancel() {
        this.data.set('mode', 'read');
        this.getList();
    }

    // 保存
    onOk() {
        return api.bpsModifyConsumerGroupInAuthCert(this.filterData(), CILENT_SILENT)
            .then(() => {
                Notification.success('保存成功');
                this.data.set('mode', 'read');
                _.each(['own', 'other'], item => {
                    this.data.set(`${item}Backup`, this.data.get(`${item}`));
                });
                this.getList();
            });
    }

    onClose() {
        this.dispose();
    }

    // 在提交请求前统一处理数据内容和格式
    // 备注：ER处理数据逻辑
    filterData() {
        const {own, ownBackup, uuid} = this.data.get();
        const change = findChange(ownBackup, own);
        return {
            certificateId: uuid,
            addAuthOfSelfGroups: change.add,
            updateAuthOfSelfGroups: change.update,
            deleteAuthIdOfSelfGroups: change.del
        }
    }
}


// 获取源、目标数据列表的差异内容
// 备注：ER处理数据逻辑
function findChange(source: Array<any>, target: Array<any>) {
    // @ts-ignore
    let add = [];
    // @ts-ignore
    let update = [];
    // @ts-ignore
    let del = [];

    let tIdsMap = {};
    for (let i = 0, l = target.length; i < l; i++) {
        // @ts-ignore
        tIdsMap[target[i].consumerGroupName] = target[i];
    }

    let sItem;
    let tItem;
    let itemChange;
    for (let i = 0, l = source.length; i < l; i++) {
        sItem = source[i];
        // @ts-ignore
        tItem = tIdsMap[sItem.consumerGroupName];
        if (tItem) {
            itemChange = _getCertChange(sItem, tItem);
        }

        if (itemChange === -1) {
            continue;
        }

        tItem.consumerGroupOperation = `${tItem.use ? 'Use' : ''}`;
        // add
        if (itemChange === 0) {
            add.push({
                consumerGroupName: tItem.consumerGroupName,
                // topicCluster: tItem.cluster,
                consumerGroupOperation: tItem.consumerGroupOperation
            });
        }
        // update
        else if (itemChange === 1) {
            update.push({
                consumerGroupName: tItem.consumerGroupName,
                consumerGroupOperation: tItem.consumerGroupOperation
            });
        }
        // delete
        else if (itemChange === 2) {
            del.push({
                consumerGroupName: tItem.consumerGroupName,
                consumerGroupOperation: ''
            });
        }
    }
    return {
        add,
        update,
        del
    };;

    // 获取单个证书的需要提交的修改类型参数
    function _getCertChange(sItem: ConsumerItem, tItem: ConsumerItem) {
        // 没有变化
        if (sItem.use === tItem.use) {
            return -1;
        }
        // 删除
        let isSourceHasAuth = sItem.use;
        let isTargetHasAuth = tItem.use;
        if (isSourceHasAuth && !isTargetHasAuth) {
            return 2;
        }
        // 新增
        if (!isSourceHasAuth && isTargetHasAuth) {
            return 0;
        }
        // 修改
        return 1;
    }
}
