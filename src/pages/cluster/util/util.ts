/* eslint-disable max-len */
/**
 * 集群相关单元函数
 *
 * @file util.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import m from 'moment';

import {formatBytes, formatEmpty, formatTime, renderZoneLabel, getModes} from '@/common/util';
import {ClusterDefaultConf, Payments, storagePolicyEnum} from '@/common/enums';
import {PaymentType, RecommandClusterType, taskType, operationType} from '@/common/enums/constant';
import {DiskType} from '@baidu/bce-bcc-sdk-enum';
import {renderSwitch} from '@/common/html';
import {CELL, LANG_MAP} from '@/common/config';

const handleNormal = (text: any) => text;
export const PriceCell = '分钟';

export const UtilHandler = {
    clusterId: handleNormal,
    operationId: handleNormal,
    name: handleNormal,
    createTime: formatTime,
    version: handleNormal,
    runningTime: (runningTime: {day: string, hour: string, minute: string, second: string} | undefined) => {
        return (
            (runningTime?.day ? runningTime.day + LANG_MAP.day : '')
            + (runningTime?.hour ? runningTime.hour + LANG_MAP.hour : '')
            + (runningTime?.minute ? runningTime.minute + LANG_MAP.minute : '')
            + (runningTime?.second ? runningTime.second + LANG_MAP.second : '')
        );
    },
    taskName: (type: string) => {
        return taskType[`${type}`];
    },
    operationName: (type: string) => {
        return operationType[`${type}`];
    },
    region: (region: string) => {
        const allRegion = window.$context.getEnum('AllRegion');
        return allRegion.getTextFromValue(region);
    },
    deployType: (deployType: string) => {
        return deployType === 'HP' ? '高性能模式' : '高可用模式';
    },
    payment: (payment: PaymentType) => Payments.getTextFromValue(payment),
    configId: (configId: string) => {
        return configId ? ClusterDefaultConf.toArray()[1].text : ClusterDefaultConf.toArray()[0].text;
    },
    vpc: (vpc: {name: string, cidr: string} | undefined) => (vpc ? `${vpc.name}(${vpc.cidr})` : ''),
    subnets: (subnets: Array<{name: string, cidr: string}> = []) => _.map(subnets, i => `${i.name}(${i.cidr})`),
    securityGroup: (securityGroup: string[] | [], securityGroups: any[]) => securityGroups.filter(item => _.includes(securityGroup, item.value)).map(item => item.text).join(', '),
    publicIpEnabled: renderSwitch,
    publicIpBandwidth: (publicIpBandwidth: number, publicIpMode: string) => (
        publicIpMode === 'AUTO_ASSIGN'
            ? publicIpBandwidth ? publicIpBandwidth + CELL.Mbps : ''
            : 'EIP实例自行查看带宽'),
    intranetIpEnabled: renderSwitch,
    formatStoragePolicyEnabled: renderSwitch,
    logicalZones: (logicalZones: string[]) => {
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        const res = !isEdgeRegion ? _.map(logicalZones, i => renderZoneLabel(i)).join('，') : renderZoneLabel(logicalZones ? logicalZones[0] : '');
        return res;
    },
    storageType: (storageType: any) => DiskType.getTextFromAlias(storageType),
    storageSize: (
        storageMeta: {
            storageSize: number;
            numberOfDisk: number;
            storageType: string;
        },
        isMultipleDisks: boolean
    ) => {
        const {storageSize, numberOfDisk} = storageMeta;
        const storageMetaTotal = storageSize * numberOfDisk;
        return isMultipleDisks
            ? `${storageMetaTotal}${CELL.GB}（${storageSize}${CELL.GB} × ${numberOfDisk}）`
            : `${storageMetaTotal}${CELL.GB}`;
    },
    totalSize: (
        storageMeta: {
            storageSize: number;
            numberOfDisk: number;
            storageType?: string;
        } = {storageSize: 0, numberOfDisk: 0},
        numberOfBrokerNodes: number
    ) => {
        const {storageSize, numberOfDisk} = storageMeta;
        const storageMetaTotal = storageSize * numberOfDisk;
        return `${storageMetaTotal * (numberOfBrokerNodes || 0)}${CELL.GB}`;
    },
    nodeType: handleNormal,
    numberOfBrokerNodes: handleNormal,
    numberOfBrokerNodesPerZone: handleNormal,
    controllerOfBrokerNodes: handleNormal,
    aclEnabled: renderSwitch,
    deploySetEnabled: renderSwitch,
    storagePolicyEnabled: renderSwitch,
    storagePolicy: (storagePolicy: {type: string, autoDelete: object}) => {
        return storagePolicyEnum.getTextFromValue(storagePolicy?.type);
    },
    authenticationModes: (authenticationModes: string[]) => getModes(authenticationModes),
    getPriceDesc: (price: number) => {
        const dayInMonth = m().daysInMonth();
        const cell = price * 60 * 24;
        return `(预计¥${cell.toFixed(2)}/天 ¥${(cell * dayInMonth).toFixed(1)}/月)`;
    },
    getPrice: (price: number, payment: PaymentType, timeLength: number, timeUnit: string) => {
        return `¥${price || 0}${
            payment === PaymentType.POSTPAID
                ? `/${PriceCell}`
                : payment === PaymentType.PREPAID && timeLength && timeUnit
                    ? '/' + timeLength + (timeUnit === 'month' ? '个月' : '年')
                    : ''
        }`;
    },
    title: (type: string) => {
        return type === RecommandClusterType.HIGHPERFORMANCE ? '超高性能配置' : '高性价比配置';
    },
    desc: (type: string) => {
        return type === RecommandClusterType.HIGHPERFORMANCE
            ? '业务有高并发或短期内有业务扩增'
            : '满足业务流量需求且性价比高';
    },
    formatBytes: (bytesNum: number) => {
        if (bytesNum < 1024 * 1024) {
            return bytesNum + 'bytes';
        }
        else if (bytesNum % (1024 * 1024 * 1024) !== 0 || bytesNum < 1024 * 1024 * 1024) {
            return Math.floor(bytesNum / 1024 / 1024) + 'MB';
        }
        else {
            return Math.floor(bytesNum / 1024 / 1024 / 1024) + 'GB';
        }
    },
    formatTimeInfo: (time: number) => {
        if (time < 1000 * 60 * 60) {
            return Math.floor(time / 1000) + '秒';
        }
        if (time < 1000 * 60 * 60 * 24) {
            return Math.floor(time / (1000 * 60 * 60)) + '小时';
        }
        else if (time % (1000 * 60 * 60 * 24 * 30) !== 0) {
            return Math.floor(time / (1000 * 60 * 60)) + '小时';
        }
        else {
            return Math.floor(time / (1000 * 60 * 60 * 24)) + '天';
        }
    }
};
