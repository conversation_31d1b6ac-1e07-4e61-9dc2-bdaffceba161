/**
 * 集群相关配置
 *
 * @file conf.ts
 * <AUTHOR>
 */

export enum UpgrageType {
    INCREASE_BROKER_COUNT = 'INCREASE_BROKER_COUNT',
    DECREASE_BROKER_COUNT = 'DECREASE_BROKER_COUNT',
    EXPAND_BROKER_DISK_CAPACITY = 'EXPAND_BROKER_DISK_CAPACITY',
    UPDATE_STORAGE_POLICY = 'UPDATE_STORAGE_POLICY',
    UPDATE_BROKER_NODE_TYPE = 'UPDATE_BROKER_NODE_TYPE',
    UPDATE_CLUSTER_NAME = 'UPDATE_CLUSTER_NAME',
    UPDATE_ACCESS_CONFIG = 'UPDATE_ACCESS_CONFIG',
    UPDATE_KAFKA_CONFIG = 'UPDATE_KAFKA_CONFIG',
    RESIZE_CLUSTER_EIP_BANDWIDTH = 'RESIZE_CLUSTER_EIP_BANDWIDTH',
    ENABLE_CLUSTER_EIP = 'ENABLE_CLUSTER_EIP',
    DISABLE_CLUSTER_EIP = 'DISABLE_CLUSTER_EIP',
    UPDATE_CLUSTER_SECURITY_GROUP = 'UPDATE_CLUSTER_SECURITY_GROUP',
    ENABLE_CLUSTER_INTRANET_IP = 'ENABLE_CLUSTER_INTRANET_IP',
    DISABLE_CLUSTER_INTRANET_IP = 'DISABLE_CLUSTER_INTRANET_IP'
}
