/**
 * @file index.less
 * <AUTHOR>
 */

@klass: bms-cluster-upgrage;

.@{klass} {
    display: block;
    height: 100%;
    padding-bottom: 80px;
    background: var(--backgroundColor);

    &__alert {
        padding: 1px 24px 12px 16px;
        position: relative;
        top: -1px;
        background-color: var(--whiteColor);

        .s-alert {
            margin: 0;
        }
    }

    &__create {
        width: 100%;
        min-height: 100%;
    }

    &__step {
        z-index: 1;
        width: 600px;
        margin: 24px auto;
        position: relative;

        .s-steps {

            .s-step {

                .s-step-line-real{
                    background-color: var(--stepLineBackgroundColor) !important;
                }

                .s-step-content-icon {
                    background: var(--backgroundColor) !important;
                }
                .s-step-content-title {
                    background: var(--backgroundColor) !important;
                }
            }
        }
    }

    &__order {
        width: 100%;
        height: auto;

        &_panel {
            min-height: 100%;
        }
    }

    &__loading {
        margin: 15px auto 0;
        width: 980px;
        padding: 15px 0;
        text-align: center;
    }

    .upgrage-text {
        display: inline-block;
        margin-top: 6px;
    }

    .config-option {
        .s-option {
            height: auto;
            padding: 8px 12px;
            width: 300px;
            overflow-x: auto;
            div {
                display: block!important;
                height: auto;
                line-height: 20px;
                font-size: 12px;
            }
            .label {
                color: #151B26;
                font-weight: 500;
            }
            .desc {
                color: #5C5F66;
                font-weight: 400;
            }
        }
    }
}
