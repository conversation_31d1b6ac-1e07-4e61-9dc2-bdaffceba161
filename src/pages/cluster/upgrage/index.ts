/**
 * 集群配置变更
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {Steps, Button, Alert, Loading, Notification} from '@baidu/sui';
import BigNumber from 'bignumber.js';

import {ROUTE_PATH, postPaidParam, prePaidParam} from '@/common/config';
import CreateNav from '@/components/nav/create';
import api from '@/common/client';
import {ClusterRefType, PaymentType} from '@/common/enums/constant';
import {formatePrice} from '@/common/util';

import {Region} from '../components/region';
import OrderItem from '../components/order-item';
import {ClusterConfig} from '../components/cluster-config';
import NetWork from '../components/network-config';
import Access from '../components/access-config';
import NodeConfig from '../components/node-config';
import DiskConfig from '../components/disk-config';
import Coupon from '../components/coupon-config';
import UpgrageShopCart from '../components/shop-cart/upgrage-cart';
import {UpgrageType} from '../util/conf';
import BaseCreate, {Refs, NodeClass, noCoupon} from '../components/base-create';
const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
import './index.less';

const klass = 'bms-cluster-upgrage';

@decorators.asPage(ROUTE_PATH.clusterUpgrage)
export default class extends BaseCreate {
    pageTitle = '变更集群';

    static template = html` <div class="${klass}">
        <create-nav backUpTitle="变更集群" back="#${ROUTE_PATH.clusterList}" />
        <div class="${klass}__alert" s-if="!KAFKA_ClusterMultipleUpdateWhiteList">
            <s-alert skin="warning"> 当您修改了某个配置项，为避免配置发生冲突，其他类型配置项将被禁用 </s-alert>
        </div>
        <div class="${klass}__step">
            <s-steps current="{{current}}" type="normal">
                <s-step title="信息配置" />
                <s-step title="确认订单" />
            </s-steps>
        </div>
        <div class="${klass}__create" style="{{current | isShow(1)}}">
            <template s-if="{{!getLoading}}">
                <region-config s-ref="${ClusterRefType.REGION}" isDetail />
                <cluster-config
                    s-ref="${ClusterRefType.CLUSTER}"
                    isDetail
                    isUpgrading="{{upgradeConfig}}"
                    on-change-upgrade-status="changeUpgradeStatus('Config')"
                />
                <node-config
                    s-ref="${ClusterRefType.NODE}"
                    isUpgrading="{{upgradeNode}}"
                    clusterId="{{clusterId}}"
                    on-price-config-change="onQueryPrice"
                    on-select-zones="onSeletZones"
                    on-nochange="resetCurrentPrice"
                    on-flavor-loaded="handleFlavorLoaded"
                    on-change-upgrade-status="changeUpgradeStatus('Node')"
                    on-singleNumberTextNumber-change="onSingleNumberTextNumberChange"
                    KafkaDecreaseBrokerCount="{{KafkaDecreaseBrokerCount}}"
                    isDetail
                    KAFKA_ClusterMultipleUpdateWhiteList="{{KAFKA_ClusterMultipleUpdateWhiteList}}"
                />
                <disk-config
                    s-ref="${ClusterRefType.DISK}"
                    cdsArr="{{cdsArr}}"
                    on-price-config-change="onQueryPrice"
                    isDetail
                    singleNumberTextNumber="{{singleNumberTextNumber}}"
                    isUpgrading="{{upgradeDisk}}"
                    on-change-upgrade-status="changeUpgradeStatus('Disk')"
                    payload="{{payload}}"
                    KAFKA_ClusterMultipleUpdateWhiteList="{{KAFKA_ClusterMultipleUpdateWhiteList}}"
                />
                <network-config
                    s-ref="${ClusterRefType.NETWORK}"
                    on-price-config-change="onQueryPrice"
                    on-publicIp-change="onPublicIpChange"
                    singleNumberTextNumber="{{singleNumberTextNumber}}"
                    on-privateIp-change="onPrivateIpChange"
                    on-change-upgrade-status="changeUpgradeStatus('Network')"
                    isUpgrading="{{upgradeNetwork}}"
                    clusterId="{{clusterId}}"
                    detail="{{detail}}"
                    isDetail
                    KAFKA_ClusterMultipleUpdateWhiteList="{{KAFKA_ClusterMultipleUpdateWhiteList}}"
                />
                <access-config
                    s-ref="${ClusterRefType.ACCESS}"
                    isUpgrading="{{upgradeAccess}}"
                    on-change-upgrade-status="changeUpgradeStatus('Access')"
                    isDetail
                />
            </template>
            <template s-else>
                <div class="${klass}__loading">
                    <s-loading loading />
                </div>
            </template>
        </div>
        <div class="${klass}__order" style="{{current | isShow(2)}}">
            <div class="${klass}__order_panel">
                <order-item
                    s-for="item in orderItems"
                    title="{{item.title}}"
                    type="{{item.type}}"
                    list="{{item.list}}"
                    payment="{{payment}}"
                    timeLength="{{timeLength}}"
                    timeUnit="{{timeUnit}}"
                    clusterPrice="{{clusterPrice}}"
                    publicIpPrice="{{publicIpPrice}}"
                    publicIp="{{publicIp}}"
                    isDetail
                />
                <coupon-config
                    s-if="showCoupon && !${isXushang}"
                    couponList="{{couponList}}"
                    errMsg="{{errMsg}}"
                    decountPrice="{{decountPrice}}"
                    on-active-coupon="activeCoupon"
                    on-coupon-change="onCouponChange"
                    on-clear-coupon="onClearCoupon"
                />
            </div>
        </div>
        <shop-cart
            current="{{current}}"
            payment="{{payment}}"
            timeLength="{{timeLength}}"
            timeUnit="{{timeUnit}}"
            isSubmiting="{{isSubmiting}}"
            publicIp="{{publicIp}}"
            totalPrice="{{totalPrice}}"
            finalPrice="{{finalPrice}}"
            clusterPrice="{{clusterPrice}}"
            publicIpPrice="{{publicIpPrice}}"
            expirationTime="{{expirationTime}}"
            oldClusterPrice="{{oldClusterPrice}}"
            margin="{{margin}}"
            decountPrice="{{decountPrice}}"
            on-click="onShopCartClick"
            on-reset="onReset"
        />
    </div>`;

    static components = {
        's-steps': Steps,
        's-step': Steps.Step,
        's-button': Button,
        's-alert': Alert,
        's-loading': Loading,
        'create-nav': CreateNav,
        'network-config': NetWork,
        'cluster-config': ClusterConfig,
        'node-config': NodeConfig,
        'disk-config': DiskConfig,
        'region-config': Region,
        'access-config': Access,
        'shop-cart': UpgrageShopCart,
        'order-item': OrderItem,
        'coupon-config': Coupon
    };

    initData() {
        return {
            ...super.initData(),
            margin: 0,
            priceLoading: false,
            getLoading: true,
            upgradeNode: true,
            upgradeAccess: false,
            upgradeNetwork: false,
            upgradeDisk: false,
            upgradeConfig: false,
            isDetail: true,
            singleNumberTextNumber: null
        };
    }

    static computed: SanComputedProps = {
        totalPrice() {
            return this.data.get('margin');
        },
        finalPrice() {
            if (this.data.get('totalPrice') < 0) {
                return this.data.get('totalPrice');
            } else {
                const totalPrice = new BigNumber(this.data.get('totalPrice'));
                const couponValue = new BigNumber(this.data.get('couponValue'));
                const res = totalPrice.minus(couponValue).toNumber();
                return res <= 0 ? 0 : formatePrice(res);
            }
        },
        decountPrice() {
            const totalPrice = this.data.get('totalPrice');
            const couponValue = this.data.get('couponValue');
            return totalPrice <= 0 ? 0 : totalPrice < couponValue ? totalPrice : couponValue;
        },
        clusterId(): string {
            return this.data.get('route.query.clusterId');
        }
    };

    static filters = {
        ...BaseCreate.filters,
        isShow(current: number, index: number) {
            return current === index ? '' : 'display:none';
        }
    };

    attached() {
        this.getDetail();
        this.getCluserUpgradeWhiteInfo();
    }

    handleFlavorLoaded(target: {cdsArr: any[], singleNumberTextNumber: number, payloadType: string}) {
        this.data.set('cdsArr', [...target.cdsArr]);
        this.data.set('payloadType', target.payloadType);
        this.data.set('upgradeNetwork', true);
        this.data.set('upgradeAccess', true);
        this.data.set('upgradeDisk', true);
        this.data.set('upgradeConfig', true);
        this.data.set('flavorloading', false);
    }

    getCluserUpgradeWhiteInfo() {
        api.getUserAcls({featureTypes: ['KafkaDecreaseBrokerCount']}).then((target: {isExist: boolean}) => {
            this.data.set('KafkaDecreaseBrokerCount', target.isExist);
        });
        api.getUserAcls({featureTypes: ['KAFKA_ClusterMultipleUpdateWhiteList']}).then((res: {isExist: boolean}) => {
            this.data.set('KAFKA_ClusterMultipleUpdateWhiteList', res.isExist);
        });
    }

    changeUpgradeStatus(type: string) {
        if (this.data.get('KAFKA_ClusterMultipleUpdateWhiteList')) {
            return;
        }
        if (!this.data.get('flavorloading')) {
            const node = this.ref('network-config') as NetWork;
            const {type: upgradetype} = node.getUpgrageData();
            const notResizeEip = _.include([
                UpgrageType.ENABLE_CLUSTER_EIP,
                UpgrageType.DISABLE_CLUSTER_EIP,
                UpgrageType.ENABLE_CLUSTER_INTRANET_IP,
                UpgrageType.DISABLE_CLUSTER_INTRANET_IP,
            ], upgradetype);
            if (this.data.get('update_publicIp') && notResizeEip) {
                return;
            }
            else if (type === 'Network' && upgradetype === '') {
                // 点击重置按钮，重置网络带宽，触发bindwith监听，触发change-upgrade-statue
                return;
            }
            this.data.set('upgradeAccess', false);
            this.data.set('upgradeNode', false);
            this.data.set('upgradeDisk', false);
            this.data.set('upgradeConfig', false);
            this.data.set('upgradeNetwork', false);
            this.data.set(`upgrade${type}`, true);
            if (type === 'Network' && notResizeEip) {
                this.data.set('upgradeAccess', true);
                this.data.set('update_publicIp', true);
            }
        }
    }

    onSingleNumberTextNumberChange(target: {singleNumberTextNumber: number}) {
        this.data.set('singleNumberTextNumber', target.singleNumberTextNumber);
    }

    formateXuShangQueryPrice(priceData, oldPriceData) {
        const configsFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1
            },
            {
                name: 'type',
                value: priceData.xsType?.slice(6).toUpperCase(),
                scale: priceData.count
            },
            {
                name: priceData?.storageMeta?.storageType?.toLowerCase(),
                value: priceData.storageMeta.storageSize * priceData.storageMeta.numberOfDisk + 'G',
                scale: priceData.count
            },
            {
                name: 'eip',
                value: priceData.publicIpBandwidth + 'M',
                scale: priceData.count
            }
        ];
        const oldFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1
            },
            {
                name: 'type',
                value: oldPriceData.xsType?.slice(6).toUpperCase(),
                scale: oldPriceData.count
            },
            {
                name: oldPriceData?.storageMeta?.storageType?.toLowerCase(),
                value: oldPriceData?.storageMeta?.storageSize * oldPriceData?.storageMeta?.numberOfDisk + 'G',
                scale: oldPriceData.count
            },
            {
                name: 'eip',
                value: oldPriceData.publicIpBandwidth + 'M',
                scale: oldPriceData.count
            }
        ];
        let publicParam;
        let configs;
        if (priceData.payment === 'Postpaid') {
            publicParam = {
                ...postPaidParam,
                orderType: 'RESIZE',
                instanceId: this.data.get('clusterId'),
                region: this.$context.getCurrentRegionId()
            };
            configs = [
                {
                    ...publicParam,
                    flavor: [
                        ...configsFlavor
                    ]
                },
                {
                    ...publicParam,
                    flavor: [
                        ...oldFlavor
                    ]
                }
            ];
        }
        else {
            publicParam = {
                ...prePaidParam,
                duration: priceData.timeLength,
                timeUnit: priceData.timeUnit?.toUpperCase(),
                orderType: event?.isDownGrade ? 'SHRINKAGE' : 'DILATATION',
                instanceId: this.data.get('clusterId'),
                region: this.$context.getCurrentRegionId()
            };
            configs = [
                {
                    ...publicParam,
                    flavor: [
                        ...configsFlavor
                    ]
                }
            ];
        }
        return {publicParam, configs, configsFlavor, oldFlavor};
    }

    // 查询价格
    async onQueryPrice() {
        if (!window.location.href.includes(`#${ROUTE_PATH.clusterUpgrage}`)) {
            // 解决变更集群页打开后快速返回集群列表页，仍在询价问题的初步方案
            return;
        }
        this.data.set('priceLoading', true);
        let priceData: NormalObject = {};
        let oldPriceData: NormalObject = {};
        _.each(Refs, ref => {
            const node = this.ref(ref);
            if (node) {
                priceData = {
                    ...node.getDetailPrice(),
                    ...priceData
                };
                if (node.getOldPriceData) {
                    oldPriceData = {
                        ...node.getOldPriceData(),
                        ...oldPriceData
                    };
                }
            }
        });
        // 检测是否有值有空 除了publicIpEnabled、publicIpBandwidth
        for (let key of Object.keys(priceData)) {
            if (!/publicIpEnabled|publicIpBandwidth/.test(key) && !priceData[key]) {
                this.data.set('priceLoading', false);
                return Promise.reject();
            }
        }
        if (priceData.xsType === 'kafka.') {
            return Promise.reject();
        }
        const {publicParam, configs, configsFlavor, oldFlavor} = this.formateXuShangQueryPrice(priceData, oldPriceData);
        const payment: PaymentType = this.data.get('payment');
        this.data.set('orderType', publicParam.orderType);
        if (isXushang && priceData.payment === 'Postpaid') {
            return api.queryPriceInXS({configs}).then((result: [{price: number}, {price: number}]) => {
                this.data.set('clusterPrice', result[0].price);
                this.data.set('oldClusterPrice', result[1].price);
                this.data.set('margin', result[0].price);
                this.data.set('priceLoading', false);
                if (this.data.get('clickNext')) {
                    this.onNext();
                }
            });
        }
        else if (isXushang && priceData.payment === PaymentType.PREPAID) {
            await api
                .queryPriceInXS({
                    configs: [
                        {
                            ...publicParam,
                            orderType: 'NEW',
                            flavor: [
                                ...oldFlavor
                            ]
                        }
                    ]
                })
                .then((result: Array<{price: number}>) => {
                    this.data.set('oldClusterPrice', result[0].price);
                });
            await api
                .queryPriceInXS({
                    configs: [
                        {
                            ...publicParam,
                            orderType: 'NEW',
                            flavor: [
                                ...configsFlavor
                            ]
                        }
                    ]
                })
                .then((result: Array<{price: number}>) => {
                    this.data.set('clusterPrice', result[0].price);
                });
            return api.queryPriceInXS({configs}).then((result: Array<{price: number}>) => {
                this.data.set('margin', result[0].price);
                this.data.set('priceLoading', false);
                if (this.data.get('clickNext')) {
                    this.onNext();
                }
            });
        }
        else {
            return api
                .upgrageQueryPrice(this.data.get('clusterId'), {...priceData})
                .then(
                    (target: {
                        payment: PaymentType;
                        currentReferencePrice: number;
                        updateReferencePrice: number;
                        expirationTime: string;
                        margin: number;
                        timeLength: number;
                        timeUnit: string;
                    }) => {
                        this.data.set('oldClusterPrice', target.currentReferencePrice);
                        this.data.set('clusterPrice', target.updateReferencePrice);
                        this.data.set('expirationTime', target.expirationTime);
                        if (payment === PaymentType.PREPAID) {
                            this.data.set('timeLength', target.timeLength);
                            this.data.set('timeUnit', target.timeUnit);
                            this.data.set('margin', target.margin);
                        } else if (payment === PaymentType.POSTPAID) {
                            this.data.set('margin', target.updateReferencePrice);
                        }
                        this.data.set('priceLoading', false);
                        if (this.data.get('clickNext')) {
                            this.onNext();
                        }
                    }
                );
        }
    }

    // 获取详情信息
    getDetail(): Promise<void> {
        this.data.set('getLoading', true);
        return api
            .getClusterDetail(this.data.get('clusterId'), {})
            .then((detail: NormalObject) => {
                this.data.set('getLoading', false);
                this.nextTick(() => {
                    _.each(Refs, ref => {
                        const node: NodeClass = this.ref(ref);
                        node?.setData(detail);
                        this.data.set('payment', detail.payment);
                        this.data.set('detail', detail);
                        this.onSingleNumberTextNumberChange({
                            singleNumberTextNumber: detail.numberOfBrokerNodesPerZone * detail?.logicalZones?.length
                        });
                    });
                    this.nextTick(() => this.onQueryPrice());
                });
            })
            .catch(() => this.data.set('getLoading', false));
    }

    getConfirmParams() {
        const clusterConfigRef = (this.ref(ClusterRefType.CLUSTER) as ClusterConfig);
        const nodeConfigRef = (this.ref(ClusterRefType.NODE) as NodeConfig);
        const networkConfigRef = (this.ref(ClusterRefType.NETWORK) as NetWork);
        const accessConfigRef = (this.ref(ClusterRefType.ACCESS) as Access);
        const diskRef = (this.ref(ClusterRefType.DISK) as DiskConfig);
        const {types: clusterTypes, ...clusterConfigParams} = clusterConfigRef.getNewUpgrageData();
        let {types: accessTypes, ...accessParams} = accessConfigRef.getNewUpgrageData();
        const currentAccessParams = accessConfigRef.getUpgrageData();
        const {types: networkTypes, ...networkParams} = networkConfigRef.getNewUpgrageData(currentAccessParams);
        const {types: nodeTypes, ...nodeParams} = nodeConfigRef.getNewUpgrageData(networkParams);
        const {types: diskTypes, ...diskParams} = diskRef.getNewUpgrageData();

        let params = {
            ...clusterConfigParams,
            ...nodeParams,
            ...networkParams,
            ...accessParams,
            ...diskParams,
        };
        // 当公网开启/关闭、产品间转储开启/关闭——》需要传aclEnabled和authenticationMode，但是type取值为公网开启/关闭、产品间转储开启/关闭的type
        if (networkTypes.some((item: UpgrageType) => ([
            UpgrageType.ENABLE_CLUSTER_EIP,
            UpgrageType.DISABLE_CLUSTER_EIP,
            UpgrageType.ENABLE_CLUSTER_INTRANET_IP,
            UpgrageType.DISABLE_CLUSTER_INTRANET_IP,
        ].includes(item)))) {
            accessTypes = [];
        }

        return {
            types: [...clusterTypes, ...nodeTypes, ...diskTypes, ...networkTypes, ...accessTypes],
            params
        };
    }

    // 确认订单
    onConfirm() {
        let {types, params} = this.getConfirmParams();

        const needToBillingType = [
            UpgrageType.EXPAND_BROKER_DISK_CAPACITY,
            UpgrageType.INCREASE_BROKER_COUNT,
            UpgrageType.DECREASE_BROKER_COUNT,
            UpgrageType.RESIZE_CLUSTER_EIP_BANDWIDTH,
            UpgrageType.ENABLE_CLUSTER_EIP,
            UpgrageType.DISABLE_CLUSTER_EIP,
            UpgrageType.UPDATE_BROKER_NODE_TYPE,
        ];

        const isNeedToBilling = types.some(item => needToBillingType.includes(item));
        if (isNeedToBilling) {
            params = {
                ...params,
                couponIds: this.data.get('couponIds')
            };
        }

        // 多项变更时不用传type
        if (!this.data.get('KAFKA_ClusterMultipleUpdateWhiteList')) {
            params.type = types[0];
        }

        this.data.set('isSubmiting', true);

        api.updateCluster(this.data.get('clusterId'), params)
            .then((target: {orderId: string, clusterId: string}) => {
                // 提示成功
                this.data.set('isSubmiting', false);
                if (isNeedToBilling) {
                    this.renderOrder(this.data.get('payment'), target.orderId);
                } else {
                    Notification.success('变更提交成功');
                    redirect(`#${ROUTE_PATH.clusterList}`);
                }
            })
            .catch(() => this.data.set('isSubmiting', false));
    }

    onReset() {
        this.resetCurrentPrice();
        const {detail} = this.data.get('');
        const node = this.ref(ClusterRefType.NODE) as NodeConfig;
        const access = this.ref(ClusterRefType.ACCESS) as Access;
        const network = this.ref(ClusterRefType.NETWORK) as NetWork;
        const disk = this.ref(ClusterRefType.DISK) as DiskConfig;
        const cluster = this.ref(ClusterRefType.CLUSTER) as ClusterConfig;
        node?.resetDetail();
        access?.setData(detail);
        network?.setData(detail);
        disk?.setData(detail);
        cluster?.resetData(detail);
        this.nextTick(() => {
            this.data.set('upgradeAccess', true);
            this.data.set('upgradeNode', true);
            this.data.set('upgradeConfig', true);
            this.data.set('upgradeNetwork', true);
            this.data.set('upgradeDisk', true);
            this.data.set('update_publicIp', false);
        });
    }

    async getCouponList() {
        let param = {
            serviceType: 'KAFKA',
            orderItemPrice: this.data.get('totalPrice'),
            region: this.$context.getCurrentRegion().id,
            totalPrice: this.data.get('totalPrice'),
            properties: []
        };
        const result = await api.getCouponList(param);

        let couponList = [noCoupon];
        _.each(result, coupon => {
            couponList.push({
                text: this.getContent(coupon),
                value: coupon.id,
                title: coupon.balance
            });
        });

        this.data.set('couponList', couponList);
    }

    resetCurrentPrice() {
        const oldClusterPrice = this.data.get('oldClusterPrice');
        this.data.set('oldClusterPrice', oldClusterPrice);
        this.data.set('clusterPrice', oldClusterPrice);
        this.data.set('margin', 0);
    }

    // region 切换
    onRegionChange() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }
}
