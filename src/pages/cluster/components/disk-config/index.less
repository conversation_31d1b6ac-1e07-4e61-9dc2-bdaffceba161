@white-color: #ffffff;
.bms-cluster-create-disk {

    .storage-table {
        width: 500px;

        .s-table-row:hover {

            .s-inputnumber,
            .s-select {
                background-color: @white-color;
            }
        }
    }

    .policy-type {

        &__wrap {
            margin: 24px 0 0;
            padding: 16px 24px;
            background-color: var(--bgColor);
            border-radius: 4px;

            &_panel {
                margin-bottom: 16px;

                .s-input-area {
                    background-color: #fff;
                }

                .s-input .s-input-addon-after {
                    padding: 0;
                }

                .s-input-suffix-container {
                    input {
                        background: #F7F7F9;
                    }
                }

                .tip {
                    font-size: 12px;
                    color: #84868C;
                    line-height: 20px;
                    font-weight: 400;
                }

                &_label::before {
                    content: '*';
                    color: red;
                }
            }
        }

        .errTip {
            padding: 10px 0 10px 20px
        }
    }

    .form-item-storage-policy {
        .s-form-item-control-wrapper {
            flex: 1;
        }
    }
}
