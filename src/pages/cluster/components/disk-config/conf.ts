import {UpgrageType} from '../../util/conf';

export enum FrozenFieldsEnum {
    diskStorage = 'diskStorage',
    storagePolicy = 'storagePolicy',
    // storagePolicyEnabled = 'storagePolicyEnabled',
    // storagePolicyType = 'storagePolicyType',
    // diskUsedThresholdPercent = 'diskUsedThresholdPercent',
    // logMinRetentionMs = 'logMinRetentionMs',
    // logMinRetentionBytes = 'logMinRetentionBytes'
}
export const FrozenFields = [
    FrozenFieldsEnum.diskStorage,
    FrozenFieldsEnum.storagePolicy
    // FrozenFieldsEnum.storagePolicyEnabled,
    // FrozenFieldsEnum.storagePolicyType,
    // FrozenFieldsEnum.diskUsedThresholdPercent,
    // FrozenFieldsEnum.logMinRetentionMs,
    // FrozenFieldsEnum.logMinRetentionBytes
];

export enum FrozenFieldsUpgrageEnum {
    storagePolicyEnabled = 'storagePolicyEnabled',
    storagePolicy = 'storagePolicy',
    diskSizeGB = 'diskSizeGB'
}

export const FrozenFieldsUpgrageMap = {
    [FrozenFieldsUpgrageEnum.storagePolicyEnabled]: UpgrageType.UPDATE_STORAGE_POLICY,
    [FrozenFieldsUpgrageEnum.storagePolicy]: UpgrageType.UPDATE_STORAGE_POLICY,
    [FrozenFieldsUpgrageEnum.diskSizeGB]: UpgrageType.EXPAND_BROKER_DISK_CAPACITY,
};

export const FrozenFieldsUpgrage = {
    // [FrozenFieldsEnum.storagePolicyEnabled]: FrozenFieldsUpgrageEnum.storagePolicyEnabled,
    // [FrozenFieldsEnum.storagePolicyType]: FrozenFieldsUpgrageEnum.storagePolicy,
    // [FrozenFieldsEnum.diskUsedThresholdPercent]: FrozenFieldsUpgrageEnum.storagePolicy,
    // [FrozenFieldsEnum.logMinRetentionMs]: FrozenFieldsUpgrageEnum.storagePolicy,
    // [FrozenFieldsEnum.logMinRetentionBytes]: FrozenFieldsUpgrageEnum.storagePolicy,
    [FrozenFieldsEnum.storagePolicy]: FrozenFieldsUpgrageEnum.storagePolicy,
    [FrozenFieldsEnum.diskStorage]: FrozenFieldsUpgrageEnum.diskSizeGB
};

export enum ITimeUnit {
    Hour = 'hour',
    Day = 'day',
}

export const TimeUnits = [
    {text: '小时', value: ITimeUnit.Hour},
    {text: '天', value: ITimeUnit.Day},
];