/* eslint-disable max-len */
/**
 * 磁盘配置模块
 *
 * @file disk-config.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Select, Form, Radio, InputNumber, Checkbox, Loading, Table, Badge, Switch, Button, Input} from '@baidu/sui';
import {DiskType} from '@baidu/bce-bcc-sdk-enum';
import {diskNums2, diskNums4, diskNums8} from '@/common/enums/index';
import {UpgrageType} from '../../util/conf';
import {INPUT_WIDTH, SELECT_HEIGHT, CELL, OneHourMs, OneDayMs} from '@/common/config';
import {VAILDITE_ITEMS} from '@/common/rules';
import api from '@/common/client';
import {ClusterRefType, storagePolicyType} from '@/common/enums/constant';
import {storagePolicyEnum} from '@/common/enums/index';
import BaseCmpt from '../base-cmpt';
import Tip from '@/components/tip';
import {UtilHandler} from '../../util/util';
import './index.less';
import {InputNumberSelect} from '@/components';
import {ITimeUnit, TimeUnits} from './conf';
import {versionType} from '../../list';

const klass = 'bms-cluster-create-disk';

const TextMap = {
    title: '存储配置',
    storageType: '磁盘类型：',
    storageSize: '单节点容量：',
    nodeStorageSize: '节点磁盘容量：',
    totalStorageSize: '总磁盘容量',
    diskNum: '个',
    diskStorage: '磁盘大小：',
    logRetentionMs: '消息保留时长：',
    storagePolicyEnabled: '磁盘水位处理：',
    storagePolicy: '磁盘阈值策略：',
    remoteStorageEnabled: '远程存储：',
    logLocalRetentionMs: '本地保留时长：',
};

class DiskThroeshod extends Component {
    static template = html`
    <s-form s-ref="form" data="{{formData}}">
        <s-form-item label="${TextMap.storagePolicy}" class="form-item-storage-policy">
            <s-radio-group
                value="{{formData.storagePolicy}}"
                on-change="onStoragePolicyChange"
                radioType="button"
                disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                datasource="{{storagePolicys}}}"
            >
            </s-radio-group>
            <div class="policy-type__wrap">
                <div class="policy-type__wrap_panel">
                    <span class="policy-type__wrap_panel_label">
                        磁盘容量阈值：
                    </span>
                    <s-inputnumber
                        value="{{formData.diskUsedThresholdPercent}}"
                        min="{{70}}"
                        max="{{diskUsedThresholdPercentMax}}"
                        stepStrictly
                        on-change="onPercentChange"
                        disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                    />
                    <span>%</span>
                    <p class="tip">范围：70%-{{diskUsedThresholdPercentMax}}%{{computedTip}}</p>
                </div>
                <div class="policy-type__wrap_panel" s-if="formData.storagePolicy !== storagePolicyType.AUTO_DELETE">
                    <span class="policy-type__wrap_panel_label">
                        {{formData.storagePolicy !== storagePolicyType.AUTO_EXPAND ? '缩短保留时间：' : '磁盘扩容步长：'}}
                    </span>
                    <s-inputnumber
                        value="{= formData.stepForwardPercent =}"
                        min="{{10}}"
                        max="{{50}}"
                        stepStrictly
                        on-change="onPolicyChange"
                        disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                    />
                    <span>%</span>
                    <span s-if="formData.storagePolicy === storagePolicyType.AUTO_EXPAND">
                        或
                        <s-inputnumber
                            value="{= formData.stepForwardSize =}"
                            min="{{10}}"
                            max="{{100}}"
                            stepStrictly
                            on-change="onPolicyChange"
                            disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                        />
                        <span>GB</span>
                    </span>
                    <p class="tip mt8">{{stepForwardPercentTip}}</p>
                </div>
                <div class="policy-type__wrap_panel" s-if="formData.storagePolicy !== storagePolicyType.AUTO_EXPAND">
                    <span class="policy-type__wrap_panel_label">
                        保底时长：
                    </span>
                    <s-input
                        value="{= formData.logMinRetentionMs =}"
                        width="110"
                        on-input="onTimeInput"
                        disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                    >
                        <s-select
                            slot="addonAfter"
                            width="70"
                            value="{{timeUnit}}"
                            datasource="{{timeUnits}}"
                            on-change="handleChangeTimeUnit"
                            disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                        >
                        </s-select>
                    </s-input>
                    <p class="tip mt8">{{logMinRetentionMsTip}}</p>
                </div>
                <div class="policy-type__wrap_panel" s-if="formData.storagePolicy === storagePolicyType.AUTO_DELETE">
                    <span class="policy-type__wrap_panel_label">
                        保底大小：
                    </span>
                    <s-input
                        value="{= formData.logMinRetentionBytes =}"
                        width="110"
                        disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                        on-input="onSizeInput"
                    >
                        <s-select
                            slot="addonAfter"
                            width="70"
                            value="{{storageUnit}}"
                            datasource="{{storageUnits}}"
                            on-change="handleChangeStorageUnit"
                            disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                        >
                        </s-select>
                    </s-input>
                    <p class="tip mt8">{{logMinRetentionBytesTip}}</p>
                </div>
                <div class="policy-type__wrap_panel" s-if="formData.storagePolicy === storagePolicyType.AUTO_EXPAND">
                    <span class="policy-type__wrap_panel_label">
                        最大容量：
                    </span>
                    <s-inputnumber
                        value="{= formData.maxStorageSize =}"
                        min="{{100}}"
                        max="{{32000}}"
                        stepStrictly
                        on-change="onPolicyChange"
                        disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                    />
                    <span>GB</span>
                    <p class="tip mt8">范围：100GB到32T，自动扩容到该规格后不再触发自动扩容</p>
                </div>
            </div>
        </s-form-item>
    </s-form>`;

    static components = {
        's-inputnumber': InputNumber,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
    };

    static computed = {
        byteMax(): number {
            const storageUnit = this.data.get('storageUnit');
            switch (storageUnit) {
                case 'GB':
                    return 100;
                case 'MB':
                    return 1024 * 100;
            }
            return 100;
        },
        computedMs(): number {
            const logMinRetentionMs = this.data.get('formData.logMinRetentionMs');
            const timeUnit = this.data.get('timeUnit');
            switch (timeUnit) {
                case 'day':
                    return logMinRetentionMs * 24 * 60 * 60 * 1000;
                case 'hour':
                    return logMinRetentionMs * 60 * 60 * 1000;
            }
            return logMinRetentionMs;
        },
        computedBytes(): number {
            const logMinRetentionBytes = this.data.get('formData.logMinRetentionBytes');
            const storageUnit = this.data.get('storageUnit');
            switch (storageUnit) {
                case 'GB':
                    return logMinRetentionBytes * 1024 * 1024 * 1024;
                case 'MB':
                    return logMinRetentionBytes * 1024 * 1024;
            }
            return logMinRetentionBytes;
        },
        stepForwardPercentTip(): string {
            const storagePolicy = this.data.get('formData.storagePolicy');
            return storagePolicy === storagePolicyType.DYNAMIC_RETENTION ? '向前调整消息保留时间后，集群会根据新的保留时间删除部分最早的消息，以保证磁盘容量充足，该效果会有一定的延迟' : '当磁盘使用达到容量阈值后，集群会根据设置的扩容最大步长（比例/增量）增加存储，以保证磁盘容量充足，存储扩展操作每六小时只能执行一次';
        },
        diskUsedThresholdPercentMax(): number {
            const diskStorage = this.data.get('storageMetaSource[0].diskStorage');
            if (diskStorage > 2000) {
                return Math.floor((1 - (200 / diskStorage)) * 100);
            }
            return 90;
        },
        computedTip(): string {
            const storagePolicy = this.data.get('formData.storagePolicy');
            switch (storagePolicy) {
                case storagePolicyType.AUTO_DELETE:
                    return '，当磁盘使用达到容量阈值后，集群会按比例删除该磁盘上所有主题分区部分最早的消息，以保证磁盘容量充足，但会导致相关主题分区副本在不同节点储存不均衡';
            }
            return '';
        },
        logMinRetentionMsTip(): string {
            const storagePolicy = this.data.get('formData.storagePolicy');
            switch (storagePolicy) {
                case storagePolicyType.DYNAMIC_RETENTION:
                    return '范围：1小时至7天，保留时间到该时间后不再触发动态调整';
            }
            return '';
        }
    };

    initData() {
        return {
            logMinRetentionBytesTip: '自动删除到该主题分区最小保留时长或者最小保留大小后不再继续删除',
            timeUnits: [
                {text: '小时', value: 'hour'},
                {text: '天', value: 'day'},
            ],
            storageUnits: [
                {label: 'MB', value: 'MB'},
                {label: 'GB', value: 'GB'},
            ],
            storageUnit: 'GB',
            timeUnit: 'hour',
            storagePolicyType: storagePolicyType,
            storagePolicys: storagePolicyEnum.toArray(),
            formData: {
                storagePolicy: storagePolicyType.AUTO_DELETE,
                diskUsedThresholdPercent: 75,
                logMinRetentionMs: 1,
                logMinRetentionBytes: 2,
                stepForwardPercent: 10,
                stepForwardSize: 10,
                maxStorageSize: 100
            },
        };
    }

    attached() {
        if (this.data.get('isDetail')) {
            this.watch('resetFlag', () => {
                this.setData(this.data.get('obj'));
            });
        }
    }

    onPercentChange(target: {value: string}) {
        this.data.set('formData.diskUsedThresholdPercent', target.value);
        this.onPolicyChange();
    }

    onPolicyChange() {
        this.fire('policy-change', {});
    }

    onStoragePolicyChange(target: {value: string}) {
        this.data.set('formData.storagePolicy', target.value);
        this.onPolicyChange();
    }

    handleChangeTimeUnit(target: {value: string}) {
        this.data.set('timeUnit', target.value);
        const timeValue = this.data.get('formData.logMinRetentionMs');
        if (target.value === 'day' && parseInt(timeValue, 10) > 7) {
            this.data.set('formData.logMinRetentionMs', 7);
        }
        this.onPolicyChange();
    }

    handleChangeStorageUnit(target: {value: string}) {
        this.data.set('storageUnit', target.value);
        const sizeValue = this.data.get('formData.logMinRetentionBytes');
        const sizeMax = this.data.get('byteMax');
        if (parseInt(sizeValue, 10) > sizeMax) {
            this.data.set('formData.logMinRetentionBytes', sizeMax);
        }
        this.onPolicyChange();
    }

    onTimeInput(target: {value: string}) {
        const temp = parseInt(target.value, 10);
        const timeUnit = this.data.get('timeUnit');
        const max = timeUnit === 'hour' ? 168 : 7;
        let content = temp;
        if (isNaN(temp)) {
            content = 1;
        }
        else if (temp >= 10 && timeUnit === 'day') {
            const num = Math.floor(temp % 10);
            content = num <= max ? (num >  0 ? num : 1) : 7;
        }
        else if (temp < 1 || temp > max) {
            content = temp < 1 ? 1 : max;
        }
        this.nextTick(() => {
            this.data.set('formData.logMinRetentionMs', content);
            this.onPolicyChange();
        });
    }

    onSizeInput(target: {value: string}) {
        const temp = parseInt(target.value, 10);
        const sizeMax = this.data.get('byteMax');
        let content = temp;
        if (isNaN(temp)) {
            content = 1;
        }
        else if (temp < 1 || temp > sizeMax) {
            content = temp < 1 ? 1 : sizeMax;
        }
        this.nextTick(() => {
            this.data.set('formData.logMinRetentionBytes', content);
            this.onPolicyChange();
        });
    }

    getConfirmData() {
        const {
            diskUsedThresholdPercent,
            storagePolicy,
            maxStorageSize,
            stepForwardSize,
            stepForwardPercent
        } = this.data.get('formData');
        const {computedMs, computedBytes} = this.data.get('');
        switch (storagePolicy) {
            case storagePolicyType.AUTO_DELETE:
                return {
                    type: storagePolicy,
                    autoDelete: {
                        diskUsedThresholdPercent: diskUsedThresholdPercent,
                        logMinRetentionMs: computedMs,
                        logMinRetentionBytes: computedBytes
                    }
                };
            case storagePolicyType.AUTO_EXPAND:
                return {
                    type: storagePolicy,
                    autoExpand: {
                        diskUsedThresholdPercent: diskUsedThresholdPercent,
                        maxStorageSize,
                        stepForwardSize,
                        stepForwardPercent
                    }
                };
            case storagePolicyType.DYNAMIC_RETENTION:
                return {
                    type: storagePolicy,
                    dynamicRetention: {
                        diskUsedThresholdPercent: diskUsedThresholdPercent,
                        logMinRetentionMs: computedMs,
                        stepForwardPercent
                    }
                };
        }
    }

    getUpgradeData() {
        const {
            diskUsedThresholdPercent,
            storagePolicy,
            maxStorageSize,
            stepForwardSize,
            stepForwardPercent
        } = this.data.get('formData');
        const {computedMs, computedBytes} = this.data.get('');
        switch (storagePolicy) {
            case storagePolicyType.AUTO_DELETE:
                return {
                    type: storagePolicy,
                    autoDelete: {
                        diskUsedThresholdPercent: diskUsedThresholdPercent,
                        logMinRetentionMs: computedMs,
                        logMinRetentionBytes: computedBytes
                    }
                };
            case storagePolicyType.AUTO_EXPAND:
                return {
                    type: storagePolicy,
                    autoExpand: {
                        diskUsedThresholdPercent: diskUsedThresholdPercent,
                        maxStorageSize,
                        stepForwardSize,
                        stepForwardPercent
                    }
                };
            case storagePolicyType.DYNAMIC_RETENTION:
                return {
                    type: storagePolicy,
                    dynamicRetention: {
                        diskUsedThresholdPercent: diskUsedThresholdPercent,
                        logMinRetentionMs: computedMs,
                        stepForwardPercent
                    }
                };
        }
    }

    hasStoragePolicyChange() {
        const storagePolicyTypeData = this.data.get('formData.storagePolicy');
        const computedBytes = this.data.get('computedBytes');
        const computedMs = this.data.get('computedMs');
        const formData = this.data.get('formData');
        const originalDataStoragePolicy = this.data.get('originalData.storagePolicy');
        if (storagePolicyTypeData !== originalDataStoragePolicy?.type) {
            return true;
        }
        else {
            let change = false;
            if (storagePolicyTypeData === storagePolicyType.AUTO_DELETE) {
                if (computedBytes !== originalDataStoragePolicy?.logMinRetentionBytes) {
                    change = true;
                }
                else if (computedMs !== originalDataStoragePolicy?.logMinRetentionMs) {
                    change = true;
                }
                else if (formData.diskUsedThresholdPercent !== originalDataStoragePolicy?.diskUsedThresholdPercent) {
                    change = true;
                }
            }
            else if (storagePolicyTypeData === storagePolicyType.AUTO_EXPAND) {
                if (formData.stepForwardPercent !== originalDataStoragePolicy?.stepForwardPercent) {
                    change = true;
                }
                else if (formData.stepForwardSize !== originalDataStoragePolicy?.stepForwardSize) {
                    change = true;
                }
                else if (formData.maxStorageSize !== originalDataStoragePolicy.maxStorageSize) {
                    change = true;
                }
                else if (formData.diskUsedThresholdPercent !== originalDataStoragePolicy?.diskUsedThresholdPercent) {
                    change = true;
                }
            }
            else {
                if (formData.stepForwardPercent !== originalDataStoragePolicy?.stepForwardPercent) {
                    change = true;
                }
                else if (computedMs !== originalDataStoragePolicy?.logMinRetentionMs) {
                    change = true;
                }
                else if (formData.diskUsedThresholdPercent !== originalDataStoragePolicy?.diskUsedThresholdPercent) {
                    change = true;
                }
            }
            return change;
        }
    }

    formateTimes(time: number) {
        if (time < 1000 * 60 * 60 * 24 && time >= 1000 * 60 * 60) {
            return {
                value: time / (1000 * 60 * 60),
                unit: 'hour'
            };
        }
        else if (time >= 1000 * 60 * 60 * 24 && time % (1000 * 60 * 60 * 24) === 0) {
            return {
                value: time / (1000 * 60 * 60 * 24),
                unit: 'day'
            };
        }
        else {
            return {
                value: time / (1000 * 60 * 60),
                unit: 'hour'
            };
        }
    }

    formatBytes(bytes: number) {
        if (bytes < 1024 * 1024 * 1024) {
            return {
                value: Math.floor(bytes / (1024 * 1024)),
                unit: 'MB'
            };
        }
        else {
            if (bytes % (1024 * 1024 * 1024) !== 0) {
                return {
                    value: Math.floor(bytes / (1024 * 1024)),
                    unit: 'MB'
                };
            }
            else {
                return {
                    value: Math.floor(bytes / (1024 * 1024 * 1024)),
                    unit: 'GB'
                };
            }
        }
    }

    // 为了变更配置使用
    async setData(obj: NormalObject) {
        const {storagePolicy} = obj;
        const type = storagePolicy?.type || 'AUTO_DELETE';
        const {
            diskUsedThresholdPercent = 70,
            logMinRetentionMs = 1000 * 60 * 60,
            logMinRetentionBytes = 2 * 1024 * 1024 * 1024,
            stepForwardPercent = 10,
            stepForwardSize = 10,
            maxStorageSize = 100
        } = storagePolicy?.autoDelete || storagePolicy?.autoExpand || storagePolicy?.dynamicRetention;
        const {value, unit} = this.formatBytes(logMinRetentionBytes);
        const {value: timeValue, unit: timeUnit} = this.formateTimes(logMinRetentionMs);
        this.data.merge('originalData', {
            ...obj,
            storagePolicy: {
                type,
                diskUsedThresholdPercent,
                logMinRetentionMs,
                logMinRetentionBytes,
                stepForwardPercent,
                stepForwardSize,
                maxStorageSize
            }
        });
        this.data.merge('formData', {
            storagePolicy: type,
            diskUsedThresholdPercent,
            logMinRetentionMs: timeValue,
            logMinRetentionBytes: value,
            stepForwardPercent,
            stepForwardSize,
            maxStorageSize
        });
        this.data.set('storageUnit', unit);
        this.data.set('timeUnit', timeUnit);
    }
}
export default class DiskConfig extends BaseCmpt {
    static template = html` <div class="${klass} bms-form-panel">
        <s-append noHighlight label="${TextMap.title}" />
        <s-form s-ref="form" rules="{{rules}}" data="{{formData}}">
            <s-formitem label="${TextMap.storageType}" prop="storageType">
                <s-select
                    s-if="{{!isDetail}}"
                    datasource="{{diskTypes}}"
                    value="{{formData.storageType}}"
                    disabled="{{payload === 'low'}}"
                    width="${INPUT_WIDTH}"
                    height="${SELECT_HEIGHT}"
                    on-change="onChangeStorageType"
                    placeholder="{{selectPlaceholder}}"
                    loading="{{flavorloading}}"
                />
                <span class="upgrage-text" s-else> {{formData.storageType | formatStorageType}} </span>
            </s-formitem>
            <s-formitem s-if="isMultipleDisks" label="${TextMap.storageSize}">
                <s-table datasource="{{storageMetaSource}}" columns="{{storageMetaColumns}}" class="storage-table">
                    <div slot="c-diskNum">
                        <s-select
                            datasource="{{diskNums}}"
                            value="{= row.diskNum =}"
                            width="80"
                            height="${SELECT_HEIGHT}"
                            getPopupContainer="{{getPopupContainer}}"
                            on-change="onDiskChange('diskNum', $event, rowIndex)"
                            disabled="{{isDetail || payload === 'low'}}"
                        />
                        <span class="ml5">${TextMap.diskNum}</span>
                    </div>
                    <div slot="c-diskStorage">
                        <s-inputnumber
                            value="{= row.diskStorage =}"
                            min="{{minLimit.diskStorage || 100}}"
                            max="{{32000}}"
                            step="{{10}}"
                            stepStrictly
                            on-change="onDiskChange('diskStorage', $event, rowIndex)"
                            disabled="{{disabledByModified.storagePolicy || !isUpgrading }}"
                        />
                        <span class="ml5">${CELL.GB}</span>
                    </div>
                    <span slot="c-total">
                        {{row.total}}
                        <span class="ml5">${CELL.GB}</span>
                    </span>
                </s-table>
                <p class="desc mt10">${TextMap.totalStorageSize}为{{totalDiskStorage}}${CELL.GB}</p>
                <p class="desc mt10" s-if="{{isDetail && isTotalDiskStorageSizeChange}}">
                    当前${TextMap.totalStorageSize} {{originTotalDiskStorageSize}} ${CELL.GB}
                </p>
            </s-formitem>
            <s-formitem s-else label="${TextMap.diskStorage}">
                <s-inputnumber
                    value="{= storageMetaSource[0].diskStorage =}"
                    min="{{minLimit.diskStorage || 100}}"
                    max="{{32000}}"
                    step="{{10}}"
                    stepStrictly
                    on-change="onDiskChange('diskStorage', $event, 0)"
                    disabled="{{disabledByModified.storagePolicy|| !isUpgrading }}"
                />
                <span class="ml5">${CELL.GB}</span>
                <p class="desc mt10">磁盘大小为单节点的磁盘大小</p>
                <template s-if="{{isDetail && isTotalDiskStorageSizeChange}}">
                    <p class="desc mt10">${TextMap.totalStorageSize}为{{totalDiskStorage}}${CELL.GB}</p>
                    <p class="desc mt10">
                        当前${TextMap.totalStorageSize} {{originTotalDiskStorageSize}} ${CELL.GB}
                    </p>
                </template>
            </s-formitem>
            <s-formitem
                s-if="!isDetail && isDefaultConfig"
                prop="logRetentionMs"
            >
                <span slot="label">
                    ${TextMap.logRetentionMs}
                    <tip-cmpt type="question">消息的最长保留时间</tip-cmpt>
                </span>
                <input-number-select
                    value="{= formData.logRetentionMs =}"
                    options="{{timeUnits}}"
                    selectValue="{= logRetentionMsUnit =}"
                    max="{{logRetentionMsUnit === ITimeUnit.Hour ? 365 * 24 : 365}}"
                    min="{{1}}"
                    on-input-change="onLogRetentionMsChange"
                    on-select-change="onLogRetentionMsUnitChange"
                />
            </s-formitem>
            <s-formitem class="form-item-center">
                <span slot="label">
                    ${TextMap.storagePolicyEnabled}
                    <tip-cmpt type="question">
                        开启磁盘水位处理后，集群将根据容量阈值策略自动调整磁盘空间
                    </tip-cmpt>
                </span>
                <s-switch
                    checked="{= formData.storagePolicyEnabled =}"
                    disabled="{{disabledByModified.diskStorage || !isUpgrading }}"
                    on-change="onPolicyChange"
                />
                <p class="desc mt4">
                    生产环境或业务上量期间，<span class="yellow">建议开启</span>磁盘水位处理，根据业务情况<span class="yellow">选择合适</span>策略，避免流量突增导致磁盘打满
                </p>
            </s-formitem>
            <disk-throeshod
                s-ref="disk-throeshod"
                s-if="{{formData.storagePolicyEnabled}}"
                on-policy-change="onPolicyChange"
                storageMetaSource="{{storageMetaSource}}"
                disabledByModified="{{disabledByModified}}"
                isUpgrading="{{isUpgrading}}"
                isDetail="{{isDetail}}"
                obj="{{obj}}"
                resetFlag="{{resetFlag}}"
            />
            <s-formitem
                s-if="showRemoteStorage"
                class="form-item-center"
            >
                <span slot="label">
                    ${TextMap.remoteStorageEnabled}
                    <tip-cmpt type="question">
                        通过将数据保存在远程，提高数据保留时长并降低存储成本
                    </tip-cmpt>
                </span>
                <s-switch
                    checked="{= formData.remoteStorageEnabled =}"
                />
            </s-formitem>
            <s-formitem
                s-if="showLogLocalRetentionMs"
                prop="logLocalRetentionMs"
            >
                <span slot="label">
                    ${TextMap.logLocalRetentionMs}
                    <tip-cmpt type="question">
                        开启远程存储后，消息在本地磁盘的保留时间，超过此时间且未达到最长保留时间的消息会被复制到远程存储
                    </tip-cmpt>
                </span>
                <input-number-select
                    value="{= formData.logLocalRetentionMs =}"
                    options="{{timeUnits}}"
                    selectValue="{= logLocalRetentionMsUnit =}"
                    max="{{logLocalRetentionMsUnit === ITimeUnit.Hour ? 720 : 30}}"
                    min="{{1}}"
                />
            </s-formitem>
        </s-form>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-inputnumber': InputNumber,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-formitem': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-loading': Loading,
        's-table': Table,
        's-switch': Switch,
        'tip-cmpt': Tip,
        's-button': Button,
        's-badge': Badge,
        'disk-throeshod': DiskThroeshod,
        'input-number-select': InputNumberSelect,
    };

    static computed = {
        diskNums(): any[] {
            const storageType = this.data.get('formData.storageType');
            const isMultipleDisksNumber = this.data.get('isMultipleDisksNumber');
            const MultipleDisksAnyNumberWhitelist = this.data.get('MultipleDisksAnyNumberWhitelist');
            if (MultipleDisksAnyNumberWhitelist) {
                return [1, 2, 3, 4, 5, 6, 7, 8];
            }
            else if (storageType === 'SSD' && isMultipleDisksNumber) {
                return diskNums8.toArray();
            }
            else if (storageType === 'ENHANCED_SSD_PL1' && !isMultipleDisksNumber) {
                return diskNums2.toArray();
            }
            return diskNums4.toArray();
        },
        totalDiskStorage(): number {
            const isMultipleDisks = this.data.get('isMultipleDisks');
            const total = this.data.get('storageMetaSource[0].total');
            const diskStorage = this.data.get('storageMetaSource[0].diskStorage');
            const singleNumberTextNumber = this.data.get('singleNumberTextNumber') || 1;
            return (isMultipleDisks ? total : diskStorage) * singleNumberTextNumber;
        },
        isTotalDiskStorageSizeChange(): boolean {
            const originalData = this.data.get('originalData');
            const storageMetaSource = this.data.get('storageMetaSource');
            const singleNumberTextNumber = this.data.get('singleNumberTextNumber');
            return originalData.diskStorage !== storageMetaSource[0].diskStorage || originalData.numberOfBrokerNodes !== singleNumberTextNumber;
        },
        originTotalDiskStorageSize(): number {
            const originalData = this.data.get('originalData');
            const storageMetaSource = this.data.get('storageMetaSource');
            const isMultipleDisks = this.data.get('isMultipleDisks');
            if (isMultipleDisks) {
                return storageMetaSource[0].diskNum * originalData.diskStorage * originalData.numberOfBrokerNodes;
            }
            return originalData.diskStorage * originalData.numberOfBrokerNodes;
        },
        // 是否是默认配置
        isDefaultConfig(): boolean {
            return this.data.get('config') === 'default';
        },
        showRemoteStorage(): boolean {
            const isDetail = this.data.get('isDetail');
            const selectVersion = this.data.get('selectVersion') as versionType;
            return !isDetail && selectVersion?.tieredStorageSupported;
        },
        showLogLocalRetentionMs(): boolean {
            const isDetail = this.data.get('isDetail');
            const {remoteStorageEnabled} = this.data.get('formData');
            const isDefaultConfig = this.data.get('isDefaultConfig');
            return !isDetail && isDefaultConfig && remoteStorageEnabled;
        },
    };

    static filters: SanFilterProps = {
        formatStorageType: UtilHandler.storageType
    };

    initData() {
        return {
            rules: {
                storageType: [VAILDITE_ITEMS.requiredSelect],
                logLocalRetentionMs: [
                    {validator: (_, value: number, callback: any) => {
                        const logLocalRetentionMsUnit = this.data.get('logLocalRetentionMsUnit');
                        const logRetentionMsUnit = this.data.get('logRetentionMsUnit');
                        const {logRetentionMs} = this.data.get('formData');
                        const finalLogLocalRetentionMs = value * (logLocalRetentionMsUnit === ITimeUnit.Hour ? OneHourMs : OneDayMs);
                        const finalLogRetentionMs = logRetentionMs * (logRetentionMsUnit === ITimeUnit.Hour ? OneHourMs : OneDayMs);

                        if (finalLogLocalRetentionMs >= finalLogRetentionMs) {
                            return callback('本地保留时长要小于消息保留时长');
                        }
                        callback();
                    }}
                ]
            },
            storagePolicyType: storagePolicyType,
            formData: {
                storageType: '',
                storagePolicyEnabled: false,
                logRetentionMs: 7,
                remoteStorageEnabled: false,
                logLocalRetentionMs: 1
            },
            storageMetaColumns: [
                {name: 'diskNum', label: '磁盘个数'},
                {name: 'diskStorage', label: '单盘容量'},
                {name: 'total', label: '总容量'}
            ],
            diskTypes: [],
            storageMetaSource: [{diskNum: 1, diskStorage: 100, total: 100}],
            isMultipleDisks: false, // 功能白名单
            isMultipleDisksNumber: false, // 功能白名单
            MultipleDisksAnyNumberWhitelist: false,
            getPopupContainer: () => document.body,
            // 集群变更下，备份信息，以便单次使用
            originalData: {},
            // 集群变更下，禁用使用
            disabledByModified: {
                storagePolicy: false,
                diskStorage: false
            },
            timeUnits: TimeUnits,
            storageUnits: [
                {label: 'MB', value: 'MB'},
                {label: 'GB', value: 'GB'},
            ],
            storageUnit: 'GB',
            timeUnit: 'hour',
            minLimit: {},
            resetFlag: false,
            logRetentionMsUnit: ITimeUnit.Day,
            logLocalRetentionMsUnit: ITimeUnit.Hour,
            ITimeUnit,
            config: '', // 由【集群配置—集群配置】触发值修改
            selectVersion: null, // 由【集群配置-集群版本】切换触发值修改
        };
    }

    async attached() {
        // 根据node-config节点的参数设置磁盘类型数组
        this.watch('cdsArr', value => {
            const diskTypes = _.map(value, i => ({
                value: i.storageType,
                text: DiskType.getTextFromAlias(i.storageType)
            }));
            this.data.set('diskTypes', diskTypes);
            if (this.data.get('isDetail')) {
                this.data.set('formData.storageType', this.data.get('originalData.storageMeta.storageType'));
            }
        });
        this.watch('payload', value => {
            if (value === 'low') {
                this.data.set('formData.storageType', 'SSD');
            }
        });
        this.watch('applyData', value => {
            this.data.set('storageMetaSource', value.storageMetaSource);
            this.data.set('formData.storageType', value.storageType);
        });
        this.getDiskWhiteInfo();
    }

    handleVersionChange(selectVersion: versionType) {
        this.data.set('selectVersion', selectVersion);
    }

    handleConfigChange(e: {value: string}) {
        this.data.set('config', e.value);
    }

    // 校验
    verify() {
        const ref = this.ref('form') as unknown as Form;
        return Promise.all([
            ref.validateFields()
        ]).catch((err: Error) => {
            throw ClusterRefType.DISK;
        });
    }

    // 获取磁盘白名单信息
    getDiskWhiteInfo() {
        return api.getUserAcls({featureTypes: ['MultipleDisksWhitelist']}).then((target: {isExist: boolean}) => {
            this.data.set('isMultipleDisks', target.isExist);
            if (target.isExist) {
                api.getUserAcls({featureTypes: ['MultipleDisksNumberWhitelist']}).then((target: {isExist: boolean}) => {
                    this.data.set('isMultipleDisksNumber', target.isExist);
                });
                api.getUserAcls({featureTypes: ['MultipleDisksAnyNumberWhitelist']}).then((target: {isExist: boolean}) => {
                    this.data.set('MultipleDisksAnyNumberWhitelist', target.isExist);
                });
            }
        });
    }

    onChangeStorageType(target: {value: string}) {
        this.data.set('formData.storageType', target.value);
        this.onDiskChange('diskNum', {value: 1}, 0);
        this.onPriceConfigChange();
    }

    // 磁盘大小或者磁盘容量的改变
    onDiskChange(type: 'diskNum' | 'diskStorage', target: {value: number}, rowIndex: number) {
        const item = this.data.get(`storageMetaSource[${rowIndex}]`);
        const total = target.value * (type === 'diskNum' ? item.diskStorage : item.diskNum);
        this.data.merge(`storageMetaSource[${rowIndex}]`, {
            [type]: target.value,
            total
        });
        this.setFrozenField();
        this.fire('change-upgrade-status', {});
        this.onPriceConfigChange();
    }

    onLogRetentionMsChange() {
        this.nextTick(() => {
            this.ref('form')?.validateFields(['logLocalRetentionMs']);
        });
    }

    onLogRetentionMsUnitChange() {
        this.nextTick(() => {
            this.ref('form')?.validateFields(['logLocalRetentionMs']);
        });
    }

    onPolicyChange() {
        this.nextTick(() => {
            this.setFrozenField();
            this.fire('change-upgrade-status', {});
        });
    }

    setFrozenField() {
        if (this.data.get('isDetail')) {
            if (this.data.get('KAFKA_ClusterMultipleUpdateWhiteList')) {
                return;
            }
            const {storageSize} = this.data.get('originalData.storageMeta');
            const isStoragePolicyEnableChange = this.data.get('formData.storagePolicyEnabled') !== this.data.get('originalData.storagePolicyEnabled');
            const hasStoragePolicyChange = isStoragePolicyEnableChange || this.ref('disk-throeshod')?.hasStoragePolicyChange();
            const old = storageSize;
            const newValue = this.data.get('storageMetaSource[0].diskStorage');
            this.data.set('disabledByModified.diskStorage', old !== newValue);
            this.data.set('disabledByModified.storagePolicy', hasStoragePolicyChange);
            if (old === newValue && !hasStoragePolicyChange) {
                // 价格没发生变化，触发nochange事件
                this.fire('nochange', {});
                // 直接返回，不轮询价格
                return;
            }
        }

    }

    onPriceConfigChange() {
        this.fire('price-config-change', {});
    }


    checkISChange() {
        const {isDetail} = this.data.get('');
        if (!isDetail) {
            return true;
        }
        const isStoragePolicyEnableChange = this.data.get('formData.storagePolicyEnabled') !== this.data.get('originalData.storagePolicyEnabled');
        const hasStoragePolicyChange = isStoragePolicyEnableChange || this.ref('disk-throeshod')?.hasStoragePolicyChange();
        const {storageSize, numberOfDisk} = this.data.get('originalData.storageMeta') || {};
        const old = storageSize * numberOfDisk;
        const newValue = this.data.get('storageMetaSource[0].diskStorage') * this.data.get('storageMetaSource[0].diskNum');
        const bool = hasStoragePolicyChange || newValue !== old;
        return bool;
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        const {formData, storageMetaSource} = this.data.get('');
        const {storageType} = formData;
        return {
            storageMeta: {
                storageSize: storageMetaSource[0].diskStorage,
                numberOfDisk: storageMetaSource[0].diskNum,
                storageType
            }
        };
    }

    getOldPriceData() {
        return this.getPriceData();
    }

    getDetailPrice() {
        return this.getPriceData();
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const {formData, logRetentionMsUnit, logLocalRetentionMsUnit} = this.data.get('');
        const {
            storagePolicyEnabled,
            remoteStorageEnabled,
            logRetentionMs,
            logLocalRetentionMs,
        } = formData;
        let params: NormalObject = {
            ...this.getPriceData(),
            storagePolicyEnabled,
            remoteStorageEnabled,
        };
        if (storagePolicyEnabled) {
            params.storagePolicy = this.ref('disk-throeshod').getConfirmData();
        }

        if (this.data.get('isDefaultConfig')) {
            params.logRetentionMs = logRetentionMs * (logRetentionMsUnit === ITimeUnit.Hour ? OneHourMs : OneDayMs);
            params.logLocalRetentionMs = logLocalRetentionMs * (logLocalRetentionMsUnit === ITimeUnit.Hour ? OneHourMs : OneDayMs);
        }

        return params;
    }

    // 获取变更参数
    getUpgrageData() {
        const {storagePolicyEnabled, storagePolicy, autoDelete} = this.data.get('formData');
        const {storageSize} = this.data.get('originalData.storageMeta');
        const old = storageSize;
        const newValue = this.data.get('storageMetaSource[0].diskStorage');
        const {computedMs, computedBytes} = this.data.get('');
        let type;
        let propValue;
        let storagePolicyObj;
        if (storagePolicyEnabled) {
            storagePolicyObj = this.ref('disk-throeshod').getUpgradeData();
        }
        if (old !== newValue) {
            type = UpgrageType.EXPAND_BROKER_DISK_CAPACITY,
            propValue = newValue;
            this.data.set('upgradeType', type);
            return {
                type,
                diskSizeGB: propValue,
            };
        }
        else {
            type = UpgrageType.UPDATE_STORAGE_POLICY;
            this.data.set('upgradeType', type);
            propValue = storagePolicyEnabled ? {
                storagePolicyEnabled,
                storagePolicy: storagePolicyObj
            } : {
                storagePolicyEnabled
            };
            return {
                type,
                ...propValue,
            };
        }
    }

    getNewUpgrageData() {
        const {storageMeta, storagePolicyEnabled: storagePolicyEnabledOld} = this.data.get('originalData');
        const {storageSize: storgeSizeOld} = storageMeta;
        const {storagePolicyEnabled} = this.data.get('formData');
        const storageSize = this.data.get('storageMetaSource[0].diskStorage');
        let params: NormalObject = {};
        let types: UpgrageType[] = [];
        if (storageSize !== storgeSizeOld) {
            types.push(UpgrageType.EXPAND_BROKER_DISK_CAPACITY);
            params.diskSizeGB = storageSize;
        }

        if (storagePolicyEnabled !== storagePolicyEnabledOld) {
            types.push(UpgrageType.UPDATE_STORAGE_POLICY);
            params.storagePolicyEnabled = storagePolicyEnabled;
        }

        const diskThroeshodRef = (this.ref('disk-throeshod') as DiskThroeshod);
        if (storagePolicyEnabled && diskThroeshodRef.hasStoragePolicyChange()) {
            types.push(UpgrageType.UPDATE_STORAGE_POLICY);
            params.storagePolicyEnabled = storagePolicyEnabled;
            params.storagePolicy = diskThroeshodRef.getUpgradeData();
        }

        params.types = types;

        return params;
    }

    getOrderItemData() {
        const {
            storageMeta: {numberOfDisk, storageSize, storageType}
        } = this.getPriceData();
        const {storagePolicyEnabled} = this.data.get('formData');
        const totalDiskStorage = this.data.get('totalDiskStorage');
        const nodeStorageSizeText = this.data.get('isMultipleDisks')
            ? `${numberOfDisk * storageSize}${CELL.GB}（${storageSize}${CELL.GB} × ${numberOfDisk}）`
            : `${numberOfDisk * storageSize}${CELL.GB}`;
        const res =  {
            title: TextMap.title,
            type: ClusterRefType.DISK,
            list: [
                {
                    label: TextMap.storageType,
                    text: UtilHandler.storageType(storageType)
                },
                {
                    label: TextMap.nodeStorageSize,
                    text: nodeStorageSizeText
                },
                {
                    label: `${TextMap.totalStorageSize}：`,
                    text: `${totalDiskStorage}${CELL.GB}`
                },
                {
                    label: `${TextMap.storagePolicyEnabled}`,
                    text: storagePolicyEnabled ? '开启' : '关闭'
                },
            ]
        };
        if (storagePolicyEnabled) {
            const ref = this.ref('disk-throeshod');
            const storagePolicy = ref.getConfirmData();
            res.list.push({
                label: `${TextMap.storagePolicy}`,
                text: storagePolicyEnum.getTextFromValue(storagePolicy.type)
            });
        }
        return res;
    }

    // 为了变更配置使用
    async setData(obj: NormalObject) {
        this.data.set('obj', obj);
        const {storageMeta: {storageSize, numberOfDisk, storageType}, storagePolicyEnabled = true} = obj;
        const {resetFlag} = this.data.get('');
        this.data.merge('originalData', {
            ...obj,
            diskStorage: storageSize,
            storagePolicyEnabled: storagePolicyEnabled,
        });
        this.data.merge('formData', {
            storageType,
            storagePolicyEnabled,
        });
        this.data.set('storageMetaSource', [{diskStorage: storageSize, diskNum: numberOfDisk, total: storageSize * numberOfDisk}]);
        this.data.set('disabledByModified', {
            storagePolicy: false,
            diskStorage: false
        });
        this.data.set('minLimit', {
            diskStorage: storageSize
        });
        this.nextTick(() => this.data.set('resetFlag', !resetFlag));
    }
}
