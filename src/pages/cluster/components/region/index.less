/**
 * 付费及地域模块
 *
 * @file index.less
 * <AUTHOR>
 */

@klass: bms-cluster-create-region;

.@{klass} {

    &__region {

        .s-form-item-control {
            display: flex;
            align-items: center;
        }

        &_radio {
            display: inline-block;
        }
    }

    .s-radio-text {
        min-width: 57px;
    }

    .available-areas {
        padding-top: 5px;

        .s-form-item-label {
            line-height: 17px;
        }

        .s-row {
            display: flex;
            align-items: flex-start;

            .s-radio {
                margin-right: 40px;
            }

            .s-checkbox-group-wrapper {
                margin-right: 40px;
            }
        }

        &__checkbox {
            height: 18px;
        }
    }

    .sale {
        position: absolute;
        top: -17px;
        right: 0;
        .s-tag {
            margin: 0;
            background-image: linear-gradient(90deg, #F3413F 22%, #F86454 100%);
            border-radius: 1.6px;
            height: 16px;
        }
        .s-tag-content {
            padding: 0 5px;
            font-size: 12px;
            color: #FFFFFF;
            line-height: 16px;
            font-weight: 400;
            height: 16px;
        }
    }
}
