/**
 * 付费及地域模块
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Form, Radio, Loading, Switch, Select, Tag} from '@baidu/sui';
import {DOCS_LINK} from '@/common/config';
import Tip from '@/components/tip';
import {getItemInfoInArr, getPayLoop} from '@/common/util';
import {Payments, Timechoices, autoLengthOfMon, autoLengthOfYear} from '@/common/enums';
import {ClusterRefType, PaymentType} from '@/common/enums/constant';
import {renderSwitch} from '@/common/html';
import BaseCmpt from '../base-cmpt';

import {UtilHandler} from '../../util/util';

import './index.less';

const $flag = ServiceFactory.resolve('$flag');
const isXushang = $flag.KafkaXushang;

const klass = 'bms-cluster-create-region';

export enum Deployment {
    PERFROMANCE = 'performance',
    AVAILABILITY = 'availability'
};

const TextMap = {
    title: '付费及地域',
    payments: '付费方式：',
    region: '当前地域：',
    timeChoice: '购买时长：',
    autoSwitch: '自动续费：',
    autoLength: '选择续费周期：'
};

const PaymentsArr = Payments.toArray();
const timeChoices = Timechoices.toArray();

export class Region extends BaseCmpt {

    static template = html`
    <div class="${klass} bms-form-panel">
        <s-append noHighlight label="${TextMap.title}" />
        <s-form
            s-ref="form"
            rules="{{rules}}"
            data="{{formData}}">
            <s-formitem class="${klass}__region">
                <span slot="label">
                    ${TextMap.region}
                    <tip-cmpt placement="right" type="question">如需修改购买其他区域产品，请前往主导航进行切换</tip-cmpt>
                </span>
                <template s-if="{{!isDetail}}">
                    <s-radio-group
                        class="${klass}__region_radio"
                        value="{= formData.region =}"
                        datasource="{{regions}}"
                        radioType="button"
                    />
                </template>
                <template s-else>
                    <span class="upgrage-text">{{formData.region | formatRegion}}</span>
                </template>
            </s-formitem>
            <s-formitem label="${TextMap.payments}">
                <s-radio-group
                    value="{= formData.payment =}"
                    datasource="{{payments}}"
                    radioType="button"
                    on-change="onPriceConfigChange"
                    s-if="{{!isDetail}}"
                />
                <span class="upgrage-text" s-else>
                    {{formData.payment | formatPayment}}
                </span>
            </s-formitem>
            <s-formitem s-if="formData.payment === '${PaymentType.PREPAID}' && !isDetail" label="${TextMap.timeChoice}">
                <s-radio-group
                    value="{= formData.timeLength =}"
                    radioType="button"
                    on-change="onPriceConfigChange"
                >
                    <s-radio s-for="item, index in timeChoices" value="{{item.value}}">
                        <s-tag class="sale" s-if="item.value > 9 && !${isXushang}" skin="danger" enhanced>折扣</s-tag>
                        {{item.text}}
                    </s-radio>
                </s-radio-group>
            </s-formitem>
            <s-formitem
                s-if="formData.payment === '${PaymentType.PREPAID}' && !${isXushang}"
                label="${TextMap.autoSwitch}"
                >
                <template s-if="{{!isDetail}}">
                    <s-switch
                        checked="{= formData.autoRenew.renew =}"
                        on-change="onChange">
                    </s-switch>
                    <a s-if="!${isXushang}" class="inline-desc" href="${DOCS_LINK.autoRenew}" target="blank">
                        什么是自动续费
                    </a>
                </template>
                <span s-else>
                    {{formData.autoRenew.renew | renderSwitch}}
                </span>
            </s-formitem>
            <s-formitem
                s-if="formData.payment === '${PaymentType.PREPAID}' && formData.autoRenew.renew && !isDetail"
                label="${TextMap.autoLength}">
                <s-select
                    datasource="{{ autoUnits }}"
                    value="{=formData.autoRenew.renewTimeUnit=}"
                    on-change="onRenewTimeUnitChange"></s-select>
                <s-select
                    datasource="{{ formData.autoRenew.renewTimeUnit === 'month' ? autoLengthOfMon : autoLengthOfYear }}"
                    value="{=formData.autoRenew.renewTime=}"
                    class="ml16">
                </s-select>
                <span class="desc mt10 inline-desc">
                    系统将于到期7天前进行扣费，扣费时长为{{formData.autoRenew.renewTime}}{{formData.autoRenew.renewTimeUnit === 'month' ? '月' : '年'}}
                </span>
            </s-formitem>
        </s-form>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-form': Form,
        's-formitem': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        'tip-cmpt': Tip,
        's-loading': Loading,
        's-tag': Tag,
        's-switch': Switch,
        's-select': Select
    };

    initData() {
        return {
            payments: PaymentsArr,
            timeChoices: timeChoices,
            autoUnits: [{text: '按月', value: 'month'}, {text: '按年', value: 'year'}],
            autoLengthOfMon: autoLengthOfMon.toArray(),
            autoLengthOfYear: autoLengthOfYear.toArray(),
            formData: {
                payment: PaymentsArr[1].value,
                timeLength: 1,
                autoRenew: {
                    renew: false,
                    renewTimeUnit: 'month',
                    renewTime: 1
                }
            },
            isDetail: false
        };
    }

    static filters: SanFilterProps = {
        formatPayment: UtilHandler.payment,
        formatRegion: UtilHandler.region,
        renderSwitch
    };

    inited() {
        const region = this.$context.getCurrentRegion();
        this.data.set('regions', [{text: region.label, value: region.id}]);
        this.data.set('formData.region', region.id);
    }

    // 付费类型改变
    onPriceConfigChange() {
        // 由于设计值的onChange所以使用nextTick等待值更新后fire，后续可以优化
        this.nextTick(() => this.fire('price-config-change', {}));
    }

    // 校验
    verify() {
        return Promise.resolve();
    }

    onRenewTimeUnitChange() {
        this.data.set('formData.autoRenew.renewTime', 1);
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        const {payment, timeLength} = this.data.get('formData');
        const bool = timeLength <= 9;
        const time = bool ? timeLength : timeLength / 12;
        const timeUnit = bool ? 'month' : 'year';
        return {
            payment,
            ...payment === PaymentType.PREPAID
                ? {timeLength: time, timeUnit} : {}
        }
    }

    getDetailPrice() {
        return this.getPriceData();
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData(): OrderItemObj {
        const {regions, formData} = this.data.get();
        const {payment, region, timeLength, autoRenew} = formData;
        let OrderData = {
            title: TextMap.title,
            type: ClusterRefType.REGION,
            list: [
                {
                    label: TextMap.region,
                    text: getItemInfoInArr(regions, region)
                },
                {
                    label: TextMap.payments,
                    text: getItemInfoInArr(PaymentsArr, payment)
                }
            ]
        };
        if (payment === PaymentType.PREPAID) {
            OrderData.list = OrderData.list.concat([
                {
                    label: TextMap.timeChoice,
                    text: timeLength <= 9 ? timeLength + '个月' : timeLength/12 + '年'
                },
                {
                    label: TextMap.autoSwitch,
                    text: autoRenew.renew ? '开' : '关'
                }
            ]);

            autoRenew.renew ? OrderData.list.push({
                label: '自动续费周期：',
                text: getPayLoop(autoRenew.renewTime, autoRenew.renewTimeUnit)
            }) : '';
        }

        return OrderData;
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const {payment, timeLength, autoRenew} = this.data.get('formData');
        return payment === PaymentType.PREPAID ? {
            payment,
            timeLength: timeLength <= 9 ? timeLength : (timeLength / 12),
            timeUnit: timeLength <= 9 ? 'month' : 'year',
            autoRenew: autoRenew.renew ? autoRenew : {}
        } : {
            payment
        };
    }

    setData(obj: NormalObject) {
        const {region, payment, autoRenew} = obj;
        this.data.merge('formData', {region, payment, autoRenew});
    }
}
