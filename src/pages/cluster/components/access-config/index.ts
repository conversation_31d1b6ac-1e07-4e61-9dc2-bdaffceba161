import { UpgrageType } from './../../util/conf';
/**
 * 访问配置模块
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Switch, Form, Checkbox, Table} from '@baidu/sui';
import {MultiToneSuccess, MultiToneError} from '@baidu/sui-icon';
import {AppLegend} from '@baidu/sui-biz';
import Tip from '@/components/tip';
import {VAILDITE_ITEMS} from '@/common/rules';
import {ClusterRefType, authenticationModesEnum} from '@/common/enums/constant';
// eslint-disable-next-line max-len
import {sasl, none, nonevpc, saslnetwork, ssl, sslnetwork, sasl_plain, sasl_plain_network, ssl_private} from '@/common/config';
import TABLE_COLUMNS from '@/common/config/columns';
import {getModes} from '@/common/util';

import BaseCmpt from '../base-cmpt';
import {UtilHandler} from '../../util/util';

import './index.less';

const klass = 'bms-cluster-create-access';

const TextMap = {
    title: '访问配置',
    access: '权限控制：',
    mode: '认证方式：',
    protocol: '访问协议：'
};

const NoneText = {
    textAble: '客户端无需身份认证，并且允许所有操作',
    textDisabled: '暂不支持开启权限管理功能下使用此认证方式',
};

const protocolColumns = _.cloneDeep(TABLE_COLUMNS.protocolColumns);

export default class AccessType extends BaseCmpt {

    static template = html`
    <div class="${klass} bms-form-panel">
        <s-append noHighlight label="${TextMap.title}">
            <div slot="extra">
                <span s-if="{{isDetail}}" class="access-tip">谨慎修改认证方式，可能导致已有访问失效。</span>
            </div>
        </s-append>
        <s-form
            s-ref="form"
            rules="{{rules}}"
            data="{{formData}}"
        >
            <s-formitem class="form-item-check-group" label="${TextMap.mode}" prop="authenticationModes">
                <template>
                    <s-checkbox-group
                        class="access-config_check-group"
                        value="{= formData.authenticationModes =}"
                    >
                        <p class="check-item">
                            <s-checkbox
                                class="s-checkbox"
                                disabled="{{None.disabled || !isUpgrading}}"
                                label="{{None.label}}"
                                value="{{None.value}}"
                                on-change="onNoneChange"
                                checked="{{None.checked}}"
                            />
                            <p class="desc mt4 mb24">{{None.text}}</p>
                        </p>
                        <p class="check-item">
                            <s-checkbox
                                class="s-checkbox"
                                disabled="{{Ssl.disabled || !isUpgrading}}"
                                label="{{Ssl.label}}"
                                value="{{Ssl.value}}"
                                on-change="onSslChange"
                                checked="{{Ssl.checked}}"
                            />
                            <p class="desc mt4 mb24">{{Ssl.text}}</p>
                        </p>
                        <p class="check-item">
                            <s-checkbox
                                class="s-checkbox"
                                disabled="{{Sasl.disabled || !isUpgrading}}"
                                label="{{Sasl.label}}"
                                value="{{ Sasl.value }}"
                                on-change="onSaslChange"
                                checked="{{Sasl.checked}}"
                            />
                            <p class="desc mt4 mb24">{{Sasl.text}}</p>
                        </p>
                        <p class="check-item">
                            <s-checkbox
                                class="s-checkbox"
                                disabled="{{SaslPlain.disabled || !isUpgrading}}"
                                label="{{SaslPlain.label}}"
                                value="{{ SaslPlain.value }}"
                                on-change="onSaslPlainChange"
                                checked="{{SaslPlain.checked}}"
                            />
                            <p class="desc mt4">{{SaslPlain.text}}</p>
                        </p>
                    </s-checkbox-group>
                </template>
            </s-formitem>
            <s-formitem class="form-item-center">
                <span slot="label">
                    ${TextMap.access}
                    <tip-cmpt placement="right" type="question">ACL访问权限功能开关</tip-cmpt>
                </span>
                <template>
                    <s-switch
                        checked="{= formData.aclEnabled =}"
                        disabled="{{aclEnabledDisable}}"
                        on-change="handleSwitchAccess"
                    />
                </template>
            </s-formitem>
            <s-formitem label="${TextMap.protocol}" s-if="datasource.length > 0">
                <s-table
                    class="protocol-table"
                    columns="{{protocolColumns}}"
                    datasource="{{datasource}}"
                >
                    <div slot="c-aclEnable">
                        <s-icon-succ s-if="row.aclEnable"></s-icon-succ>
                        <s-icon-err s-else></s-icon-err>
                    </div>
                    <div slot="c-secret">
                        <s-icon-succ s-if="row.secret"></s-icon-succ>
                        <s-icon-err s-else></s-icon-err>
                    </div>
                    <div slot="c-userVision">
                        <s-icon-succ s-if="row.userVision"></s-icon-succ>
                        <s-icon-err s-else></s-icon-err>
                    </div>
                </s-table>
            </s-formitem>
        </s-form>
    </div>`;

    static components = {
        's-form': Form,
        's-formitem': Form.Item,
        's-append': AppLegend,
        's-switch': Switch,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-checkbox': Checkbox,
        'tip-cmpt': Tip,
        's-table': Table,
        's-icon-succ': MultiToneSuccess,
        's-icon-err': MultiToneError
    };

    initData() {
        return {
            rules: {
                authenticationModes: [
                    VAILDITE_ITEMS.requiredCheckGroup,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            const {publicNetworkStatus, privateNetworkStatus} = this.data.get('');
                            // eslint-disable-next-line max-len
                            const isNoneOnly = value.length === 1 && _.includes(value, 'NONE');
                            if (publicNetworkStatus && isNoneOnly) {
                                return callback('开启公网时，SSL，SASL/SCRAM，SASL/PLAIN 至少选择一项');
                            }
                            // if (privateNetworkStatus && !_.includes(value, 'NONE') && !_.includes(value, 'SSL')) {
                            //     return callback('开启产品转储时，NONE，SSL 至少选择一项');
                            // }
                            callback();
                        }
                    }
                ],
            },
            formData: {
                aclEnabled: false,
                authenticationModes: []
            },
            None: {
                value: 'NONE',
                label: authenticationModesEnum.NONE,
                text: NoneText.textAble,
                disabled: false,
                checked: false
            },
            Sasl: {
                value: 'SASL_SCRAM',
                label: authenticationModesEnum.SASL_SCRAM,
                text: 'SCRAM使用SASL框架提供用户名和密码验证方法',
                disabled: false,
                checked: false
            },
            Ssl: {
                value: 'SSL',
                label: authenticationModesEnum.SSL,
                text: '使用SSL证书验证连接到集群的客户端的身份',
                disabled: false,
                checked: false
            },
            SaslPlain: {
                value: 'SASL_PLAIN',
                label: authenticationModesEnum.SASL_PLAIN,
                text: 'PLAIN使用SASL框架提供用户名和密码验证方法',
                disabled: false,
                checked: false
            },
            publicNetworkStatus: false,
            privateNetworkStatus: false,
            tempModes: {
                None: false,
                Sasl: false,
                Ssl: false,
                SaslPlain: false
            },
            protocolColumns: protocolColumns
        };
    }

    static filters = {
        formatAclEnabled: UtilHandler.aclEnabled,
        formatAuthenticationModes: UtilHandler.authenticationModes
    };

    static computed = {
        datasource() {
            const publicNetworkStatus = this.data.get('publicNetworkStatus');
            const None = this.data.get('None.checked');
            const Ssl = this.data.get('Ssl.checked');
            const Sasl = this.data.get('Sasl.checked');
            const SaslPlain = this.data.get('SaslPlain.checked');
            const aclEnabled = this.data.get('formData.aclEnabled');
            let datasource = [];
            if (None) {
                datasource = datasource.concat([none]);
            }
            if (Ssl) {
                datasource = datasource.concat(publicNetworkStatus ? [ssl, sslnetwork] : [ssl]);
            }
            if (Sasl) {
                datasource = datasource.concat(publicNetworkStatus
                    ? [{...sasl, aclEnable: aclEnabled}, {...saslnetwork, aclEnable: aclEnabled}]
                    : [{...sasl, aclEnable: aclEnabled}]);
            }
            if (SaslPlain) {
                datasource = datasource.concat(publicNetworkStatus
                    ? [{...sasl_plain, aclEnable: aclEnabled}, {...sasl_plain_network, aclEnable: aclEnabled}]
                    : [{...sasl_plain, aclEnable: aclEnabled}]);
            }
            return datasource;
        },
        aclEnabledDisable(): boolean {
            const Sasl = this.data.get('Sasl.checked');
            const SaslPlain = this.data.get('SaslPlain.checked');
            const isUpgrading = this.data.get('isUpgrading');
            return !isUpgrading || (!Sasl && !SaslPlain);
        }
    };

    onSaslChange(target: {value: boolean}) {
        this.data.set('Sasl.checked', target.value);
        const SaslPlain = this.data.get('SaslPlain.checked');
        this.handleSwitchAccess({value: SaslPlain || target.value});
        this.fire('change-upgrade-status', {});
    }

    onSaslPlainChange(target: {value: boolean}) {
        this.data.set('SaslPlain.checked', target.value);
        const Sasl = this.data.get('Sasl.checked');
        this.handleSwitchAccess({value: Sasl || target.value});
        this.fire('change-upgrade-status', {});
    }

    onSslChange(target: {value: boolean}) {
        this.data.set('Ssl.checked', target.value);
        this.fire('change-upgrade-status', {});
    }

    onNoneChange(target: {value: boolean}) {
        this.data.set('None.checked', target.value);
        this.fire('change-upgrade-status', {});
    }

    handleChange() {
        let modes = [];
        if (this.data.get('None.checked')) {
            modes.push('NONE');
        }
        if (this.data.get('Ssl.checked')) {
            modes.push('SSL');
        }
        if (this.data.get('Sasl.checked')) {
            modes.push('SASL_SCRAM');
        }
        if (this.data.get('SaslPlain.checked')) {
            modes.push('SASL_PLAIN');
        }
        this.data.set('formData.authenticationModes', modes);
    }

    cacheUserChoiceOfNone() {
        const authenticationModes = this.data.get('formData.authenticationModes');
        this.data.set('tempModes.None', authenticationModes.indexOf('NONE') > -1);
    }

    cacheUserChoiceOfSasl() {
        const {publicNetworkStatus, privateNetworkStatus} = this.data.get('');
        if (!publicNetworkStatus && !privateNetworkStatus) {
            const authenticationModes = this.data.get('formData.authenticationModes');
            this.data.set('tempModes.Sasl', authenticationModes.indexOf('SASL_SCRAM') > -1);
        }
    }

    cacheUserChoiceOfSwitch() {
        const {aclEnabled} = this.data.get('formData');
        this.data.set('tempModes.aclEnabled', aclEnabled);
    }

    // cacheUserChoiceOfSaslPlain() {
    //     const {publicNetworkStatus, privateNetworkStatus} = this.data.get('');
    //     if (!publicNetworkStatus && !privateNetworkStatus) {
    //         const authenticationModes = this.data.get('formData.authenticationModes');
    //         this.data.set('tempModes.SaslPlain', authenticationModes.indexOf('SASL_PLAIN') > -1);
    //     }
    // }

    handleSwitchAccess(target: {value: boolean}) {
        // 保存初始态
        this.data.set('formData.aclEnabled', target.value);
        this.handleChange();
        this.fire('change-upgrade-status', {});
    }

    // 修改公/内网时，如果有一个公/内开关任意有一个选中，sasl置灰选中，否则sasl可选且恢复用户选择态tempFormData
    handleIpChange(changeType: string) {
        console.log(changeType);
        const {publicNetworkStatus, privateNetworkStatus} = this.data.get('');
        if (!publicNetworkStatus && privateNetworkStatus) {
            this.handleChange();
        }
        else if (publicNetworkStatus && privateNetworkStatus) {
            if (changeType === 'public') {
                this.data.set('Sasl.checked', true);
                this.data.set('formData.aclEnabled', true);
            }
            this.handleChange();
        }
        else if (publicNetworkStatus && !privateNetworkStatus) {
            if (changeType === 'public') {
                this.data.set('Sasl.checked', true);
                this.data.set('formData.aclEnabled', true);
            }
            this.handleChange();
        }
        else {
            this.handleChange();
        }
    }

    handlePublicChange(bool: boolean) {
        // 保存初始态
        this.cacheUserChoiceOfSasl();
        this.cacheUserChoiceOfSwitch();
        // this.cacheUserChoiceOfSaslPlain();
        this.data.set('publicNetworkStatus', bool);
        this.handleIpChange('public');
    }

    handlePrivateChange(bool: Boolean) {
        // 保存初始态
        this.cacheUserChoiceOfSasl();
        // this.cacheUserChoiceOfSaslPlain();
        bool ? this.cacheUserChoiceOfNone() : '';
        this.data.set('privateNetworkStatus', bool);
        this.handleIpChange('private');
    }

    compare(originalData, formData) {
        const len1 = originalData.authenticationModes.length;
        const len2 = formData.authenticationModes.length;
        if (originalData.aclEnabled !== formData.aclEnabled) {
            return true;
        }
        else if (len1 !== len2) {
            return true;
        }
        else if (len1 ===  len2 && len1 > 0) {
            const data1 = originalData.authenticationModes;
            const data2 = formData.authenticationModes;
            return _.difference(data1, data2).length !== 0;
        }
        else {
            return false;
        }
    }

    checkISChange() {
        const {originalData, formData, isDetail} = this.data.get('');
        if (!isDetail) {
            return true;
        }

        const change = this.compare(originalData, formData);
        return change;
    }

    // 校验
    verify() {
        const ref = (this.ref('form') as unknown as Form);
        return Promise.all([
            ref.validateFields()
        ]).catch((err: Error) => {
            throw (ClusterRefType.ACCESS);
        });
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        return {};
    }

    getDetailPrice() {
        return {};
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData(): OrderItemObj {
        const {formData} = this.data.get('');
        const {aclEnabled, authenticationModes} = formData;
        return {
            title: TextMap.title,
            type: ClusterRefType.ACCESS,
            list: [
                {
                    label: TextMap.access,
                    text: aclEnabled ? '开' : '关'
                },
                {
                    label: TextMap.mode,
                    text: getModes(authenticationModes)
                }
            ]
        };
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const {formData} = this.data.get('');
        const {aclEnabled, authenticationModes} = formData;
        return {
            aclEnabled,
            authenticationModes
        };
    }

    // 获取变更参数——》即使未变更也返回，主要为了在公网开启/关闭、产品间转储开启/关闭时传参
    getUpgrageData() {
        const {aclEnabled, authenticationModes} = this.data.get('formData');
        return {
            aclEnabled,
            authenticationMode: authenticationModes
        };
    }

    // 获取变更参数——》仅变更了的参数
    getNewUpgrageData() {
        const {aclEnabled, authenticationModes} = this.data.get('formData');
        const {aclEnabled: aclEnabledOld, authenticationModes: authenticationModesOld} = this.data.get('originalData');
        let params: NormalObject = {};
        let types: UpgrageType[] = [];
        if (aclEnabled !== aclEnabledOld) {
            params.aclEnabled = aclEnabled;
            types.push(UpgrageType.UPDATE_ACCESS_CONFIG);
        }

        if (!_.isEqual(_.sortBy(authenticationModes), _.sortBy(authenticationModesOld))) {
            params.authenticationMode = authenticationModes;
            !types.includes(UpgrageType.UPDATE_ACCESS_CONFIG) && types.push(UpgrageType.UPDATE_ACCESS_CONFIG);
        }
        params.types = types;
        return params;
    }

    setData(obj: NormalObject) {
        const {aclEnabled, authenticationModes, publicIpEnabled, intranetIpEnabled} = obj;
        this.data.set('originalData', {aclEnabled, authenticationModes, publicip: obj.publicIpEnabled});
        this.data.merge('formData', {aclEnabled, authenticationModes});
        const {isDetail} = this.data.get('');
        if (isDetail) {
            this.data.set('tempModes', {
                None: _.includes(authenticationModes, 'NONE'),
                Sasl: _.includes(authenticationModes, 'SASL_SCRAM'),
                Ssl: _.includes(authenticationModes, 'SSL'),
                SaslPlain: _.includes(authenticationModes, 'SASL_PLAIN'),
            });
            this.data.set('publicNetworkStatus', publicIpEnabled);
            this.data.set('privateNetworkStatus', intranetIpEnabled);
            this.data.set('Ssl.checked', _.includes(authenticationModes, 'SSL'));
            this.data.set('SaslPlain.checked', _.includes(authenticationModes, 'SASL_PLAIN'));
            this.data.set('None.checked', _.includes(authenticationModes, 'NONE'));
            this.data.set('Sasl.checked', _.includes(authenticationModes, 'SASL_SCRAM'));
        }
    }
}

