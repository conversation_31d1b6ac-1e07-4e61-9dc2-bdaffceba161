/* eslint-disable max-len */
/**
 * 节点缩容解绑EIP提示弹窗
 *
 * @file decrease_node_tip.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Table, Button} from '@baidu/sui';
import {FilledInfo} from '@baidu/sui-icon';
import api from '@/common/client';
import _ from 'lodash';

const klass = 'decrease-node-tip';
class DecreaseNode extends Component {
    static template = html`
    <template>
        <s-dialog
            open="{{open}}"
            class="${klass}"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}"
            on-confirm="onConfirm"
            width="1000"
        >
            <div slot="title">
                <s-outlined-exclamation-circle width="20" color="#2468f2" class="mr16"/>变更提示
            </div>
            <p>本次变更中以下EIP实例与BCC实例解绑，解绑后的EIP实例不会被释放，将持续产生费用，如需释放，请
            <a href="/eip/#/eip/instance/list" target="_blank" class="inline-desc">前往EIP实例列表</a>
            </p>
            <s-table
                columns="{{columns}}"
                datasource="{{datasource}}"
                loading="{{loading}}"
                class="mt16"
                width="700"
            >
                <div slot="c-name">
                    {{row.name}}
                    <span class="release-tag ml16">即将释放</span>
                </div>
            </s-table>
            <div slot="footer">
                <s-button skin="primary" on-click="onConfirm">确定解绑</s-button>
            </div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-table': Table,
        's-button': Button,
        's-outlined-exclamation-circle': FilledInfo
    };

    initData() {
        return {
            open: true,
            confirming: false,
            loading: true,
            datasource: [],
            columns: [
                {name: 'name', label: '实例名称', width: 300},
                {name: 'eip', label: 'IP地址'},
                {name: 'bandwidthInMbps', label: '带宽（Mbps）'},
                {
                    name: 'routeType',
                    label: '线路类型',
                },
            ],
        };
    }

    attached() {
        this.getTableList();
    }

    async getTableList() {
        const {clusterId, numberOfBrokerNodesPerZone = 0} = this.data.get('');
        const {eips: eipsource} = await api.getEips({clusterId});
        const {decreaseNodeIds}  = await api.getDecreaseBrokerCountPlan(this.data.get('clusterId'), {numberOfBrokerNodesPerZone});
        let res = []; // 解绑实例index数组
        eipsource.forEach((item, index) => {
            if (_.includes(decreaseNodeIds, item.instanceId) && item.status === 'binded' && item.instanceType === 'BCC') {
                res.push(item);
                this.data.set(`eipsource[${index}].toRelease`, true);
            }
            else {
                this.data.set(`eipsource[${index}].toRelease`, false);
            }
        });
        this.data.set('datasource', res);
        this.data.set('loading', false);
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    onConfirm() {
        this.onClose();
    }
}

export default DecreaseNode;
