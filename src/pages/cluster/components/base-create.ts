/**
 * 基础创建相关的公共类（将集群创建抽离出来）
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {redirect, ServiceFactory} from '@baiducloud/runtime';
import {Notification, toast, Loading} from '@baidu/sui';
import {AppCreatePage} from '@baidu/sui-biz';
import api from '@/common/client';
import {PaymentType, ClusterRefType, Deployment} from '@/common/enums/constant';

import {ROUTE_PATH} from '@/common/config';
import {Region} from '../components/region';
import {ClusterConfig} from '../components/cluster-config';
import NetWork from '../components/network-config';
import Access from '../components/access-config';
import NodeConfig from '../components/node-config';
import {ShopcartClickType} from './shop-cart/base';
import {SERVICE_TYPE} from '@/common/config/index';
import {XuShangPostPayMessage, isOneCloudId} from '@/common/util';
import DecreaseNodeDialog from './decrease_node_tip';
import {UpgrageType} from './../util/conf';

const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
const LoadingDialog = Loading.LoadingDialog;

export const Refs = Object.values(ClusterRefType);

export type NodeClass = Region | ClusterConfig | NetWork | NodeConfig;

export interface ClusterObj {[x: string]: number | string | boolean | object}

export const noCoupon = {text: '不选择代金券', value: '', title: '不选择代金券'};

export const ClusterCreateSuccUrl = {
    'Prepaid': '/finance/#/finance/pay~fromService=',
    'PostPaid': '/billing/#/order/success~fromService='
};

export default abstract class BaseCreate extends AppCreatePage {

    initData() {
        return {
            current: 1,
            clusterPrice: 0,
            publicIpPrice: 0,
            couponValue: 0,
            selectZones: [],
            isSubmiting: false,
            couponList: [],
            couponIds: [],
            showCoupon: false,
            errMsg: '',
            payment: PaymentType.POSTPAID,
            timeLength: 1,
            timeUnit: 'month',
            flavorloading: true,
            singleNumberTextNumber: 3,
            isOneCloud: isOneCloudId(),
            checkResourceLoading: false
        };
    }

    static filters = {
        isShow(current: number, index: number) {
            return current === index ? '' : 'display:none';
        }
    };

    abstract attached(): void;

    abstract onConfirm(): void;

    abstract onQueryPrice(): void;

    abstract getCouponList(): void;

    onShopCartClick(type: ShopcartClickType) {
        switch (type) {
            case ShopcartClickType.PREV:
                this.onPrev();
                break;
            case ShopcartClickType.NEXT:
                this.onNext();
                break;
            case ShopcartClickType.CONFIRM:
                this.onConfirm();
                break;
            case ShopcartClickType.CANCEL:
                redirect(`#${ROUTE_PATH.clusterList}`);
                break;
        }
    }

    // 公网带宽的变化
    onPublicIpChange(bool: boolean) {
        this.data.set('publicIp', bool);
        const node = (this.ref(ClusterRefType.ACCESS) as Access);
        node?.handlePublicChange(bool);
    }

    // 内网权限变化
    onPrivateIpChange(bool: boolean) {
        const node = (this.ref(ClusterRefType.ACCESS) as Access);
        node?.handlePrivateChange(bool);
    }

    // 部署方式改变
    onDeployChange(deployType: Deployment) {
        const node = (this.ref(ClusterRefType.NETWORK) as NetWork);
        node && node.handleDeployType(deployType);
        // 由于部署方式的改变，会导致子网、磁盘类型置空，所以置空价格
        this.data.set('clusterPrice', '');
        this.data.set('publicIpPrice', '');
    }

    // 获取用户核数上限
    getLimitQuotas() {
        return api.getLimitQuotas({})
            .then((target: {rest: number, sum: number, type: string}) => {
                this.data.set('cpuSum', target.sum);
                this.data.set('cpuRest', target.rest);
            });
    }

    getContent(coupon: {balance: string, productRuleDescription: string, conditionEffectDescription: string}) {
        let content
            = `￥${coupon.balance}` + ' '
            + coupon.productRuleDescription
            + (coupon.conditionEffectDescription
                ? '（' + coupon.conditionEffectDescription + '）'
                : '');
        return content;
    }

    async onCouponChange(target: {couponId: string}) {
        if (target.couponId === '') {
            this.data.set('couponIds', []);
            this.data.set('couponValue', 0);
        }
        else {
            let couponIds = [];

            couponIds.push(target.couponId.toString());
            this.data.set('couponIds', couponIds);

            const couponList = this.data.get('couponList');

            let coupon = _.find(couponList, item => {
                return item.value === target.couponId;
            });

            this.data.set('couponValue', coupon.title);
        }
    }

    onClearCoupon() {
        this.data.set('errMsg', '');
    }

    activeCoupon(target: {couponName: string}) {
        let param = {
            couponName: target.couponName
        };
        this.data.set('errMsg', '');
        api.activeCoupon(param).then((result: NormalObject) => {
            let list = result.result;
            let couponList = [noCoupon];
            _.each(list, coupon => {
                couponList.push({
                    text: '￥' + coupon.balance,
                    value: coupon.id,
                    title: coupon.balance
                });
            });
            this.data.set('couponList', result.result);
            Notification.success('代金券激活成功');
        }).catch((e: Error) => {
            this.data.set('errMsg', '代金券无效');
        });
    }

    // 选中区域
    onSeletZones(zones: string[]) {
        const node = (this.ref(ClusterRefType.NETWORK) as NetWork);
        node && node.handleZones(zones);
    }

    // 校验
    verifyForms() {
        const upgradeNetwork = this.data.get('upgradeNetwork');
        const ACCESS = this.ref('access-config') as NodeClass;
        const NODE = this.ref('node-config') as NodeClass;
        const NETWORK = this.ref('network-config') as NodeClass;
        const Disk = this.ref('disk-config') as NodeClass;
        const Clutser = this.ref('cluster-config') as NodeClass;
        const change1 = NODE.checkISChange();
        const change2 = NETWORK.checkISChange();
        const change3 = ACCESS.checkISChange() || (change2 && upgradeNetwork);
        const change4 = Disk.checkISChange();
        const change5 = Clutser.checkISChange();
        console.log(change1, change2, change3, change4, change5);
        // 以下情况则阻止提交，并提示。
        // 1.所有配置项均未变更
        // 2.flavor未加载完成
        if ((!change1 && !change2 && !change3 && !change4 && !change5)
            || this.data.get('flavorloading')
        ) {
            Notification.error('请您选中一个配置项进行变配', {width: 300, duration: -1});
            return Promise.reject();
        }
        const arr: Array<PromiseLike<void>> = [];
        _.each(Refs, ref => {
            const node: NodeClass = this.ref(ref);
            // @ts-ignore
            node && arr.push(node.verify());
        });
        return Promise.all(arr);
    }


    showLoading() {
        LoadingDialog.show({
            size: 'large',
            tip: '正在校验资源...'
        });
    }

    hideLoading() {
        LoadingDialog.hide();
    }

    // 下一步
    // 预付费降配模式下，应付金额为负数，代金券接口报错，需隐藏代金券模块
    onNext() {
        if (this.data.get('priceLoading')) {
            this.data.set('clickNext', true);
            return;
        }
        this.data.set('clickNext', false);
        // 校验
        this.verifyForms()
            .then(async () => {
                const canGoNext = await this.setData();
                if (!canGoNext) {
                    return;
                }
                this.data.set('current', 2);
                // 第二步账单数组内容
                const {margin = 0} = this.data.get('');
                const needHideCoupon = margin < 0;
                if (this.data.get('payment') === PaymentType.PREPAID && !needHideCoupon) {
                    this.data.set('showCoupon', true);
                    this.getCouponList();
                }
                else {
                    this.data.set('showCoupon', false);
                    this.data.set('couponIds', []);
                    this.data.set('couponValue', 0);
                }
            }).catch((err: ClusterRefType) => {
                const node = this.ref(err) as NodeClass;
                node && node.el && node.el.scrollIntoView();
            });
    }

    // 上一步
    onPrev() {
        this.data.set('current', 1);
    }

    // 设置账单提交参数
    async setData() {
        let confirmData: ClusterObj = {};
        const arr: OrderItemObj[] = [];
        const clusterTag = await this.ref('tag-config')?.getTagPanel().getTags();
        _.each(Refs, ref => {
            const node: NodeClass = this.ref(ref);
            if (node && node !== this.ref('tag-config')) {
                arr.push(node.getOrderItemData());
                confirmData = {
                    ...node.getConfirmData(),
                    ...confirmData
                };
            }
            else if (node === this.ref('tag-config')) {
                confirmData = {
                    tags: clusterTag,
                    ...confirmData
                };
            }
        });
        this.data.set('orderItems', arr);
        // 提交订单参数
        this.data.set('confirmData', confirmData);
        if (!this.data.get('isDetail')) {
            this.showLoading();
            const {
                subnets,
                storageMeta,
                deployType,
                logicalZones,
                numberOfBrokerNodes,
                numberOfBrokerNodesPerZone,
                nodeType,
                configMeta,
                payment,
                timeLength,
                timeUnit,
                authenticationModes,
                intranetIpEnabled,
                publicIpEnabled,
                securityGroups,
                vpc
            } = this.data.get('confirmData');
            const params = {
                subnets,
                storageMeta,
                deployType,
                logicalZones,
                numberOfBrokerNodes,
                numberOfBrokerNodesPerZone,
                nodeType,
                configMeta,
                payment,
                authenticationModes,
                intranetIpEnabled,
                publicIpEnabled,
                securityGroups,
                vpc
            };
            try {
                const success = await api.checkResource(
                    payment === 'Prepaid' ? {...params, timeLength, timeUnit} : params);
                if (!success) {
                    toast.error({
                        mesaage: '资源不足，请选择其他配置',
                        duration: 10
                    });
                    this.hideLoading();
                    return false;
                }
                else {
                    this.hideLoading();
                    return true;
                }
            }
            catch (e) {
                this.hideLoading();
                return false;
            }
        }
        else if (confirmData.publicIpEnabled && confirmData.publicIpMode === 'MANUAL_SELECT') {
            this.decreaseTip();
            return true;
        }
        else {
            return true;
        }
    }

    decreaseTip() {
        const upgradeNode = this.data.get('upgradeNode');
        const node = (this.ref(ClusterRefType.NODE) as NodeConfig);
        const nodeUpgradeData = {
            ...(node?.getUpgrageData() ?? {}),
        };
        if (upgradeNode && UpgrageType.DECREASE_BROKER_COUNT === nodeUpgradeData.type) {
            const dialog = new DecreaseNodeDialog({
                data: {
                    clusterId: this.data.get('clusterId'),
                    upgrageType: UpgrageType.DECREASE_BROKER_COUNT,
                    numberOfBrokerNodesPerZone: nodeUpgradeData.numberOfBrokerNodesPerZone,
                }
            });
            dialog.attach(document.body);
            dialog.on('success', () => {
                return true;
            });
        }
    }

    // 确认订单
    renderOrder(payment: PaymentType, orderId: string, orderType: string) {
        if (isXushang) {
            XuShangPostPayMessage({
                orderId,
                orderType
            });
            return;
        }
        redirect(`${ClusterCreateSuccUrl.Prepaid}${SERVICE_TYPE}&orderType=NEW&orderId=${orderId}`);
    }
}
