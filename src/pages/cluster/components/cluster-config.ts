/* eslint-disable max-len */
/**
 * 集群配置模块
 *
 * @file cluster-config.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Select, Input, Form, Radio, Loading} from '@baidu/sui';
import {INPUT_WIDTH, SELECT_HEIGHT} from '@/common/config';
import {VAILDITE_ITEMS} from '@/common/rules';
import {getItemInfoInArr} from '@/common/util';
import {ClusterDefaultConf} from '@/common/enums';
import {ClusterRefType, VersionTypeEnum} from '@/common/enums/constant';
import {formatTime} from '@/common/util';
import BaseCmpt from './base-cmpt';
import {UtilHandler} from '../util/util';
import {ConfigItem} from '@/pages/cluster-config/list';
import {RevisionItem} from '@/pages/cluster-config/detail';
import api from '@/common/client';
import {UpgrageType} from '../util/conf';
import {throttle} from '@/common/decorator';
import {versionType} from '@/pages/cluster/list/index';
const klass = 'bms-cluster-create-cluster';

const Confs = ClusterDefaultConf.toArray();

const TextMap = {
    title: '集群配置',
    name: '集群名称：',
    version: '集群版本：',
    config: '集群配置：',
    configId: '集群配置：',
    revisionId: '配置版本：',
};

export class ClusterConfig extends BaseCmpt {
    static template = html`
    <div class="${klass} bms-form-panel">
        <s-append noHighlight label="${TextMap.title}" />
        <s-form
            s-ref="form"
            rules="{{rules}}"
            data="{= formData =}">
            <s-formitem label="${TextMap.name}" prop="name" help="不超过65个字符，仅支持数字/字母/特殊符号(_/-.)">
                <template s-if="{{!isDetail}}">
                    <s-input
                        value="{= formData.name =}"
                        width="248"
                        placeholder="请输入集群名称"
                        on-input="onNameInputChange"
                    />
                    <span slot="error" class="error" s-if="{{nameErr}}">{{nameErr}}</span>
                </template>
                <span class="upgrage-text" s-else>
                    {{formData.name | formatName}}
                </span>
            </s-formitem>
            <s-formitem label="${TextMap.version}" prop="version">
                <template s-if="{{!isDetail}}">
                    <s-select
                        s-if="{{!versionLoading}}"
                        datasource="{{versions}}"
                        value="{= formData.version =}"
                        width="${INPUT_WIDTH}"
                        height="${SELECT_HEIGHT}"
                        on-change="onVersionChange"
                    />
                    <s-loading s-else loading />
                </template>
                <span class="upgrage-text" s-else>
                    {{formData.version | formatVersion}}
                </span>
            </s-formitem>
            <s-formitem label="${TextMap.config}">
                <s-radio-group
                    radioType="button"
                    datasource="{{configs}}"
                    on-change="onConfigChange"
                    disabled="{{!isUpgrading}}"
                    value="{{formData.config}}"
                />
            </s-formitem>
            <s-formitem
                style="margin-left: 106px"
                prop="configId"
                label="${TextMap.configId}"
                s-if="{{formData.config === 'selfDefine'}}"
            >
                <s-select
                    value="{{formData.configId}}"
                    on-change="onConfigIdChange"
                    filterable
                    loading="{{configLoading}}"
                    disabled="{{!isUpgrading}}"
                    width="${INPUT_WIDTH}"
                    height="${SELECT_HEIGHT}"
                >
                    <s-option
                        class="config-option"
                        s-for="item in configSource"
                        value="{{item.value}}"
                        label="{{item.text}}"
                    >
                        <div class="label">{{item.text}}</div>
                        <div class="desc">描述：{{item.alias || '-'}}</div>
                        <div class="desc">创建日期：{{item.createTime || '-'}}</div>
                    </s-option>
                </s-select>
                <a href="#/kafka/config/create" target="blank" class="inline-desc">新增</a>
            </s-formitem>
            <s-formitem
                prop="revisionId"
                style="margin-left: 106px"
                label="${TextMap.revisionId}"
                s-if="{{formData.config === 'selfDefine'}}"
            >
                <s-select
                    value="{{formData.revisionId}}"
                    width="${INPUT_WIDTH}"
                    loading="{{configVersionLoading}}"
                    on-change="onRevisionIdChange"
                    disabled="{{!isUpgrading}}"
                    height="${SELECT_HEIGHT}"
                >
                    <s-option
                        class="config-option"
                        s-for="item in revisionSource"
                        value="{{item.value}}"
                        label="{{item.text}}"
                    >
                        <p class="label">{{item.text}}</p>
                        <p class="desc">描述：{{item.alias || '-'}}</p>
                        <p class="desc">创建日期：{{item.createTime || '-'}}</p>
                    </s-option>
                </s-select>
            </s-formitem>
        </s-form>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-formitem': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-loading': Loading,
        's-option': Select.Option
    };

    initData() {
        return {
            versions: [],
            configs: Confs,
            formData: {
                version: '',
                name: '',
                configId: '',
                revisionId: '',
                config: Confs[0].value
            },
            rules: {
                name: [
                    VAILDITE_ITEMS.requiredInput,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (value.length > 65) {
                                return callback('不能超过65个字符');
                            }
                            if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(value)) {
                                return callback('输入字符格式有误');
                            }
                            callback();
                        }
                    }
                ],
                version: [VAILDITE_ITEMS.requiredSelect],
                configId: [VAILDITE_ITEMS.requiredSelect],
                revisionId: [VAILDITE_ITEMS.requiredSelect]
            },
            versionLoading: true,
            configVersionLoading: true,
            configLoading: true
        };
    }

    static filters: SanFilterProps = {
        formatName: UtilHandler.name,
        formatVersion: UtilHandler.version,
        formatConfig: UtilHandler.configId
    };

    inited() {
        if (this.data.get('isDetail')) {
            this.data.set('formData.name', '');
        }
        // if (isOneCloudId()) {
        //     this.data.set('configs', Confs.filter(i => i.value !== 'selfDefine'));
        // }
    }

    attached() {
        this.getVersion();
        const isDetail = this.data.get('isDetail');
        if (!isDetail) {
            this.nextTick(() => {
                this.fire('config-change', {value: Confs[0].value});
            });
        }
    }

    onConfigChange(target: {value: string}) {
        this.data.set('formData.config', target.value);
        if (target.value === 'selfDefine') {
            this.listConfigs();
        }
        else {
            this.data.merge('formData', {
                revisionId: '',
                configId: ''
            });
        }
        this.fire('config-change', target);
        this.fire('change-upgrade-status', {});
    }

    onConfigIdChange(target: {value: string}) {
        this.data.set('formData.configId', target.value);
        this.data.set('formData.revisionId', '');
        this.fire('change-upgrade-status', {});
        this.getConfigVersions(target.value);
    }

    onRevisionIdChange(target: {value: string}) {
        this.data.set('formData.revisionId', target.value);
        this.fire('change-upgrade-status', {});
    }

    getConfigVersions(configId: string) {
        const params  = {pageNo: 1, pageSize: 1000};
        this.data.set('configVersionLoading', true);
        return api.getConfigVersions(configId, params)
            .then((res: {result: RevisionItem[]}) => {
                this.data.set('revisionSource',
                    _.map(res.result, i => ({value: i.revisionId, text: i.revisionId, alias: i.description, createTime: formatTime(i.createTime)})));
                if (!this.data.get('isDetail') || configId !== this.data.get('originalData.configId')) {
                    this.data.set('formData.revisionId', res.result[0].revisionId);
                }
                this.data.set('configVersionLoading', false);
                return Promise.resolve();
            });
    }

    // 获取版本
    listConfigs() {
        this.data.set('configLoading', true);
        const param = {
            pageNo: 1,
            pageSize: 1000,
        };
        api.listConfig(param)
            .then((target: {result: ConfigItem[]}) => {
                this.data.set('configSource',
                    _.map(target.result, i => ({value: i.configId, text: i.name, alias: i.description, createTime: formatTime(i.createTime)})));
                if (!this.data.get('isDetail')) {
                    this.data.set('formData.configId', target.result[0].configId);
                    this.getConfigVersions(target.result[0].configId);
                }
                else {
                    const configId = this.data.get('originalData.configId') || target.result[0].configId;
                    this.data.set('formData.configId', configId);
                    this.getConfigVersions(configId);
                }
            })
            .finally(() => this.data.set('configLoading', false));
    }

    // 获取版本
    getVersion() {
        api.listAvailableVersion({})
            .then((target: {versions: versionType[]}) => {
                this.data.set('versions',
                    _.map(target.versions, i => ({
                        ...i,
                        value: i.version,
                        text: i.version + VersionTypeEnum[i.status]
                    })));
            })
            .finally(() => this.data.set('versionLoading', false));
    }

    onVersionChange(e: {value: versionType['version']}) {
        const versions = this.data.get('versions') as versionType[];
        const selectVersion = versions.find(item => item.value === e.value);
        this.fire('version-change', selectVersion);
    }

    // 集群名称改变
    onNameInputChange(target: {value: string}) {
        this.data.set('nameErr', '');
    }

    // 检测名称是否重复
    checkName(): Promise<void> {
        return api.checkClusterName({name: this.data.get('formData.name')})
            .then((data: {duplicateName: boolean}) => {
                if (data.duplicateName) {
                    throw 'repeat';
                }
                else {
                    this.data.set('nameErr', '');
                    return Promise.resolve();
                }
            })
            .catch((err: 'repeat' | object) => {
                this.data.set('nameErr', err === 'repeat'
                    ? '与已有集群名称重复，请修改名称'
                    : err?.message?.field?.name);
                throw 'name error';
            });
    }

    // 校验
    verify() {
        const node = (this.ref('form') as unknown as Form);
        const arr = [
            // 这里不用使用node.validateFields(['version'])，会一直pending，所以临时采用node.validateFields()
            node.validateFields(),
        ];
        if (!this.data.get('isDetail')) {
            arr.push(node.validateFields(['name']).then(this.checkName.bind(this)));
        }
        return Promise.all(arr).catch((err: Error) => {
            throw (ClusterRefType.CLUSTER);
        });
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        return {};
    }

    checkISChange() {
        const formData = this.data.get('formData');
        const originalData = this.data.get('originalData');
        const {isDetail} = this.data.get('');

        if (!isDetail) {
            return true;
        }

        if (formData.config !== originalData.config
            || originalData.configId && formData.configId !== originalData.configId
            || originalData.revisionId && formData.revisionId !== originalData.revisionId
        ) {
            return true;
        }

        return false;
    }

    getDetailPrice() {
        return {};
    }

    getUpgrageData() {
        const {configId, revisionId} = this.data.get('formData');
        return {
            type: UpgrageType.UPDATE_KAFKA_CONFIG,
            configId,
            revisionId
        };
    }

    getNewUpgrageData() {
        const {configId, revisionId, config} = this.data.get('formData');
        const {configId: configIdOld, revisionId: revisionIdOld, config: configOld} = this.data.get('originalData');

        let params: NormalObject = {};
        let types: UpgrageType[] = [];

        if (
            config !== configOld
            || configIdOld && configId !== configIdOld
            || revisionIdOld && revisionId !== revisionIdOld
        ) {
            params.configId = configId;
            params.revisionId = revisionId;
            types = [UpgrageType.UPDATE_KAFKA_CONFIG];
        }

        params.types = types;
        return params;
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData(): OrderItemObj {
        const {formData, versions, configs, configSource} = this.data.get('');
        const {name, version, config, configId, revisionId} = formData;

        const res =  {
            title: TextMap.title,
            type: ClusterRefType.CLUSTER,
            list: [
                {
                    label: TextMap.name,
                    text: name
                },
                {
                    label: TextMap.version,
                    text: getItemInfoInArr(versions, version)
                },
                {
                    label: TextMap.config,
                    text: getItemInfoInArr(configs, config)
                }
            ]
        };
        if (config === 'selfDefine') {
            const configName = _.find(configSource, i => i.value === configId).text;
            res.list = res.list.concat([{
                label: TextMap.configId,
                text: configName
            },
            {
                label: TextMap.revisionId,
                text: revisionId
            }]);
        }

        return res;
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const {name, version, configId = '', revisionId = ''} = this.data.get('formData');
        return {
            name: name,
            version,
            configMeta: {
                configId,
                revisionId
            }
        };
    }

    resetData(obj: NormalObject) {
        const {name, version, config} = obj;
        this.data.set('originalData', config ? {
            config: 'selfDefine',
            ...config
        } : {
            config: 'default'
        });
        this.nextTick(() => {
            this.data.merge('formData', {
                name,
                version,
                config: config ? 'selfDefine' : 'default',
                configId: config ? config.configId : '',
                revisionId: config ? config.revisionId : ''
            });
        });
        if (config) {
            this.getConfigVersions(config.configId);
        }
    }

    @throttle(1000)
    setData(obj: NormalObject) {
        const {name, version, config} = obj;
        this.data.set('originalData', config ? {
            config: 'selfDefine',
            ...config
        } : {
            config: 'default'
        });
        this.nextTick(() => {
            this.data.set('configSource', [{value: config?.configId, text: config?.configName, alias: '', createTime: ''}]);
            const selectConfig = config ? 'selfDefine' : 'default';
            this.nextTick(() => {
                this.data.merge('formData', {
                    name,
                    version,
                    config: selectConfig,
                    configId: config ? config.configId : '',
                    revisionId: config ? config.revisionId : ''
                });
            });
            if (config) {
                this.listConfigs();
            }
            this.fire('config-change', {value: selectConfig});
        });
    }
}
