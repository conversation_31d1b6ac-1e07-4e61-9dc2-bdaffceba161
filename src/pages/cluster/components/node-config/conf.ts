import {AvailableInfo, Deployment} from '@/common/enums/constant';
import {UpgrageType} from '../../util/conf';
import {formatEmpty} from '@/common/util';

export enum ZoneType {
    SINGLE = 'single',
    MULTI = 'multi'
}

export const DeploymentText = {
    [Deployment.PERFROMANCE]: '高性能模式',
    [Deployment.AVAILABILITY]: '高可用模式'
};

export const Deployments = [
    {text: DeploymentText[Deployment.PERFROMANCE], value: Deployment.PERFROMANCE},
    {text: DeploymentText[Deployment.AVAILABILITY], value: Deployment.AVAILABILITY, disabled: true}
];

export enum ZoneText {
    PERFROMANCE = '高性能模式选择1个可用区',
    AVAILABILITY = '高可用模式选择2-3个可用区，'
}

export const TextMap = {
    title: '节点配置',
    nodeType: '节点类型：',
    brokerDeploySet: '部署集：',
    singleNumber: '单区节点数：',
    storageType: '磁盘类型：',
    storageSize: '单节点容量：',
    deployType: '部署方式：',
    zone: '可用区：',
    edgezone: '边缘区域：',
    totalNumber: '总节点数：',
    nodeStorageSize: '节点磁盘容量：',
    totalStorageSize: '总磁盘容量',
    diskNum: '个',
    diskStorage: '磁盘大小：',
    specificationRecommand: '规模预估工具：'
};

export interface BccFlavor {
    name: string;
    cpu: string;
    memory: string;
    type: string;
    status: AvailableInfo;
    bandwidth: number;
    partitions: number;
    workload: string;
    upgrade: boolean;
}

export interface CdsFlavor {
    storageType: string;
    status: AvailableInfo;
}

export const SingleNumberMin = 3;

// 可用区为高性能最大长度为1
export const ZoneSingleMax = 1;

export enum FrozenFieldsEnum {
    nodeType = 'nodeType',
    singleNumber = 'singleNumber',
}

export const FrozenFields = [FrozenFieldsEnum.nodeType, FrozenFieldsEnum.singleNumber];

export enum FrozenFieldsUpgrageEnum {
    numberOfBrokerNodesPerZone = 'numberOfBrokerNodesPerZone',
    nodeType = 'nodeType'
}

export const FrozenFieldsUpgrageMap = {
    [FrozenFieldsUpgrageEnum.numberOfBrokerNodesPerZone]: UpgrageType.INCREASE_BROKER_COUNT,
    [FrozenFieldsUpgrageEnum.nodeType]: UpgrageType.UPDATE_BROKER_NODE_TYPE
};

export const FrozenFieldsUpgrage = {
    [FrozenFieldsEnum.nodeType]: FrozenFieldsUpgrageEnum.nodeType,
    [FrozenFieldsEnum.singleNumber]: FrozenFieldsUpgrageEnum.numberOfBrokerNodesPerZone,
};

export const nodeColumns = [
    {name: 'name', label: '规格名称', width: 200},
    {name: 'cpu', label: 'CPU（核）', width: 100},
    {name: 'memory', label: '内存（GB）', width: 100},
    {
        name: 'bandwidth',
        label: '内网带宽（Gbps）',
        width: 150,
        render: (item: {bandwidth?: number}) => formatEmpty(item.bandwidth)
    },
    {
        name: 'partitions',
        label: '推荐最大分区数',
        width: 150,
        render: (item: {partitions?: number}) => formatEmpty(item.partitions)
    },
];
