/* eslint-disable max-nested-callbacks */
/* eslint-disable max-len */
/**
 * 节点配置模块
 *
 * @file node-config.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Select, Form, Radio, InputNumber, Checkbox, Loading, Table, Badge, Switch, Button, Cascader, Alert} from '@baidu/sui';
import {GiveTheThumbsUp} from '@baidu/xicon-san';
import {UpgrageType} from '../../util/conf';
import {DOCS_LINK, TICKET_LINK} from '@/common/config';
import {VAILDITE_ITEMS} from '@/common/rules';
import api from '@/common/client';
import {getItemInfoInArr, renderZoneLabel} from '@/common/util';
import {ClusterRefType, Deployment} from '@/common/enums/constant';
import {renderSwitch} from '@/common/html';

import BaseCmpt from '../base-cmpt';
import Tip from '@/components/tip';
import SpecificationRecommand from '../specification-recommand';
import {UtilHandler} from '../../util/util';
import {
    ZoneType,
    DeploymentText,
    Deployments,
    ZoneText,
    TextMap,
    BccFlavor,
    CdsFlavor,
    SingleNumberMin,
    FrozenFields,
    FrozenFieldsEnum,
    FrozenFieldsUpgrageEnum,
    FrozenFieldsUpgrageMap,
    FrozenFieldsUpgrage,
    nodeColumns
} from './conf';

import ClusterShrink from './cluster-shrink';

import './index.less';

const $flag = ServiceFactory.resolve('$flag');
const isXushang = $flag.KafkaXushang;
const klass = 'bms-cluster-create-node';

interface Region {
    region: string;
    name: string;
    country: string;
    countryName: string;
    cities: City[];
}

interface City {
    city: string;
    name: string;
    serviceProviders: ServiceProvider[];
}

interface ServiceProvider {
    serviceProvider: string;
    name: string;
    regionId: string;
}

interface RegionInfo {
    all: string[];
    sellout: string[];
    hp: string[];
    regions: Region[];
}

export default class extends BaseCmpt {
    static template = html` <div class="${klass} bms-form-panel">
        <s-append noHighlight label="${TextMap.title}">
            <div slot="extra">
                <a
                    s-if="{{!isDetail && showPayLoad}}"
                    class="append-extra"
                    href="javascript:void(0);"
                    on-click="onSpecificationRecommand"
                >
                    <s-thumbs-up class="mr4" theme="line" color="#2468f2" size="{{16}}" strokeLinejoin="round" />
                    规模预估工具
                </a>
                <span class="partition-limit">此配置建议分区数上限为：{{partitionLimit}}</span>
            </div>
        </s-append>
        <s-form s-ref="form" rules="{{rules}}" data="{{formData}}">
            <s-formitem label="${TextMap.deployType}">
                <template s-if="{{!isDetail}}">
                    <s-radio-group
                        value="{= formData.deployType =}"
                        datasource="{{deployments}}"
                        radioType="button"
                        on-change="onDeployChange"
                    />
                </template>
                <span class="upgrage-text" s-else> {{formData.deployType | formatDeployType}} </span>
            </s-formitem>
            <s-formitem class="form-item-center">
                <span slot="label">
                    ${TextMap.brokerDeploySet}
                    <tip-cmpt s-if="{{!isDetail}}" type="question">
                        <span s-if="${isXushang}">开启部署集后，节点会分布在不同宿主机上以提升可用性，但会提高集群创建、变更的失败概率。</span>
                        <span s-else>
                            开启
                            <a href="${DOCS_LINK.brokerDeploySet}" target="_blank">部署集</a>
                            后，节点会分布在不同宿主机上以提升可用性，但会提高集群创建、变更的失败概率。
                        </span>
                    </tip-cmpt>
                </span>
                <template s-if="{{!isDetail}}">
                    <s-switch checked="{= formData.deploySetEnabled =}" />
                </template>
                <span class="upgrage-text" style="margin-top: 0" s-else>
                    {{formData.deploySetEnabled | formatdeploySetEnabled}}
                </span>
            </s-formitem>
            <s-formitem label="${TextMap.zone}" class="available-areas" prop="zone" s-if="!isEdgeRegion">
                <template s-if="{{!isDetail}}">
                    <template s-if="{{!availableLoading}}">
                        <s-radio-group
                            value="{= formData.zone =}"
                            on-change="onRadioChange"
                            s-if="{{formData.deployType === '${Deployment.PERFROMANCE}'}}"
                        >
                            <s-radio
                                s-for="item in availableAreasFirst"
                                disabled="{{item.soldout}}"
                                value="{=item.value=}"
                            >
                                <s-badge
                                    s-if="{{item.soldout}}"
                                    text="售罄"
                                    skin="normal"
                                    offset="{{[-10, 0]
                                }}">
                                    {{item.text}}
                                </s-badge>
                                <span s-else>{{item.text}}</span>
                            </s-radio>
                        </s-radio-group>
                        <s-checkbox-group
                            s-else
                            class="available-areas__checkbox"
                            value="{{formData.zoneList}}"
                            on-change="onCheckBoxChange"
                        >
                            <s-checkbox
                                s-for="item in availableAreasSecond"
                                disabled="{{item.disabled || item.soldout}}"
                                value="{{item.value}}"
                            >
                                <s-badge
                                    s-if="{{item.soldout}}"
                                    text="售罄"
                                    skin="normal"
                                    offset="{{[-10, 0]
                                }}">
                                    {{item.text}}
                                </s-badge>
                                <span s-else>{{item.text}}</span>
                            </s-checkbox>
                        </s-checkbox-group>
                        <p class="desc mt4">
                            {{formData.deployType === '${Deployment.PERFROMANCE}' ? '${ZoneText.PERFROMANCE}' :
                            '${ZoneText.AVAILABILITY}'}}
                            <span
                                s-if="formData.deployType === '${Deployment.AVAILABILITY}'"
                                style="color: #FF9326"
                            >
                                推荐3个可用区
                            </span>
                        </p>
                        <span class="err" s-if="{{zoneErr && formData.deployType === '${Deployment.AVAILABILITY}'}}">
                            {{zoneErr}}
                        </span>
                    </template>
                    <s-loading s-else loading />
                </template>
                <span s-else> {{formData.logicalZones | formatLogicalZones}} </span>
            </s-formitem>
            <s-formitem label="${TextMap.edgezone}" prop="edgezone" s-else>
                地区：
                <s-select
                    datasource="{{cities}}"
                    value="{{formData.cities}}"
                    style="margin-right: 8px"
                    on-change="onCitiesChange"
                    disabled="{{isDetail}}"
                    labelFilter="{{labelFilter}}"
                    width="150">
                </s-select>
                运营商：
                <s-select
                    datasource="{{serviceProviders}}"
                    style="margin-right: 8px"
                    value="{{formData.zone}}"
                    labelFilter="{{serviceProvidersFilter}}"
                    disabled="{{isDetail}}"
                    on-change="onProviderChange"
                    width="150">
                </s-select>
            </s-formitem>
            <s-formitem label="${TextMap.nodeType}" prop="nodeType">
                <div class="nodetype-panel">
                    <s-radio-group
                        s-if="showPayLoad && !tableLoading"
                        on-change="handleRadioGroupChange"
                        value="{=payloadType=}"
                        radioType="button"
                        disabled="{{isDetail}}"
                        datasource="{{payloadTypeSource}}"
                        name="payloadType"
                        class="payload-type-radio"
                    />
                    <tip-cmpt s-if="showPayLoad && !tableLoading" placement="right" type="question">
                        <div>
                            <p>生产工作负载：</p>
                            <p>-- 用于高吞吐、高并发的生产工作负载场景</p>

                            <p>小型工作负载：</p>
                            <p>-- 用于工作负载极低或者低成本开发的场景</p>
                        </div>
                    </tip-cmpt>
                    <s-loading s-if="showPayLoad && tableLoading" loading />
                    <s-table
                        datasource="{{nodeTypes}}"
                        columns="{{nodeColumns}}"
                        loading="{{tableLoading}}"
                        rowClassName="{{rowClassName}}"
                        selection="{{selection}}"
                        on-selected-change="onChangeNodeType"
                        class="nodetype-table mt16"
                    >
                        <div slot="h-partitions">
                            推荐最大分区数
                            <tip-cmpt placement="top" type="question">
                                单个节点的最佳分区数上限，集群最大分区总数是所有节点的累加和，使用分区总数超过后，会出现吞吐性能下降，增加集群稳定性风险，降低集群的SLA。
                            </tip-cmpt>
                        </div>
                    </s-table>
                    <p
                        s-if="showPayLoad && payloadType === 'low'"
                        class="desc mt10"
                    >
                        该规格只能创建{{quotaAll}}个最小规格集群，现已创建{{quotaUsed}}个集群，适用于写流量小于80M/s的场景，不能{{!isDetail ? '变更节点规格和' : ''}}扩容节点数量。
                    </p>
                    <p class="desc mt10" s-if="{{allsellout}}">
                        暂无资源，请
                        <a
                            s-if="!${isXushang}"
                            href="${TICKET_LINK}"
                            target="blank"
                            class="inline-desc"
                            style="margin-left: 0"
                        >提交工单</a>
                        <span s-else>提交工单</span>。
                    </p>
                </div>
            </s-formitem>
            <s-formitem label="${TextMap.singleNumber}">
                <s-inputnumber
                    value="{= formData.singleNumber =}"
                    min="{{isDetail ? minLimit.singleNumber : singleNumberMin}}"
                    max="{{singleNumberMax}}"
                    on-change="onChangeSingleNumber"
                    disabled="{{disabledByModified.singleNumber || !isUpgrading || payloadType === 'low' || tableLoading}}"
                    step="{{1}}"
                    stepStrictly
                />
                <s-button
                    skin="stringfy"
                    style="position: relative; left: -8px"
                    s-if="KafkaDecreaseBrokerCount"
                    disabled="{{shrinkDisabled}}"
                    on-click="onClusterShrink"
                >
                    更多设置
                </s-button>
                <p class="desc mt10" s-if="!isEdgeRegion">
                    总节点数：{{singleNumberTextNumber}}
                    <span class="desc mt10" s-if="{{isDetail && copyInfo.singleNumber !== formData.singleNumber}}">
                        ，原总节点数：{{copyInfo.singleNumber * formData.logicalZones.length}}
                    </span>
                </p>
                <s-alert
                    skin="info"
                    showIcon="{{true}}"
                    class="mt8"
                    s-if="{{formData.singleNumber > copyInfo.singleNumber && copyInfo.publicIpMode === 'MANUAL_SELECT'}}"
                >
                    总节点数扩容，请在「网络配置」增加EIP实例，使节点数与EIP实例数量对应
                </s-alert>
                <s-alert
                    skin="warning"
                    showIcon="{{true}}"
                    class="mt8"
                    s-if="{{formData.singleNumber < copyInfo.singleNumber && copyInfo.publicIpMode === 'MANUAL_SELECT'}}"
                >
                    节点缩容后，多出的EIP实例将自动解绑，解绑后的EIP实例不会被释放，将持续产生费用，如需释放请参考<a href="/eip/#/eip/instance/list" target="_blank">EIP实例列表</a>
                </s-alert>
            </s-formitem>
        </s-form>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-inputnumber': InputNumber,
        's-select': Select,
        's-form': Form,
        's-formitem': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-loading': Loading,
        's-table': Table,
        's-switch': Switch,
        'tip-cmpt': Tip,
        's-button': Button,
        's-thumbs-up': GiveTheThumbsUp,
        's-badge': Badge,
        's-cascader': Cascader,
        's-alert': Alert
    };

    initData() {
        return {
            rules: {
                zone: [VAILDITE_ITEMS.requiredSelect],
                nodeType: [{
                    validator: (rule: any, value: string, callback: Function) => {
                        if (!value) {
                            return callback('请选择节点');
                        }
                        callback();
                    }
                }]
            },
            nodeTypes: [],
            formData: {
                deployType: Deployment.PERFROMANCE,
                zone: '',
                zoneList: [],
                singleNumber: SingleNumberMin,
                deploySetEnabled: true
            },
            isWhiteUser: false,
            payloadTypeSource: [
                {label: '生产工作负载', value: 'high'},
                {label: '小型工作负载', value: 'low', disabled: true}
            ],
            payloadType: 'high',
            deployments: [],
            availableAreas: [],
            availableLoading: true,
            selectZones: [],
            getPopupContainer: () => document.body,
            // 集群变更下，备份信息，以便单次使用
            copyInfo: {},
            // 集群变更下，禁用使用
            disabledByModified: {},
            // 集群变更下，最小值设置
            minLimit: {},
            nodeColumns: nodeColumns,
            selection: {
                mode: 'single',
                selectedItems: [],
                selectedIndex: [0],
                disabledIndex: []
            },
            tableLoading: true,
            quotaUsed: 0,
            quotaAll: 0,
            rowClassName(item, index) {
                if (item.status === 'sellout') {
                    return 'custom-sellout';
                }
            },
            allsellout: false,
            KafkaDecreaseBrokerCount: false,
            decreaseData: {},
            isEdgeRegion: false,
            labelFilter(labels: string[]) {
                if (labels.length >= 5) {
                    return labels.slice(0, 4).join(',') + ' ...';
                }
                else {
                    return labels.join(',');
                }
            },
            serviceProvidersFilter(labels: string[]) {
                return labels.join(',');
            },
        };
    }

    static computed: SanComputedProps = {
        nodeTypeText() {
            const {nodeType} = this.data.get('formData');
            const nodeTypes = this.data.get('nodeTypes');
            return _.find(nodeTypes, node => node.value === nodeType)?.text;
        },
        singleNumberTextNumber() {
            const deployType = this.data.get('formData.deployType');
            const singleNumber = this.data.get('formData.singleNumber');
            const zoneList = this.data.get('formData.zoneList') || [];
            return deployType === Deployment.PERFROMANCE ? singleNumber : singleNumber * zoneList.length;
        },
        singleNumberMax() {
            const deployType = this.data.get('formData.deployType');
            const zoneList = this.data.get('formData.zoneList') || [];
            const payloadType = this.data.get('payloadType');
            const isWhiteUser = this.data.get('isWhiteUser');
            const maxNum = isWhiteUser ? 1002 : 30;
            if (payloadType === 'high') {
                return deployType === Deployment.PERFROMANCE ? maxNum : maxNum / zoneList.length;
            }
            else {
                return deployType === Deployment.PERFROMANCE ? 3 : zoneList.length === 2 ? 2 : 1;
            }
        },
        singleNumberMin() {
            const deployType = this.data.get('formData.deployType');
            const zoneList = this.data.get('formData.zoneList') || [];
            return deployType === Deployment.PERFROMANCE ? SingleNumberMin : zoneList.length === 2 ? 2 : 1;
        },
        isDependShow() {
            const deployType = this.data.get('formData.deployType');
            const selectZones = this.data.get('selectZones');
            const zoneList = this.data.get('formData.zoneList') || [];
            if (deployType === Deployment.PERFROMANCE) {
                return true;
            }
            return selectZones.length === zoneList.length;
        },
        selectPlaceholder() {
            const deployType = this.data.get('formData.deployType');
            const isDependShow = this.data.get('isDependShow');
            return deployType === Deployment.PERFROMANCE || isDependShow ? '请选择' : '请您选取2-3个可用区';
        },
        mapDetailNodeType() {
            const nodeType = this.data.get('copyInfo.nodeType');
            const nodeTypes = this.data.get('nodeTypes');
            return getItemInfoInArr(nodeTypes, nodeType) || nodeType;
        },
        partitionLimit() {
            const numberOfBrokerNodes = this.data.get('singleNumberTextNumber');
            const nodeType = this.data.get('formData.nodeType');
            const nodeTypes = this.data.get('nodeTypes');

            const index = _.findIndex(nodeTypes, item => item.type === nodeType);

            return numberOfBrokerNodes * nodeTypes[index]?.partitions || 0;
        },
        shrinkDisabled() {
            const singleNumber = this.data.get('formData.singleNumber');
            const originalSingleNumber = this.data.get('copyInfo.singleNumber');
            const isDetail = this.data.get('isDetail');
            return !(singleNumber < originalSingleNumber && isDetail);
        }
    };

    static filters: SanFilterProps = {
        formatDeployType: (deployType: Deployment) => DeploymentText[deployType],
        formatLogicalZones: UtilHandler.logicalZones,
        formatdeploySetEnabled: UtilHandler.deploySetEnabled
    };

    inited() {
        this.fire('select-zones', []);
    }

    async attached() {
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        this.data.set('isEdgeRegion', isEdgeRegion);
        if (isEdgeRegion) {
            this.data.set('deployments', [{text: DeploymentText[Deployment.PERFROMANCE], value: Deployment.PERFROMANCE}]);
            if (!this.data.get('isDetail')) {
                this.getEdgeZones();
            }
        }
        else {
            this.data.set('deployments', Deployments);
            if (!this.data.get('isDetail')) {
                await this.getZones();
            }
            this.getSingleNumberWhiteInfo();
            this.getQuota();
            this.getNodeTypeWhiteInfo();
        }
        this.watch('isUpgrading', isUpgrading => {
            const nodeTypes = this.data.get('nodeTypes');
            if (isUpgrading) {
                this.resetDetail();
            } else {
                this.data.merge('selection', {disabledIndex: Array.from(new Array(nodeTypes.length).keys())});
            };
        });
        this.watch('singleNumberTextNumber', (value: number) => {
            this.fire('singleNumberTextNumber-change', {singleNumberTextNumber: value});
        });
    }

    getQuota() {
        api.getQuota({}).then((result: {used: number, quota: number}) => {
            this.data.set('quotaUsed', result.used);
            this.data.set('quotaAll', result.quota);
        });
    }

    // 获取磁盘白名单信息
    getNodeTypeWhiteInfo() {
        const featureTypes = ['KafkaNodeType_INTEL_1_2', 'KafkaNodeType_AMD_1_4', 'KafkaNodeType_AMD_1_2'];
        api.getUserAcls({featureTypes}).then((target: {isExist: boolean}) => {
            this.data.set('showPayLoad', !target.isExist);
        });
    }

    // 获取单区节点数白名单信息
    getSingleNumberWhiteInfo() {
        const featureTypes = ['BrokerCountWhitelist'];
        api.getUserAcls({featureTypes}).then((target: {isExist: boolean}) => {
            this.data.set('isWhiteUser', target.isExist);
        });
    }

    getSelloutIndex(nodeTypes: any[]) {
        let res = [];
        nodeTypes.forEach((item, idx) => {
            item.status === 'sellout' && res.push(idx);
        });

        return res;
    }

    handleRadioGroupChange(target: {value: string}) {
        const nodeTypes = this.data.get('bccFlavors').filter(item => item.workload === target.value);
        this.data.set('nodeTypes', nodeTypes);
        this.data.set('payloadType', target.value);
        const {isDetail, quotaAll, quotaUsed, singleNumberMin} = this.data.get('');
        let disableArr = [];
        if (target.value === 'low' && quotaAll === quotaUsed) {
            disableArr = Array.from(new Array(nodeTypes.length).keys());
        }
        else {
            disableArr = this.getSelloutIndex(nodeTypes);
        }
        this.fire('payload-change', {payloadType: target.value});
        let idx = _.findIndex(nodeTypes, item => item.status !== 'sellout');
        idx = quotaAll === quotaUsed && target.value === 'low' ? -1 : idx;
        this.data.merge('selection', {
            mode: 'single',
            selectedItems: idx !== -1 ? [nodeTypes[idx]] : [],
            selectedIndex: idx !== -1 ? [idx] : [],
            disabledIndex: disableArr
        });
        !isDetail && this.data.set('formData.singleNumber', singleNumberMin);
    }

    // 打开配置推荐弹窗
    onSpecificationRecommand() {
        const {payment, timeLength, timeUnit} = this.data.get('');
        const dialog = new SpecificationRecommand({
            data: {
                payment,
                timeLength,
                timeUnit
            }
        });
        dialog.attach(document.body);
        dialog.on('success', (applyData: {formData, storageMetaSource}) => {
            this.onAllConfigChange(applyData);
        });
    }

    // 表单项改变时，执行一下各表单项on-change事件中的逻辑
    async onAllConfigChange(applyData) {
        const formData = applyData.formData;
        this.data.set('applyData', applyData);
        const {deployType} = this.data.get('formData');
        if (formData.deployType !== deployType) {
            await this.onDeployChange({value: formData.deployType});
        }
        if (formData.deployType === 'HA') {
            await this.onCheckBoxChange({value: formData.zoneList});
        }
        else if (deployType === 'HA' && formData.deployType === 'HP') {
            this.data.set('formData.zone', formData.zone);
            this.setSelectZones();
        }
        else {
            await this.onRadioChange({value: formData.zone});
        }

        this.data.merge('formData', formData);
        this.data.set('payloadType', 'high');
        const nodeTypes = this.data.get('bccFlavors').filter(item => item.workload === 'high');
        this.data.set('nodeTypes', nodeTypes);
        const index = _.findIndex(nodeTypes, item => item.type === formData.nodeType);
        const disableArr = this.getSelloutIndex(nodeTypes);
        this.data.set('selection', {
            mode: 'single',
            selectedItems: [nodeTypes[index]],
            selectedIndex: [index],
            disabledIndex: disableArr
        });
        this.fire('storageMetaSource-apply', {
            storageType: applyData.formData.storageType,
            storageMetaSource: applyData.storageMetaSource
        });
        this.data.set('isApply', true);
        this.fire('price-config-change', {isDownGrade: false});
    }

    onChangeNodeType(target: {value: {selectedItems: [], selectedIndex: []}}) {
        const nodeType = target.value.selectedItems[0]?.type;
        this.data.merge('selection', {
            selectedItems: target.value.selectedItems,
            selectedIndex: target.value.selectedIndex
        });
        this.setFrozenField('nodeType', nodeType);
        this.data.set('formData.nodeType', nodeType);
    }

    onChangeSingleNumber(target: {value: number}) {
        this.setFrozenField('singleNumber', target.value);
    }

    // 价格配置改变方法
    onPriceConfigChange() {
        const isDownGrade = this.data.get('isDownGrade') || false;
        this.nextTick(() => {
            // 防止nodeType没值的时候发送询价请求从而报错
            if (this.data.get('formData.nodeType')) {
                this.fire('price-config-change', {isDownGrade});
            }
        });
    }

    // 获取可用区列表
    async getZones() {
        const data = await api.listZone({});
        this.data.set('ha', data.ha);
        this.data.set('deployments[1].disabled', data.ha.length <= 0);
        const area = _.map(data.all, i => ({
            value: i,
            text: renderZoneLabel(i),
            soldout: _.includes(data.sellout, i)
        }));
        this.data.set('availableAreasFirst', area);
        this.data.set('availableAreasSecond', area);
        const value = data.hp[0] ? data.hp[0] : '';
        this.data.set('formData.zone', value);
        this.setSelectZones();
        await this.getFlavor();
        this.data.set('availableLoading', false);
    }

    async getEdgeZones() {
        await api.getEdgeNodeList({})
            .then((data: {regions: Region[]}) => {
                let cities: object[] = [];
                data.regions.forEach(item => {
                    item.cities?.forEach(i => {
                        cities.push({
                            text: item.name + '-' + i.name,
                            value: i.city,
                        });
                    });
                });
                this.data.set('cities', cities);
                this.data.set('regions', data.regions);
                window._regions_ = [...data.regions];
            });
    }

    // 设置选中区域
    setSelectZones() {
        const {zone, zoneList, deployType} = this.data.get('formData');
        const isSimple = deployType === Deployment.PERFROMANCE;
        const type = isSimple ? ZoneType.SINGLE : ZoneType.MULTI;
        const tem = deployType === Deployment.PERFROMANCE ? zone : zoneList;
        const zones = type === ZoneType.SINGLE ? [tem] : tem;
        this.data.set('selectZones', zones);
        this.fire('select-zones', isSimple || (!isSimple && _.includes([2, 3], zones.length)) ? zones : []);
    }

    onCitiesChange(target: {value: string}) {
        this.data.set('formData.cities', target.value);
        this.data.set('formData.zone', '');
        this.data.set('serviceProviders', []);
        const {formData, regions} = this.data.get('');
        regions.forEach(item => {
            item.cities.forEach(c => {
                if (_.includes(formData.cities, c.city)) {
                    this.data.set('serviceProviders', c.serviceProviders.map(i => ({
                        value: i.regionId,
                        text: i.name,
                    })));
                }
            });
        });
    }

    onProviderChange(target: {value: string}) {
        this.data.set('formData.zone', target.value);
        this.data.set('selectZones', target.value ? [target.value] : []);
        this.fire('select-zones', target.value ? [target.value] : []);
        this.getFlavor();
    }

    // 数据重置
    resetType() {
        this.data.set('formData.nodeType', '');
        this.data.set('selection', {
            mode: 'single',
            selectedItems: [],
            selectedIndex: []
        });
        this.data.set('nodeTypes', []);
        this.data.set('bccFlavors', []);
        this.data.set('payloadType', 'high');
        // 重置磁盘类型
    }

    // 部署方式的改变
    async onDeployChange(target: {value: Deployment}) {
        this.resetType();
        this.data.set('formData.deployType', target.value);
        this.fire('deploy-change', target.value);
        const {zoneList} = this.data.get('formData');
        const value = target.value === Deployment.PERFROMANCE ? SingleNumberMin : zoneList.length === 2 ? 2 : 1;
        this.data.set('formData.singleNumber', value);
        this.setSelectZones();
        await this.getFlavor();
    }

    // radio 改变
    async onRadioChange(target: {value: string}) {
        this.data.set('formData.zone', target.value);
        this.setSelectZones();
        this.resetType();
        await this.getFlavor();
    }

    // checkbox 改变，限制最多只能选取三个
    async onCheckBoxChange(target: {value: string[]}) {
        target.value = this.handleZones(target.value);
        this.data.set('formData.zoneList', target.value);
        const firstMap = new Map(target.value.map((value: string) => [value, true]));
        // ha: Array<string[]>
        const {ha, availableAreasSecond} = this.data.get('');
        const remainMap = new Map();
        // 为了可读性，使用两个if方式
        if (target.value.length === 1) {
            _.each(ha, item => {
                _.some(item, i => firstMap.get(i)) &&
                    _.each(item, i => {
                        // 判断项目中有选中后，单独设置没设置的项为true
                        !firstMap.get(i) && remainMap.set(i, true);
                    });
            });
        }
        if (target.value.length === 2) {
            _.each(ha, item => {
                let count = 0;
                // 先判断是否两个存在的项
                _.each(item, i => {
                    firstMap.get(i) && count++;
                });
                // 如果是存在，单独设置没选中的项为true
                if (count === 2) {
                    _.each(item, i => {
                        !firstMap.get(i) && remainMap.set(i, true);
                    });
                }
            });
            this.data.set('formData.singleNumber', 2);
        }

        if (target.value.length === 3) {
            this.data.set('formData.singleNumber', 1);
        }
        // 判断禁用逻辑：
        // 1.当长度为0时，所有都不禁用
        // 2.当长度为3时，如果在firstMap存在的才不禁用，其余禁用
        // 3.当长度少于3，如果在firstMap或remainMap存在，就不禁用，其余禁用
        _.each(availableAreasSecond, (item: {value: string, disabled?: boolean}, index: number) => {
            const ha = this.data.get('ha');
            const value = item.value;
            const notZoneBool = target.value.length !== 0;
            const maxLength = _.some(ha, haitem => {
                haitem.length === 2 && target.value.length === 2
                    && haitem.sort().toString() === target.value.sort().toString();
            }) ? 2 : 3;
            const maxLengthBool = target.value.length >= maxLength && !firstMap.get(value);
            const checkBool = !(firstMap.get(value) || remainMap.get(value));
            this.data.set(`availableAreasSecond[${index}].disabled`, notZoneBool && (maxLengthBool || checkBool));
        });
        this.setSelectZones();
        if (target.value.length >= 2) {
            this.data.set('payloadType', 'high');
            await this.getFlavor();
        } else {
            // 重置下节点、磁盘类型
            this.resetType();
        }
        this.verifyZone();
    }

    // 处理下可用区数据使得多选时，数据可以按照显示的多选顺序来显示
    handleZones(zones: string[]) {
        const availableAreasSecond = this.data.get('availableAreasSecond');
        const newMap = new Map(zones.map((value: string) => [value, true]));
        return _.map(
            _.filter(availableAreasSecond, item => newMap.get(item.value)),
            i => i.value
        );
    }

    // 手动校验可用区
    verifyZone() {
        const {zoneList, deployType} = this.data.get('formData');
        if (deployType === Deployment.PERFROMANCE) {
            this.data.set('zoneErr', '');
            return Promise.resolve();
        }
        const bool = deployType === Deployment.AVAILABILITY && zoneList.length < 2;
        this.data.set('zoneErr', bool ? '请您选取2-3个可用区' : '');
        return bool ? Promise.reject() : Promise.resolve();
    }

    // 获取套餐
    async getFlavor(): Promise<void> {
        const zones = this.data.get('selectZones');
        const {clusterId, isDetail} = this.data.get('');
        const param = isDetail ? {clusterId, zones} : {zones};
        if (zones.length === 0) {
            this.data.merge('formData.nodeType', '');
            this.data.set('nodeTypes', []);
            return Promise.resolve();
        }
        this.data.set('getFlavoring', true);
        this.data.set('tableLoading', true);
        return api
            .queryFlavor(param)
            .then((target: {instanceFlavors: {bccFlavors: BccFlavor[], cdsFlavors: CdsFlavor[]}}) => {
                this.formatInstance(target.instanceFlavors.bccFlavors);
                this.fire('flavor-loaded', {
                    cdsArr: target.instanceFlavors.cdsFlavors,
                    singleNumberTextNumber: this.data.get('singleNumberTextNumber'),
                    payloadType: this.data.get('payloadType'),
                });
                if (!this.data.get('isApply') && !this.data.get('isDetail')) {
                    this.onPriceConfigChange();
                }
                else {
                    this.data.set('isApply', false);
                }
                const {nodeTypes, isUpgrading} = this.data.get('');
                if (!isUpgrading) {
                    this.data.merge('selection', {disabledIndex: Array.from(new Array(nodeTypes.length).keys())});
                }
            })
            .finally(() => {
                this.data.set('getFlavoring', false);
                this.data.set('tableLoading', false);
            });
    }

    // 处理节点类型
    formatInstance(bccFlavors: BccFlavor[]) {
        this.data.set('bccFlavors', bccFlavors);
        if (this.data.get('isDetail')) {
            const {copyInfo} = this.data.get('');
            this.handleRadioGroupChange({value: copyInfo.workload});
            const {nodeTypes} = this.data.get('');
            const index = _.findIndex(nodeTypes, item => item.type === copyInfo.nodeType);
            this.onChangeNodeType({value: {selectedItems: [nodeTypes[index]], selectedIndex: [index]}});
        }
        else if (!this.data.get('isApply')) {
            const nodeTypes = bccFlavors.filter(item => item.workload === 'high');
            this.data.set('nodeTypes', nodeTypes);
            const disableArr = this.getSelloutIndex(nodeTypes);
            const idx = _.findIndex(nodeTypes, item => item.status !== 'sellout');
            this.data.merge('selection', {
                mode: 'single',
                selectedItems: idx !== -1 ? [nodeTypes[idx]] : [],
                selectedIndex: idx !== -1 ? [idx] : [],
                disabledIndex: disableArr
            });
            const checkHasLow = _.findIndex(bccFlavors, item => item.workload === 'low');
            this.data.set('payloadTypeSource[1].disabled', checkHasLow === -1);
            const allsellout = _.findIndex(nodeTypes, item => item.status === 'available');
            this.data.set('allsellout', allsellout === -1);
            const selection = this.data.get('selection');
            this.onChangeNodeType({value: {selectedItems: selection.selectedItems, selectedIndex: selection.selectedIndex}});
        }
    }

    // 当变配时，对应项改变时，设置其他禁用
    setFrozenField(type: string, value: number | string) {

        if (_.find(FrozenFields, item => item === type) && this.data.get('isDetail')) {
            const old = this.data.get(`copyInfo.${type}`);
            const isEmpty = old === value;
            if (!this.data.get('KAFKA_ClusterMultipleUpdateWhiteList')) {
                _.each(FrozenFields, item => {
                    this.data.set(`disabledByModified.${item}`, !isEmpty && type !== item);
                });
            }
            const {nodeTypes, payloadType, isUpgrading} = this.data.get('');
            if (type === 'nodeType') {
                const oldIdx = _.findIndex(nodeTypes, item => item.value === old);
                const newIdx = _.findIndex(nodeTypes, item => item.value === value);
                this.data.set('isDownGrade', oldIdx > newIdx);
            }
            else if (type === 'singleNumber') {
                this.data.set('isDownGrade', old > value);
            }
            const disabledByModified = this.data.get('disabledByModified');
            let disableArr = [];
            if (disabledByModified.nodeType || !isUpgrading) {
                disableArr = Array.from(new Array(nodeTypes.length).keys());
            }
            else {
                disableArr = this.getSelloutIndex(nodeTypes);
            }
            this.data.merge('selection', {
                disabledIndex: disableArr
            });

            !isEmpty && this.fire('change-upgrade-status', {});
        }
        // 触发价格改变
        this.onPriceConfigChange();
    }

    // 重置详情
    resetDetail() {
        const {nodeType, singleNumber, workload} = this.data.get('copyInfo');
        const {nodeTypes} = this.data.get('');
        this.data.merge('formData', {
            singleNumber,
            nodeType
        });
        this.data.set('disabledByModified', {});
        let disableArr = [];
        const index = _.findIndex(nodeTypes, item => item.type === nodeType);
        if (workload === 'low') {
            disableArr = Array.from(new Array(nodeTypes.length).keys());
        }
        else {
            disableArr = this.getSelloutIndex(nodeTypes);
        }
        this.data.merge('selection', {
            selectedItems: [nodeTypes[index]],
            selectedIndex: [index],
            disabledIndex: disableArr
        });
    }

    checkISChange() {
        const {isDetail} = this.data.get('');
        if (!isDetail) {
            return true;
        }
        const {originalData, formData} = this.data.get('');

        const {nodeType, singleNumber: numberOfBrokerNodesPerZone} = formData;
        const {nodeType: nodeTypeOld, numberOfBrokerNodesPerZone: numberOfBrokerNodesPerZoneOld} = originalData;
        // 配置是否发生变化
        if (nodeType !== nodeTypeOld
            || numberOfBrokerNodesPerZone !== numberOfBrokerNodesPerZoneOld
        ) {
            return true;
        }

        return false;
    }

    // 校验
    verify() {
        const isDependShow = this.data.get('isDependShow');
        const ref = this.ref('form') as unknown as Form;
        return Promise.all([
            this.verifyZone(),
            isDependShow ? ref.validateFields(['nodeType']) : ref.validateFields(['zone'])
        ]).catch((err: Error) => {
            throw ClusterRefType.NODE;
        });
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        const {singleNumberTextNumber, formData, nodeTypes, isDetail} = this.data.get('');
        const {nodeType} = formData;
        const idx = _.findIndex(nodeTypes, item => item.type === nodeType);
        let xsType = nodeTypes[idx]?.name || 'kafka.';
        if (isXushang && isDetail && xsType === 'kafka.') {
            xsType = this.data.get('copyInfo.nodeSpec');
        }
        let priceData = {
            nodeType,
            count: singleNumberTextNumber
        };
        if (isXushang) {
            priceData = {
                ...priceData,
                xsType
            };
        }
        return priceData;
    }

    getOldPriceData() {
        const {
            numberOfBrokerNodes,
            nodeType,
            nodeSpec,
            storageMeta
        } = this.data.get('originalData');
        let priceData = {
            storageMeta,
            nodeType,
            count: numberOfBrokerNodes
        };
        if (isXushang) {
            priceData = {
                ...priceData,
                xsType: nodeSpec || 'kafka.'
            };
        }
        return priceData;
    }

    getDetailPrice() {
        return this.getPriceData();
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData(): OrderItemObj {
        const {
            formData,
            deployments,
            availableAreasFirst,
            nodeTypes,
            singleNumberTextNumber,
            isEdgeRegion
        } = this.data.get('');
        const {deployType, zone, zoneList, logicalZones, nodeType, singleNumber, deploySetEnabled} = formData;
        let zoneText;
        const nodeIndex = _.findIndex(nodeTypes, item => item.type === nodeType);
        if (!this.data.get('isDetail')) {
            if (!isEdgeRegion) {
                zoneText
                = deployType === Deployment.PERFROMANCE
                        ? getItemInfoInArr(availableAreasFirst, zone)
                        : _.filter(availableAreasFirst, i => _.find(zoneList, z => z === i.value))
                            .map(i => i.text)
                            .join('，');
            }
            else {
                zoneText = renderZoneLabel(zone);
            }
        }
        else {
            zoneText = !isEdgeRegion ? UtilHandler.logicalZones(logicalZones) : renderZoneLabel(zone);
        }
        return {
            title: TextMap.title,
            type: ClusterRefType.NODE,
            list: [
                {
                    label: TextMap.deployType,
                    text: getItemInfoInArr(deployments, deployType)
                },
                {
                    label: TextMap.brokerDeploySet,
                    text: renderSwitch(deploySetEnabled)
                },
                {
                    label: TextMap.zone,
                    text: zoneText
                },
                {
                    label: TextMap.nodeType,
                    text: nodeTypes[nodeIndex]?.name
                },
                {
                    label: TextMap.singleNumber,
                    text: singleNumber
                },
                {
                    label: TextMap.totalNumber,
                    text: singleNumberTextNumber
                }
            ]
        };
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const singleNumberTextNumber = this.data.get('singleNumberTextNumber');
        const {deployType, zone, zoneList, nodeType, singleNumber, deploySetEnabled} = this.data.get('formData');
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        return {
            deployType,
            logicalZones: isEdgeRegion ? [zone] : deployType === Deployment.PERFROMANCE ? [zone] : zoneList,
            numberOfBrokerNodes: singleNumberTextNumber,
            numberOfBrokerNodesPerZone: singleNumber,
            nodeType,
            deploySetEnabled
        };
    }

    // 获取变更参数
    getUpgrageData() {
        const {copyInfo, decreaseData} = this.data.get('');
        const {nodeType, singleNumber} = this.data.get('formData');
        const index = _.findIndex(FrozenFields, item => !this.data.get(`disabledByModified.${item}`));
        if (index > -1) {
            const changeField = FrozenFields[index];
            const prop = FrozenFieldsUpgrage[changeField];
            const type = changeField !== FrozenFieldsEnum.singleNumber || singleNumber >= copyInfo.singleNumber
                ? FrozenFieldsUpgrageMap[prop] : UpgrageType.DECREASE_BROKER_COUNT;
            this.data.set('upgradeType', type);
            const propValue =
                prop === FrozenFieldsUpgrageEnum.numberOfBrokerNodesPerZone
                    ? singleNumber
                    : nodeType;
            const decreaseParam = type === UpgrageType.DECREASE_BROKER_COUNT ? decreaseData : {};
            return {
                [prop]: propValue,
                ...decreaseParam,
                type
            };
        }
        return {};
    }

    getNewUpgrageData(networkParams: NormalObject) {
        const {nodeType, singleNumber: numberOfBrokerNodesPerZone} = this.data.get('formData');
        const {nodeType: nodeTypeOld, numberOfBrokerNodesPerZone: numberOfBrokerNodesPerZoneOld} = this.data.get('originalData');

        let params: NormalObject = {};
        let types: UpgrageType[] = [];
        if (nodeType !== nodeTypeOld) {
            params.nodeType = nodeType;
            types.push(UpgrageType.UPDATE_BROKER_NODE_TYPE);
        }

        const isDecreaseBrokerCount = numberOfBrokerNodesPerZone < numberOfBrokerNodesPerZoneOld;
        if (numberOfBrokerNodesPerZone !== numberOfBrokerNodesPerZoneOld) {
            params.numberOfBrokerNodesPerZone = numberOfBrokerNodesPerZone;
            types.push(isDecreaseBrokerCount ? UpgrageType.DECREASE_BROKER_COUNT : UpgrageType.INCREASE_BROKER_COUNT);
        }

        // 如果是减少broker数量——》需要传eips 和 publicIpMode
        if (isDecreaseBrokerCount) {
            params = {
                ...params,
                eips: networkParams.eips || [],
                publicIpMode: networkParams.publicIpMode
            };
        }

        params.types = types;
        return params;
    }

    onClusterShrink(event: Event) {
        event.stopPropagation();
        const {clusterId, formData, dialogData} = this.data.get('');
        const dialog = new ClusterShrink({
            data:
            {
                clusterId,
                numberOfBrokerNodesPerZone: formData.singleNumber,
                dialogData
            }
        });
        dialog.attach(document.body);
        dialog.on('success', data => {
            this.data.set('decreaseData', data.decreaseData);
            this.data.set('dialogData', data.detailData);
        });
    }

    // 为了变更配置使用
    async setData(obj: NormalObject) {
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        this.data.set('originalData', obj);
        const {
            logicalZones,
            numberOfBrokerNodes,
            numberOfBrokerNodesPerZone,
            nodeType,
            nodeSpec,
            upgrade,
            deploySetEnabled
        } = obj;
        const deployType = Array.isArray(logicalZones) && logicalZones.length === 1
            ? Deployment.PERFROMANCE : Deployment.AVAILABILITY;
        const temObj = deployType === Deployment.PERFROMANCE
            ? {zone: logicalZones[0]} : {zoneList: logicalZones};

        this.data.set('copyInfo', {
            singleNumber: numberOfBrokerNodesPerZone,
            nodeType,
            nodeSpec,
            workload: upgrade ? 'high' : 'low',
            publicIpMode: obj.publicIpMode,
        });
        this.data.set('selectZones', logicalZones);
        this.data.merge('formData', {
            ...temObj,
            deployType,
            nodeType,
            logicalZones,
            cities: isEdgeRegion ? logicalZones[0].split('-')[1].toUpperCase() : '',
            zone: isEdgeRegion ? logicalZones[0] : '',
            deploySetEnabled,
            numberOfBrokerNodes,
            numberOfBrokerNodesPerZone,
            singleNumber: numberOfBrokerNodesPerZone
        });
        if (isEdgeRegion) {
            await this.getEdgeZones();
            const {regions} = this.data.get('');
            regions.forEach(item => {
                item.cities.forEach(c => {
                    if (obj.logicalZones[0]?.split('-')[1] === c.city.toLowerCase()) {
                        this.data.set('serviceProviders', c.serviceProviders.map(i => ({
                            value: i.regionId,
                            text: i.name,
                        })));
                    }
                });
            });
        }
        const {singleNumberMin, KafkaDecreaseBrokerCount} = this.data.get('');
        this.data.set('minLimit', {
            singleNumber: KafkaDecreaseBrokerCount ? singleNumberMin : numberOfBrokerNodesPerZone,
        });
        this.getFlavor();
    }
}
