/**
 * 节点配置
 *
 * @file index.less
 * <AUTHOR>
 */

@klass: bms-cluster-create-node;
@extra-text-color: #2468f2;
@white-color: #ffffff;

.@{klass} {

    .append-extra {
        margin-left: 16px;
        padding-right: 0;
        height: 16px;
        line-height: 16px;
        font-size: 12px;
        font-weight: normal;
        color: var(--descColor3);
        &:hover {
            color: #528eff;
        }
        &:active {
            color: #144bcc;
        }
        svg {
            position: relative;
            top: -1.5px;
        }
    }

    .partition-limit {
        font-size: 12px;
        color: #FF9326;
        font-weight: 400;
        margin-left: 8px;
    }

    .available-areas {
        padding-top: 5px;

        .s-form-item-label {
            line-height: 17px;
        }

        .s-row {
            display: flex;
            align-items: flex-start;

            .s-radio {
                margin-right: 40px;
            }

            .s-checkbox {
                margin-right: 40px;
            }
        }

        &__checkbox {
            height: 18px;
        }
    }

    .custom-sellout {
        > td.s-table-cell:first-child {
            position: relative;
            overflow: hidden;
            &::after {
                position: absolute;
                left: -18px;
                top: 0;
                font-size: 8px;
                content: '售罄';
                width: 40px;
                height: 16px;
                line-height: 16px;
                text-align: center;
                transform: rotate(-40deg) scale(.8);
                border-radius: 0;
                background: #B4B6BA;
                display: block;
                color: #fff;
                padding: 0 7px;
            }
        }
    }

    .s-table-row-disabled {

        > td.s-table-cell:first-child {
            position: relative;
            overflow: hidden;
        }

       .s-table-cell .s-table-cell-text {
            color: #999;
        }
    }
}

.nodetype-panel {

    .payload-type-radio {
        display: inline-block;
        margin-right: 8px;
    }

    .nodetype-table {
        width: 850px;
    }
}

.cluster-shrink {
    width: 650px;
    .s-form-item-label {
        label {
            display: inline-block;
            min-width: 67px;
        }
    }

    .yellow-text {
        color: #FFAB52;
    }

    &__bottom {
        position: absolute;
        right: 40px;
        bottom: 20px;
    }

    .topic-table {
        width: 500px;
        max-height: 200px;
        overflow-y: auto;
    }
}
