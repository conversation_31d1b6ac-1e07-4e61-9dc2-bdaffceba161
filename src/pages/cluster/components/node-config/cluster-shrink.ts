/**
 * 集群缩容抽屉弹窗
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import m from 'moment';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Form, Select, DatePicker, Radio, InputNumber, Button, Transfer, Table} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import Tip from '@/components/tip';
import api from '@/common/client';
import {TOPIC_RESPLIT_VALIDATE} from '@/common/rules';

const klass = 'cluster-shrink';
export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="节点缩容"
            class="formate-drawer"
            open="{{open}}"
            maskClose="{{true}}"
            otherClose="{{true}}"
            on-close="onClose"
            getContainer="{{getContainer}}"
            size="700"
        >
            <s-form
                class="form-format ${klass}"
                s-ref="form"
                rules="{{rules}}"
                data="{= formData =}">
                <s-form-item label="待删节点：" prop="decreaseBrokers" class="form-item-center">
                    <span>{{formData.decreaseBrokers}}</span>
                </s-form-item>
                <s-form-item label="保留节点：" prop="retentionBrokers" class="form-item-center">
                    <span>{{formData.retentionBrokers}}</span>
                </s-form-item>
                <s-form-item label="调整主题：">
                    <s-table
                        class="topic-table"
                        columns="{{columns}}"
                        datasource="{{formData.reassignTopics}}"
                    ></s-table>
                </s-form-item>
                <s-form-item label="分批执行：" required class="form-item-center">
                    <s-radio-group
                        datasource="{{radioGroup.batchSource}}"
                        value="{= isBatch =}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item
                    s-if="isBatch"
                    label="分区个数："
                    class="inline-form-item"
                    prop="batchSize"
                >
                    <s-input-number
                        min="{{1}}"
                        max="{{batchSizeMax}}"
                        width="160"
                        stepStrictly
                        value="{= formData.batchSize =}"
                    />
                        个
                    <p slot="help">
                        任务分批执行时，每个批次执行的分区个数，当前涉及分区总数为
                        <span class="yellow-text">{{formData.totalPartitionNum}}</span>
                        个
                    </p>
                </s-form-item>
                <s-form-item label="流量限制：" required class="form-item-center">
                    <s-radio-group
                        datasource="{{radioGroup.throttleSource}}"
                        value="{= isThrottle =}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item
                    s-if="isThrottle"
                    label="流量限额："
                    class="inline-form-item"
                    prop="interBrokerThrottle"
                >
                    <s-input-number
                        min="{{minThrottle}}"
                        max="{{10000000}}"
                        width="160"
                        stepStrictly
                        value="{= formData.interBrokerThrottle =}"
                    />
                        MB/s
                    <p slot="help">节点数据迁移流量限制，最小限制为
                        <span class="yellow-text">{{formData.minThrottle}}</span>
                        MB/s
                    </p>
                </s-form-item>
                <s-form-item
                    prop="duration"
                    s-if="isThrottle"
                    label="预计耗时："
                    class="form-item-center"
                >
                    <span class="yellow-text">{{formData.duration | formateDuration}}</span>
                </s-form-item>
                <s-form-item label="执行时间：" required class="form-item-center">
                    <s-radio-group
                        datasource="{{radioGroup.timeSource}}"
                        value="{= isNow =}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item s-if="!isNow" label="执行时间：" class="inline-form-item" prop="startTime">
                    <s-date-picker width="160" value="{= formData.startTime =}" range="{{range}}" mode="second" />
                    <p slot="help">可选择未来 24 小时之内的时间</p>
                </s-form-item>
            </s-form>
            <div class="${klass}__bottom">
                <s-button on-click="onClose">取消</s-button>
                <s-button
                    skin="primary"
                    class="ml12"
                    on-click="onConfirm">
                    确定
                </s-button>
            </div>
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-option': Select.Option,
        's-date-picker': DatePicker,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-input-number': InputNumber,
        's-button': Button,
        's-transfer': Transfer,
        's-table': Table,
        'tip-cmpt': Tip,
        's-icon-refresh': OutlinedRefresh,
    };

    static filters = {
        formateDuration(value: number) {
            const duration = m.duration(value, 'seconds');
            return `${duration.days()} 天 ${duration.hours()} 时 ${duration.minutes()} 分 ${duration.seconds()} 秒`;
        },
    };

    static computed = {
        minThrottle(): number {
            // 获取表单数据中的最小节流值
            const throttle = this.data.get('formData.minThrottle');
            // 返回取整后的最小节流值（向上取整）
            return Math.ceil(throttle) || 1;
        },
        batchSizeMax(): number {
            const totalPartitionNum = this.data.get('formData.totalPartitionNum') || 1;
            return totalPartitionNum > 500 ? 500 : totalPartitionNum;
        }
    };

    initData() {
        return {
            open: true,
            formData: {
                startTime: null,
                brokerIds: [],
                topics: [],
                interBrokerThrottle: null,
                batchSize: 1,
                retentionBrokers: '',
                decreaseBrokers: '',
                reassignTopics: [],
                totalPartitionNum: 1,
                minThrottle: 1
            },
            isNow: true,
            isBatch: true,
            isThrottle: true,
            radioGroup: {
                batchSource: [
                    {label: '指定批次大小', value: true},
                    {label: '否', value: false}
                ],
                throttleSource: [
                    {label: '指定限制流量', value: true},
                    {label: '不限制', value: false}
                ],
                timeSource: [
                    {label: '立即执行', value: true},
                    {label: '自定义时间', value: false}
                ],
            },
            columns: [
                {name: 'topicName', label: '主题名称', width: 210},
                {
                    name: 'partitionNum',
                    label: '分区数',
                    width: 110
                },
                {
                    name: 'replicationFactor',
                    label: '副本数',
                    width: 110
                },
            ],
            getContainer: document.getElementById('main'),
            brokerTitles: ['保留节点', '待删节点'],
            rules: {
                ...TOPIC_RESPLIT_VALIDATE
            },
        };
    }

    inited() {
        const current = new Date().getTime() + 3 * 60 * 1000;
        const nextDay = current + 24 * 60 * 60 * 1000;
        const range = {
            begin: new Date(current),
            end: new Date(nextDay)
        };
        this.data.set('range', range);
    }

    async attached() {
        const {dialogData} = this.data.get('');
        if (dialogData) {
            this.data.merge('formData', dialogData);
            this.data.set('isBatch', Boolean(dialogData.isBatch));
            this.data.set('isThrottle', Boolean(dialogData.isThrottle));
            this.data.set('isNow', Boolean(dialogData.isNow));
        }
        else {
            await this.getPlan();
        }
        this.watch('formData.batchSize', this.getParamters);
        this.watch('formData.interBrokerThrottle', this.getParamters);
    }

    async getPlan() {
        const {numberOfBrokerNodesPerZone, clusterId} = this.data.get('');
        await api.getDecreasePlan(clusterId, {numberOfBrokerNodesPerZone})
            .then((res) => {
                const {
                    retentionBrokerIds,
                    decreaseBrokerIds,
                    reassignTopics,
                    totalPartitionNum,
                    minThrottle,
                    proposedAssignments,
                    currentAssignments,
                    duration
                } = res;
                this.data.merge('formData', {
                    retentionBrokers: retentionBrokerIds.join(', '),
                    decreaseBrokers: decreaseBrokerIds.join(', '),
                    reassignTopics,
                    totalPartitionNum,
                    minThrottle: minThrottle.toFixed(4),
                    interBrokerThrottle: Math.ceil(minThrottle) || 1,
                    currentAssignments,
                    proposedAssignments,
                    duration
                });
            });
    }

    async getParamters() {
        const {formData, clusterId} = this.data.get('');
        const {currentAssignments, proposedAssignments, batchSize, interBrokerThrottle} = formData;
        await api.getParameters(clusterId, {
            currentAssignments,
            proposedAssignments,
            batchSize,
            interBrokerThrottle
        }).then((res) => {
            this.data.merge('formData', {
                minThrottle: res.minThrottle.toFixed(4),
                duration: res.duration
            });
        });
    }

    // 确认
    async onConfirm() {
        try {
            await (this.ref('form') as unknown as Form).validateFields();
            const {formData, isBatch, isNow, isThrottle} = this.data.get('');
            const {currentAssignments, proposedAssignments} = this.data.get('formData');
            let decreaseData = {
                currentAssignments,
                proposedAssignments,
                batchSize: isBatch ? formData.batchSize : null,
                interBrokerThrottle: isThrottle ? formData.interBrokerThrottle : null
            };
            if (!isNow) {
                decreaseData = {
                    ...decreaseData,
                    startTime: formData.startTime.getTime()
                };
            }
            const detailData = {
                ...formData,
                isBatch,
                isNow,
                isThrottle
            };
            this.fire('success', {decreaseData, detailData});
            this.onClose();
        }
        catch (e) {
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
