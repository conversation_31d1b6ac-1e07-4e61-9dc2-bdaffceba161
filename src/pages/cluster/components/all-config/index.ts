/* eslint-disable max-len */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Loading} from '@baidu/sui';
import {DiskType} from '@baidu/bce-bcc-sdk-enum';
import {AppLegend, AppDetailCell} from '@baidu/sui-biz';
import api from '@/common/client';
import {Payments} from '@/common/enums/index';
import {ClusterDetail} from '../../detail/index';
import './index.less';

const klass = 'all-config';

const TEXT_MAP = {
    name: '集群名称：',
    payment: '付费方式：',
    publicIpEnabled: '公网开关：',
    publicIpBandwidth: '公网带宽：',
    nodeSpec: '节点类型：',
    numberOfBrokerNodes: '节点总数：',
    numberOfBrokerNodesPerZone: '单可用区节点数：'
};

const TEXT_MAP_2 = {
    vipTime: '购买时长：',
    autoRenew: '自动续费：',
    autoRenewTime: '自动续费周期：',
};

const TEXT_MAP_3 = {
    storageType: '磁盘类型：',
    storageSize: '节点磁盘容量：',
    numberOfDisk: '总磁盘容量：',
};

const timeUnitMap = {
    month: '个月',
    year: '年'
};
export default class PreConfig extends Component {
    static template = html`
    <div class="${klass} bms-form-panel">
        <div class="${klass}__header">
            <div class="${klass}__header_left">
                <s-append noHighlight label="消息服务（KAFKA）" />
            </div>
            <div class="${klass}__header_right">
                <span>配置费用：</span>
                <span
                    class="price"
                >
                    {{clusterPrice}}/{{priceUnit}}
                </span>
                <span class="mr10">网络费用：</span>
                <span
                    class="price"
                >
                    {{publicIpPrice}}/{{priceUnit}}
                </span>
            </div>
        </div>
        <div class="${klass}_content">
            <s-loading loading="{{loading}}" style="width: 100%;background: transparent">
                <s-detail-cell datasource="{{datasource}}" divide="3">
                    <span slot="c-payment">预付费</span>
                    <span slot="c-publicIpEnabled">{{item.value ? '开启' : '关闭'}}</span>
                    <span slot="c-publicIpBandwidth">{{item.value || 0}}Mbps</span>
                    <span slot="c-numberOfDisk">{{computeTotal}}GB</span>
                    <span slot="c-storageSize">{{computeStorageSize}}</span>
                    <span slot="c-storageType">{{item.value | formateStorageType}}</span>
                </s-detail-cell>
            </s-loading>
        </div>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-detail-cell': AppDetailCell,
        's-loading': Loading
    };

    static filters = {
        paymentFilter: (value: string) => {
            return Payments.getTextFromValue(value);
        },
        formateStorageType: (value: string) => {
            return DiskType.getTextFromAlias(value);
        },
    };

    static computed = {
        priceUnit(): string {
            const confirmData = this.data.get('confirmData');
            return confirmData?.timeLength + timeUnitMap[confirmData?.timeUnit];
        },
        computeTotal(): number {
            const detail = this.data.get('detail');
            const {storageSize = 0, numberOfDisk = 1} = detail?.storageMeta || {};
            const {numberOfBrokerNodes = 1} = detail || {};
            return numberOfDisk * storageSize * numberOfBrokerNodes;
        },
        computeStorageSize(): string {
            const detail = this.data.get('detail');
            const {storageSize = 0, numberOfDisk = 1} = detail?.storageMeta || {};
            return numberOfDisk * storageSize + 'GB（' + storageSize + 'GB ×' + numberOfDisk + '）';
        }
    };

    initData() {
        return {
            datasource: [],
            loading: true,
            DiskType: DiskType
        };
    }

    attached() {
        this.watch('current', () => {
            if (this.data.get('confirmData')) {
                this.getClusterDetail();
            }
        });
    }

    getClusterDetail(): Promise<void> {
        return api.getClusterDetail(this.data.get('clusterId'), {})
            .then((detail: ClusterDetail) => {
                this.data.set('detail', detail);
                const confirmData = this.data.get('confirmData');
                let datasource = [];
                const {storageMeta} = detail;

                if (confirmData) {
                    const {timeUnit, timeLength, autoRenew} = confirmData;
                    const vipTime = timeLength + timeUnitMap[timeUnit];
                    const autoRenewSwitch = autoRenew?.renew ? '开启' : '关闭';
                    const autoRenewTime = autoRenew?.renew ? autoRenew?.renewTime + timeUnitMap[autoRenew?.renewTimeUnit] : '无';

                    datasource.push({label: TEXT_MAP_2.vipTime, value: vipTime, slot: 'vipTime'});
                    datasource.push({label: TEXT_MAP_2.autoRenew, value: autoRenewSwitch, slot: 'autoRenew'});
                    autoRenew?.renew && datasource.push({label: TEXT_MAP_2.autoRenewTime, value: autoRenewTime, slot: 'autoRenewTime'});
                }

                const datasource2 = Object.keys(TEXT_MAP).map((key: string) => ({
                    label: TEXT_MAP[key],
                    value: detail[key],
                    slot: key,
                }));

                const datasource3 = Object.keys(TEXT_MAP_3).map((key: string) => ({
                    label: TEXT_MAP_3[key],
                    value: storageMeta[key],
                    slot: key,
                }));

                this.data.set('datasource', [...datasource, ...datasource2, ...datasource3]);

                this.data.set('loading', false);

                return Promise.resolve();
            });
    }
}
