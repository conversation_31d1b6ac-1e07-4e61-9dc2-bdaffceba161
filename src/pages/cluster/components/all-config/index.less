.all-config {
    &__header {
        height: 40px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid var(--borderColor2);

        &_left {
            flex: 1;
        }

        &_right {
            font-weight: 500;

            &_item {
                margin-left: 30px;
                font-size: 13px;
            }

            .price {
                color: var(--priceColor);
            }
        }
    }

    &_content {
        margin-top: 16px;
        min-width: 100px;
        .s-detail-cell{
            .detail-cell{
                label{
                    width:96px ;
                }
            }
        }
    }

    .price-text {
        color: var(--priceColor);
        font-weight: 500;
    }
}
