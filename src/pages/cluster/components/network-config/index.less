/**
 * 网络配置
 *
 * @file index.less
 * <AUTHOR>
 */

.network-type {

    &__wrap {
        margin: 24px 0 0;
        padding-right: 20px;
        background-color: var(--bgColor);
        border-radius: 4px;

        &_panel {
            padding: 10px 0 0;
            font-size: 0;

            .s-input {
                background-color: var(--whiteColor);
            }

            &_label {
                display: inline-block;
                vertical-align: middle;
                width: 130px;
                padding-left: 20px;
                font-size: 12px;
            }

            &:last-child {
                padding-bottom: 10px;
            }
        }
    }

    &__select {

        .s-select-dropdown-empty {
            height: 86px;
            padding-top: 46px;
        }
    }

    .errTip {
        padding: 10px 0 10px 20px
    }
}

.eip-table {
    width: 100%;
    overflow-y: auto;
    display: inline-block;
}

.bms-cluster-create-network {

    .bank {
        margin-left: -1px;
    }

    .yellow-text {
        color: #FFAB52;
    }

    .form-item-eips .s-form-item-control-wrapper {
        flex: 1
    }
}

.release-tag {
    padding: 0 8px;
    border: 1px solid #FF9326;
    border-radius: 2px;
    color: #FF9326;
}
