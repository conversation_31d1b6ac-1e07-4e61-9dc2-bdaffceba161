/* eslint-disable max-len */
/**
 * 网络配置模块
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Select, InputNumber, Switch, Form, Radio, Slider, Alert, Table, Button} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';
import Tip from '@/components/tip';
import {INPUT_WIDTH, SELECT_HEIGHT, CELL, ZoneMutilMax} from '@/common/config';
import api from '@/common/client';
import {VAILDITE_ITEMS} from '@/common/rules';
import {getItemInfoInArr, renderZoneLabel, formatEmpty} from '@/common/util';
import {ClusterRefType, Deployment} from '@/common/enums/constant';
import {routeTypeEnum, AllEnum} from '@/common/enums';
import {ZoneSingleMax} from '../node-config/conf';
import {renderSwitch} from '@/common/html';
import {debounce} from '@/common/decorator';
import {UtilHandler} from '../../util/util';
import BaseCmpt from '../base-cmpt';
import {UpgrageType} from '../../util/conf';
import {OutlinedPlus} from '@baidu/sui-icon';
import './index.less';

const klass = 'bms-cluster-create-network';

const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
const KafkaPublicTopicMonitor = $flag.KafkaPublicTopicMonitor;
const networkKlass = 'network-type';

const Networts = [{text: '按带宽付费', value: 'bank'}];
const allEnum = AllEnum.toArray();
interface SubnetId {
    subnetId: string;
    name: string;
    subnetType: string;
    zone: string;
    vpcId: string;
}

interface SecurityGroup {
    securityGroupId: string;
    name: string;
    vpcId: string;
}

interface Eip {
    /**
     * 带宽
     */
    bandwidthInMbps: number;
    /**
     * 创建时间
     */
    createTime: string;
    /**
     * IP地址
     */
    eip: string;
    /**
     * 唯一ID
     */
    eipId: string;
    /**
     * eip类型
     */
    eipInstanceType: string;
    /**
     * 过期时间
     */
    exipreTime: string;
    /**
     * 绑定的实例ID
     */
    instanceId: string;
    /**
     * 绑定的实例类别
     */
    instanceType: string;
    /**
     * eip名称
     */
    name: string;
    /**
     * 区域
     */
    region: string;
    /**
     * 状态
     */
    status: string;
    /**
     * 线路类型
     */
    routeType: string;
}

const TextMap = {
    title: '网络配置',
    vpc: '网络实例：',
    subnet: '可用区',
    securityGroup: '安全组：',
    publicIpEnabled: '公网访问：',
    publicIpWay: '公网方式：',
    network: '公网带宽：',
    intranetIpEnabled: '产品间转储：',
    eip: '选择实例：'
};

enum FieldsType {
    SecurityGroup = 'securityGroup',
    PublicIpEnabled = 'publicIpEnabled',
    BandWidth = 'publicIpBandWidth',
    IntranetIpEnabled = 'intranetIpEnabled'
}
const NetworkFrozenFields = [
    FieldsType.SecurityGroup,
    FieldsType.PublicIpEnabled,
    FieldsType.BandWidth,
    FieldsType.IntranetIpEnabled
];

const compareArr = (arr1: string[], arr2: string[]): boolean => {
    return _.isEqual(arr1, arr2);
};

class NetworkType extends Component {

    static template = html`
    <div class="${networkKlass}">
        <s-form
            s-ref="form"
            rules="{{rules}}"
            class="mb24"
            data="{{formData}}">
            <s-formitem label="${TextMap.vpc}" prop="vpc">
                <template s-if="{{!isDetail}}">
                    <s-select
                        class="${networkKlass}__select"
                        datasource="{{vpcTypes}}"
                        value="{= formData.vpc =}"
                        width="${INPUT_WIDTH}"
                        height="${SELECT_HEIGHT}"
                        noDataText="请前往VCP页面创建当前可用区下的子网"
                        on-change="onVpcChange"
                        on-visible-change="onGetVpcVisibleChange"
                    />
                    <a s-if="!${isXushang} && !isEdgeRegion" href="/network/" target="blank" class="inline-desc">创建VPC</a>
                    <a s-else-if="{{isEdgeRegion}}" href="/bec/#/bec/vpc/list" target="blank" class="inline-desc">创建VPC</a>
                    <div class="${networkKlass}__wrap" s-if="{{formData.vpc && selectZones.length}}">
                        <div class="${networkKlass}__wrap_panel"
                            s-for="item,index in selectZones">
                            <span class="${networkKlass}__wrap_panel_label">
                                {{item | renderZoneLabel}}：
                            </span>
                            <s-select
                                datasource="{{zoneSources[index]}}"
                                value="{= zoneList[index] =}"
                                height="${SELECT_HEIGHT}"
                                width="${INPUT_WIDTH}"
                                on-change="onSubnetChange"
                                on-visible-change="onSubnetVisibleChange"
                            />
                        </div>
                        <div class="err errTip" s-if="{{errTip}}">{{errTip}}</span></div>
                </template>
                <span class="upgrage-text" s-else>
                    {{detail.vpc.name}}
                </span>
            </s-formitem>
            <s-formitem
                s-if="{{formData.vpc}}"
                label="${TextMap.securityGroup}"
                class="mb24"
                prop="securityGroup">
                <template>
                    <s-select
                        datasource="{{computedSecurityGroups}}"
                        disabled="{{securityGroupDisabled}}"
                        multiple
                        labelFilter={{labelFilter}}
                        value="{{formData.securityGroup}}"
                        on-change="onSecurityGroupChange"
                        width="${INPUT_WIDTH}"
                        height="${SELECT_HEIGHT}"
                    />
                    <a
                        href="/network/#/vpc/security/create"
                        target="blank"
                        s-if="!${isXushang} && !isEdgeRegion"
                        class="inline-desc">
                        创建安全组
                    </a>
                    <a
                        href="/bec/#/bec/security-group/list"
                        target="blank"
                        s-if="{{isEdgeRegion}}"
                        class="inline-desc">
                        创建安全组
                    </a>
                </template>
            </s-formitem>
        </s-form>
    </div>`;

    static components = {
        's-select': Select,
        's-form': Form,
        's-formitem': Form.Item,
        's-input-number': InputNumber
    };

    initData() {
        return {
            selectZones: [],
            securityGroups: [],
            zoneSources: [],
            zoneList: [],
            rules: {
                vpc: [VAILDITE_ITEMS.requiredSelect],
                securityGroup: [VAILDITE_ITEMS.requiredSelect]
            },
            labelFilter(labels: string[]) {
                return labels.join(',');
            },
            isEdgeRegion: false,
        };
    }

    static filters = {
        renderZoneLabel,
        formatVpc: UtilHandler.vpc
    };

    static computed = {
        computedSecurityGroups() {
            const securityGroups = this.data.get('securityGroups');
            const checkedSecurityGroups = this.data.get('formData.securityGroup') || [];

            if (checkedSecurityGroups.length >= 1) {
                return securityGroups.map(item => ({
                    ...item,
                    disabled: checkedSecurityGroups.indexOf(item.value) === -1
                }));
            }
            else {
                return securityGroups;
            }
        },
        securityGroupDisabled(): boolean {
            const isUpgrading = this.data.get('isUpgrading');
            const disabledByModified = this.data.get('disabledByModified');
            return !isUpgrading || disabledByModified[FieldsType.SecurityGroup];
        }
    };

    async attached() {
        this.getNetwort();
        if (this.data.get('isDetail')) {
            this.watch('detail.securityGroups', () => {
                const securityGroups = this.data.get('detail.securityGroups');
                const vpc = this.data.get('detail.vpc');
                this.data.merge('formData', {
                    vpc: vpc.vpcId || '',
                    securityGroup: securityGroups?.map(item => item.securityGroupId) || [],
                });
                this.data.set('securityGroups', securityGroups.map(item => ({
                    text: item.name,
                    value: item.securityGroupId
                })));
                this.getSecurityGroup();
            });
        }
    }


    // 根据部署方式，进行设置内部部署方式
    handleDeployTypeChange(deployType: Deployment) {
        this.data.set('deployType', deployType);
        if (deployType === Deployment.AVAILABILITY) {
            this.data.set('zoneList', new Array(ZoneMutilMax).fill(''));
            this.data.set('zoneSources', new Array(ZoneMutilMax).fill([]));
        }
        else {
            this.data.set('zoneList', new Array(ZoneSingleMax).fill(''));
            this.data.set('zoneSources', new Array(ZoneSingleMax).fill([]));
        }
        this.getVpcSubnet();
    }

    handleZonesChange(zones: string[]) {
        const {deployType} = this.data.get('');
        if ((zones.length === ZoneSingleMax && deployType === Deployment.PERFROMANCE)
            || (_.includes([2, 3], zones.length) && deployType === Deployment.AVAILABILITY)) {
            this.data.set('selectZones', []);
            setTimeout(() => {
                this.data.set('selectZones', zones);
                this.data.set('zoneList', new Array(zones.length).fill(''));
                this.data.set('zoneSources', new Array(length).fill([]));
                this.getVpcSubnet();
            }, 0);
        }
        else {
            this.data.set('selectZones', []);
        }
        this.data.set('errTip', '');
    }

    // 获取vpc
    getNetwort() {
        api.listVpc({}).then((target: {vpcs: Array<{vpcId: string, name: string}>}) => {
            const arr = _.map(target.vpcs, i => ({text: i.name, value: i.vpcId}));
            this.data.set('vpcTypes', arr);
        });
    }

    // vpc切换
    onVpcChange(target: {value: string}) {
        const selectZones = this.data.get('selectZones');
        this.data.merge('formData', {vpc: target.value, securityGroup: []});
        this.data.set('zoneSources', _.map(this.data.get('zoneSources'), item => []));
        this.data.set('zoneList', new Array(selectZones.length));
        this.data.set('list', []);
        this.data.set('securityGroups', []);
        this.getVpcSubnet();
        this.getSecurityGroup();
    }

    // vpc下拉框下拉时，请求刷新下拉vpc列表
    onGetVpcVisibleChange(target: {value: string}) {
        target.value && this.getNetwort();
    }

    // 安全组下拉时，请求刷选安全组信息
    onGetSecurityGroup(target: {value: string}) {
        this.getSecurityGroup();
    }

    onSecurityGroupChange(target: {value: string[]}) {
        this.data.set('formData.securityGroup', target.value);
        let changeCheck = false;
        const {isDetail, formData, detail} = this.data.get('');
        if (isDetail && !compareArr(formData.securityGroup, detail.securityGroups.map(item => item.securityGroupId))) {
            changeCheck = true;
        }
        this.fire('security-group-change', {value: changeCheck});
    }

    // 获取vpc下面的子网
    getVpcSubnet() {
        const vpc = this.data.get('formData.vpc');
        const {deployType, selectZones} = this.data.get('');
        if (!vpc || (deployType === Deployment.AVAILABILITY && selectZones.length < 2)) {
            return;
        }
        api.listSubnet(vpc, {zones: selectZones})
            .then((target: {zoneSubnets: Array<{
                    zone: string;
                    subnets: SubnetId[];
                }>;}) => {
                const zoneSources = new Array(selectZones.length);
                _.each(selectZones, (item: string, index: number) => {
                    const subnet = _.find(target.zoneSubnets, z => z.zone === item);
                    zoneSources[index] = subnet
                        ? _.map(subnet.subnets, subnetItem => ({text: subnetItem.name, value: subnetItem.subnetId}))
                        : [];
                });
                this.data.set('zoneSources', zoneSources);
            });
    }

    // 子网选中
    onSubnetChange() {
        // 使用nextTick在子网选中后判定长度
        this.nextTick(() => {
            const deployType: string = this.data.get('deployType');
            const zoneList: string[] = this.data.get('zoneList');
            const selectZones = this.data.get('selectZones');
            if ((deployType === Deployment.PERFROMANCE && zoneList.length === ZoneSingleMax)
                || (deployType === Deployment.AVAILABILITY && zoneList.length === selectZones.length)) {
                this.verifyZonelist();
            }
        });
    }

    // 子网下拉框下拉时，请求刷新数据
    onSubnetVisibleChange(target: {value: boolean}) {
        target.value && this.getVpcSubnet();
    }

    // 获取安全组
    getSecurityGroup() {
        const vpc = this.data.get('formData.vpc');
        if (!vpc) {
            return;
        }
        api.listSecurityGroup(vpc, {}).then((target: {securityGroups: SecurityGroup[]}) => {
            this.data.set('securityGroups',
                _.map(target.securityGroups, i => ({text: i.name, value: i.securityGroupId})));
        });
    }

    verifyZonelist() {
        return new Promise<void>((resolve, reject) => {
            const zoneList: string[] = this.data.get('zoneList');
            const isEmpty = _.some(zoneList, i => !i);
            if (isEmpty) {
                this.data.set('errTip', `请选取${zoneList.length}个子网`);
                return reject();
            }
            const map = new Map();
            for (let item of zoneList) {
                if (map.get(item)) {
                    this.data.set('errTip', '子网不能重复');
                    return reject();
                }
                map.set(item, true);
            }
            this.data.set('errTip', '');
            return resolve();
        });
    }

    // 校验
    verify() {
        if (this.data.get('isDetail')) {
            return this.data.get('formData.securityGroup.length') > 0 ? Promise.resolve() : Promise.reject();
        }
        return Promise.all([
            (this.ref('form') as unknown as Form).validateFields(),
            this.verifyZonelist()
        ]).catch((err: Error) => {
            throw (ClusterRefType.NETWORK);
        });
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData() {
        const {
            formData,
            vpcTypes,
            zoneSources,
            selectZones,
            zoneList,
            isDetail,
            securityGroups
        } = this.data.get('');

        const {vpc, securityGroup} = formData;

        if (isDetail) {
            return [
                {
                    label: TextMap.vpc,
                    text: getItemInfoInArr(vpcTypes, vpc),
                },
                {
                    label: TextMap.securityGroup,
                    text: UtilHandler.securityGroup(securityGroup, securityGroups)
                },
            ];
        }

        return [
            {
                label: TextMap.vpc,
                text: getItemInfoInArr(vpcTypes, vpc),
                hasAddition: true,
                addition: _.map(zoneList, (z, index) => {
                    let text = formatEmpty();
                    _.each(zoneSources, item => {
                        const index = _.findIndex(item, (i: {value: string}) => i.value === z);
                        if (index > -1) {
                            text = item[index].text;
                        }
                    });
                    return {
                        label: `${renderZoneLabel(selectZones[index])}：`,
                        text: text
                    };
                })
            },
            {
                label: TextMap.securityGroup,
                text: UtilHandler.securityGroup(securityGroup, securityGroups)
            },
        ];
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {
        const {zoneList, formData} = this.data.get('');
        return {
            vpc: formData?.vpc,
            subnets: zoneList,
            securityGroups: formData?.securityGroup
        };
    }

    getUpgradeData() {
        const {securityGroup} = this.data.get('formData');
        return {
            // type: UpgrageType.UPDATE_CLUSTER_SECURITY_GROUP,
            securityGroups: securityGroup
        };
    }
}

export default class extends BaseCmpt {
    static template = html`
    <div class="${klass} bms-form-panel">
        <s-append noHighlight label="${TextMap.title}" />
        <s-alert skin="info" class="mb16">
            依据访问协议设置安全组出、入站的 TCP 协议端口以及 IP 范围
        </s-alert>
        <s-form
            s-ref="form"
            rules="{{rules}}"
            data="{{formData}}">
            <network-type
                s-ref="network"
                isUpgrading="{{isUpgrading}}"
                isDetail="{{isDetail}}"
                detail="{{detail}}"
                regions="{{regions}}"
                isEdgeRegion="{{isEdgeRegion}}"
                disabledByModified="{{disabledByModified}}"
                on-security-group-change="onSecurityGroupChange"
            />
            <s-formitem class="switch-with-tip">
                <span slot="label">
                    ${TextMap.publicIpEnabled}
                    <tip-cmpt s-if="isEdgeRegion" placement="right" type="sign">由于边缘节点产品 BCE 目前不支持公网访问，因此暂时关闭此功能</tip-cmpt>
                </span>
                <s-switch
                    checked="{= formData.publicIpEnabled =}"
                    on-change="onPublicIpEnabledChange"
                    disabled="{{publicIpEnableDisabled}}"
                />
                <p class="desc mt4" s-if="formData.publicIpEnabled">
                    通过公网访问时，认证方式可以选择 SSL、SASL/PLAIN、SASL/SCRAM，<span class="yellow-text">推荐使用 SASL/SCRAM</span>
                </p>
                <s-alert
                    skin="warning"
                    showIcon="{{ture}}"
                    class="mt8"
                    s-else-if="{{isDetail && !formData.publicIpEnabled && originalData.publicIpEnabled && originalData.publicIpMode === 'MANUAL_SELECT'}}"
                >
                    关闭公网访问，我们会将EIP实例与节点解绑，EIP实例不会被删除，详情请查看<a href="/eip/#/eip/instance/list">EIP实例列表</a>
                </s-alert>
            </s-formitem>
            <s-formitem s-if="{{formData.publicIpEnabled}}" label="${TextMap.publicIpWay}" class="radio-with-tip">
                <s-radio-group
                    radioType="button"
                    datasource="{{publicIpWays}}"
                    value="{{formData.publicIpMode}}"
                    on-change="onPublicIpModeChange"
                    disabled="{{isDetail && originalData.publicIpEnabled}}"
                />
                <p class="desc yellow-text mt4" s-if="{{isDetail}}">目前不支持公网方式的动态切换，如您需更换公网方式，请关闭公网访问并保存后，再次发起变更开启公网访问重新配置</p>
                <p class="desc yellow-text mt4" s-else-if="formData.publicIpMode === 'MANUAL_SELECT'">
                    「手动选择」由用户自行创建按流量计费的EIP实例，Kafka仅提供EIP绑定服务不计费
                </p>
                <p class="desc yellow-text mt4" s-else-if="formData.publicIpMode === 'AUTO_ASSIGN'">
                    「自动分配」由Kafka为用户创建和管理EIP实例，按带宽或用量收取费用
                </p>
            </s-formitem>
            <s-formitem
                label="${TextMap.eip}"
                s-if="{{showEipsTable}}"
                prop="eips"
                class="form-item-eips"
            >
                <s-alert skin="warning" showIcon="{{ture}}" class="mb16" s-if="showRelaseEipTip">
                    节点缩容后，多出的EIP实例将自动解绑，解绑后的EIP实例不会被释放，将持续产生费用，如需释放请参考
                    <a
                        href="/eip/#/eip/instance/list"
                        target="_blank"
                        class="inline-desc"
                    >
                        解绑EIP实例
                    </a>
                </s-alert>
                <s-alert s-else skin="info" showIcon="{{ture}}" class="mb16">
                    <p>1. 每个多选EIP实例的地域、带宽、线路类型需一样，且选择数量与节点数对应；</p>
                    <p>2. EIP实例创建后，不建议自行解绑，这会造成EIP无法访问，如有强需求请参照
                        <a href="/eip/#/eip/instance/list" target="_blank" class="inline-desc">解绑EIP实例</a>
                    </p>
                </s-alert>
                <p class="mb16" style="display: flex; align-items: middle">
                    <s-button skin="stringfy" on-click="onCreateEip">
                        <outlined-plus class="create-icon" />
                        前往创建EIP实例
                    </s-button>
                    <span style="color: #84868c">已选择{{selection.selectedIndex.length}}个，需选择{{singleNumberTextNumber}}个</span>
                </p>
                <s-table
                    columns="{{columns}}"
                    s-ref="table"
                    datasource="{{eipsource}}"
                    selection="{{selection}}"
                    on-selected-change="onSelectChange"
                    on-filter="onFilter"
                    maxHeight="200"
                    class="eip-table"
                >
                    <div slot="empty">
                        <s-empty vertical emptyText="暂无可用EIP实例，请前往创建。">
                            <s-button skin="stringfy" slot="action" on-click="onCreateEip">
                                <outlined-plus class="create-icon" />
                                创建EIP实例
                            </s-button>
                        </s-empty>
                    </div>
                    <div slot="c-name">
                        {{row.name}}
                        <span class="release-tag ml16" s-if="row.toRelease">即将释放</span>
                    </div>
                </s-table>
                <div class="err errTip" s-if="{{errTipForEips}}">{{errTipForEips}}</span></div>
            </s-formitem>
            <s-formitem
                label="${TextMap.network}"
                s-if="{{formData.publicIpEnabled && formData.publicIpMode === 'AUTO_ASSIGN'}}">
                <template s-if="{{!isDetail}}">
                    <s-slider
                        showTooltip
                        showInput="{{true}}"
                        step="{{1}}"
                        min="{{1}}"
                        max="{{500}}"
                        marks="{{marks}}"
                        parts="{{4}}"
                        value="{=formData.publicIpBandwidth=}"
                    />
                    <span class="ml5">${CELL.Mbps}</span>
                </template>
                <template s-else-if="{{isDetail && formData.publicIpEnabled}}">
                    <div class="mt24">
                        <s-slider
                            showTooltip
                            showInput="{{true}}"
                            step="{{1}}"
                            min="{{1}}"
                            max="{{500}}"
                            marks="{{marks}}"
                            parts="{{4}}"
                            disabled="{{publicIpBandWidthDisabled}}"
                            value="{=formData.publicIpBandwidth=}"
                        />
                        <span class="ml5">${CELL.Mbps}</span>
                    </div>
                </template>
                <span class="upgrage-text" s-else>
                    {{formData.publicIpBandwidth | formatPublicIpBandwidth}}
                </span>
                <p class="desc mt24">公网按您实际选择的带宽计费</p>
            </s-formitem>
            <s-formitem
                label="${TextMap.intranetIpEnabled}"
                class="switch-with-tip"
            >
                <s-switch
                    checked="{= formData.intranetIpEnabled =}"
                    on-change="onPrivateIpEnabledChange"
                    disabled="{{intranetIpDisabled}}"
                />
                <p class="desc mt4">当<span class="yellow-text">部分产品</span>转储KAFKA时开启，提供网络打通方案，如流日志、消息中心、CFC、DTS等，详情见产品协同</p>
            </s-formitem>
        </s-form>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-select': Select,
        's-form': Form,
        's-formitem': Form.Item,
        's-inputnumber': InputNumber,
        's-switch': Switch,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        'network-type': NetworkType,
        'tip-cmpt': Tip,
        's-slider': Slider,
        's-alert': Alert,
        's-table': Table,
        's-button': Button,
        'outlined-plus': OutlinedPlus,
        's-empty': Empty
    };

    initData() {
        return {
            networks: Networts,
            securityGroup: [],
            detail: {},
            // 集群变更下，禁用使用
            disabledByModified: {},
            formData: {
                publicIpEnabled: false,
                network: Networts[0].value,
                publicIpBandwidth: 0,
                intranetIpEnabled: false,
                publicIpMode: 'AUTO_ASSIGN',
                eips: []
            },
            marks: {
                1: '1Mbps',
                250: '250Mbps',
                500: '500Mbps'
            },
            publicIpWays: [
                {label: '自动分配', value: 'AUTO_ASSIGN'}
            ],
            KafkaPublicTopicMonitor: KafkaPublicTopicMonitor,
            columns: [
                {name: 'name', label: '实例名称', width: 240},
                {name: 'eip', label: 'IP地址', width: 260},
                {name: 'bandwidthInMbps', label: '带宽（Mbps）', width: 160},
                {
                    name: 'routeType',
                    label: '线路类型',
                    width: 160,
                    filter: {
                        options: [
                            ...allEnum,
                            ...routeTypeEnum.toArray()
                        ],
                        value: allEnum[0].value
                    },
                },
            ],
            eipsource: [],
            selection: {
                disabledSelectAll: true,
                mode: 'multi',
                selectedIndex: [],
                disabledIndex: []
            }
        };
    }

    static computed = {
        publicIpEnableDisabled(): boolean {
            const isUpgrading = this.data.get('isUpgrading');
            const disabledByModified = this.data.get('disabledByModified');
            const isEdgeRegion = this.data.get('isEdgeRegion');
            return $flag.KafkaPublicIpDisable || !isUpgrading || isEdgeRegion || disabledByModified[FieldsType.PublicIpEnabled];
        },
        publicIpBandWidthDisabled(): boolean {
            const isUpgrading = this.data.get('isUpgrading');
            const disabledByModified = this.data.get('disabledByModified');
            return !isUpgrading || disabledByModified[FieldsType.BandWidth];
        },
        showEipsTable(): boolean {
            const publicIpEnabled = this.data.get('formData.publicIpEnabled');
            const publicIpMode = this.data.get('formData.publicIpMode');
            return publicIpEnabled && publicIpMode === 'MANUAL_SELECT';
        },
        intranetIpDisabled(): boolean {
            const isUpgrading = this.data.get('isUpgrading');
            const disabledByModified = this.data.get('disabledByModified');
            return !isUpgrading || disabledByModified[FieldsType.IntranetIpEnabled];
        }
    };

    static filters: SanFilterProps = {
        formatPublicIpEnabled: UtilHandler.publicIpEnabled,
        formatPublicIpBandwidth: UtilHandler.publicIpBandwidth,
        formatIntranetIpEnabled: UtilHandler.intranetIpEnabled,
    };

    inited() {
        this.fire('publicIp-change', this.data.get('formData.publicIpEnabled'));
    }

    async attached() {
        // slider组件原生on-change事件，在滑块直接点击而不是拖动改变值的时候没有触发，用监听值的方式代替
        const {isDetail} = this.data.get('');
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        await api.getUserAcls({featureTypes: ['ClusterManualSelectEipWhiteList']}).then((target: {isExist: boolean}) => {
            this.data.set('ClusterManualSelectEipWhiteList', target.isExist);
            if (target.isExist) {
                this.data.push('publicIpWays',
                    {label: '手动选择', value: 'MANUAL_SELECT'}
                );
            }
        });
        this.data.set('isEdgeRegion', isEdgeRegion);
        if (!isDetail && !isEdgeRegion) {
            this.getEips({});
        }
        this.watch('formData.publicIpBandwidth', () => this.onBandWidthChange());
        this.watch('singleNumberTextNumber', value => {
            const showEipsTable = this.data.get('showEipsTable');
            // showEipsTable && this.resetEipsource(value);
            showEipsTable && this.handleEipSourceByNumberOfNodes(value);
        });
    }

    /**
     * 根据单区节点数变化设置eip数据
     * @param {number} value 单区节点数值
     */
    handleEipSourceByNumberOfNodes(value: number) {
        const {isDetail, detail, eipsource, clusterId, selection, originalData} = this.data.get('');
        const selectedIndex = selection.selectedIndex;
        const {region, routeType, bandwidthInMbps} = eipsource[selectedIndex[0]] || {};
        const {numberOfBrokerNodesPerZone, logicalZones} = detail || {};
        this.data.set('showRelaseEipTip', false);
        const selectedLen = selectedIndex.length;
        const originNumberOfBrokerNodes = numberOfBrokerNodesPerZone * logicalZones?.length;
        let originSelectedEipIndex: number[] = [];
        let regionRouteTypeBandwidthInMbpsDiffIndex: number[] = [];
        let notSelectedEipIndex: number[] = [];
        eipsource.forEach((item, index) => {
            if (_.findIndex((originalData?.eips || []), eip => eip.name === item.name) !== -1) {
                originSelectedEipIndex.push(index);
            }
            else if (item.region !== region || item.routeType !== routeType || item.bandwidthInMbps !== bandwidthInMbps) {
                regionRouteTypeBandwidthInMbpsDiffIndex.push(index);
            }
            else if (!selectedIndex.includes(index)) {
                notSelectedEipIndex.push(index);
            }
        });
        // 置灰已绑定的eip
        let disabledIndex = [...originSelectedEipIndex];

        if (isDetail) {
            // 升配时，以下情况置灰：
            // 1.置灰已绑定的eip
            // 2.与第1项EIP的地域、带宽、线路类型不同的eip
            if (value > originNumberOfBrokerNodes) {
                disabledIndex = disabledIndex.concat([...regionRouteTypeBandwidthInMbpsDiffIndex]);
            }
            else if (value < originNumberOfBrokerNodes) {
                // 标志待解绑实例
                let decreaseNodeIds = [];

                api.getDecreaseBrokerCountPlan(clusterId, {numberOfBrokerNodesPerZone: value / logicalZones?.length}).then(target => {
                    decreaseNodeIds = target.decreaseNodeIds;
                    this.data.set('decreaseNodeIds', decreaseNodeIds);
                    eipsource.forEach((item, index) => {
                        if (_.includes(decreaseNodeIds, item.instanceId) && item.status === 'binded' && item.instanceType === 'BCC') {
                            this.data.set(`eipsource[${index}].toRelease`, true);
                        }
                        else {
                            this.data.set(`eipsource[${index}].toRelease`, false);
                        }
                    });
                });
                this.data.set('showRelaseEipTip', true);
            }

            // 当节点数 <= 已选eip数量时，需要再置灰未选eip
            if (value <= selectedLen) {
                disabledIndex = disabledIndex.concat(notSelectedEipIndex);
            }
        }
        else {
            if (selectedLen) {
                // eip有已选项：地域、带宽、线路类型与已选第1项不同的eip置灰
                disabledIndex = [...regionRouteTypeBandwidthInMbpsDiffIndex];
            }
            // 如果节点数 <= 已选eip数量，置灰未选eip
            if (value <= selectedLen) {
                disabledIndex = disabledIndex.concat(notSelectedEipIndex);
            }
        }

        this.data.set('selection.disabledIndex', _.unique(disabledIndex));
    }

    resetEipsource(value) {
        const {isDetail, detail, eipsource, clusterId, selection, originalData} = this.data.get('');
        const selectedIndex = selection.selectedIndex;
        const {region, routeType, bandwidthInMbps} = eipsource[selectedIndex[0]] || {};
        const {numberOfBrokerNodesPerZone, logicalZones} = detail || {};
        this.data.set('showRelaseEipTip', false);
        if (isDetail && value >= numberOfBrokerNodesPerZone * logicalZones?.length) {
            const disabledIndex = [];
            eipsource.forEach((item, index) => {
                if (_.findIndex(originalData.eips, eip => eip.name === item.name) !== -1) {
                    disabledIndex.push(index);
                }
                else if (item.region !== region || item.routeType !== routeType || item.bandwidthInMbps !== bandwidthInMbps) {
                    disabledIndex.push(index);
                }
            });
            this.data.set('selection.disabledIndex', disabledIndex);
        }
        else if (isDetail && value < numberOfBrokerNodesPerZone * logicalZones?.length) {
            // 标志待解绑实例
            let decreaseNodeIds = [];

            api.getDecreaseBrokerCountPlan(clusterId, {numberOfBrokerNodesPerZone: value / logicalZones?.length}).then(target => {
                decreaseNodeIds = target.decreaseNodeIds;
                this.data.set('decreaseNodeIds', decreaseNodeIds);
                eipsource.forEach((item, index) => {
                    if (_.includes(decreaseNodeIds, item.instanceId) && item.status === 'binded' && item.instanceType === 'BCC') {
                        this.data.set(`eipsource[${index}].toRelease`, true);
                    }
                    else {
                        this.data.set(`eipsource[${index}].toRelease`, false);
                    }
                });
            });
            this.data.set('showRelaseEipTip', true);
            this.data.set('selection.disabledIndex', new Array(eipsource.length).fill(0).map((item, index) => index));
        }
        else if (isDetail) {
            this.data.set('selection.disabledIndex', new Array(eipsource.length).fill(0).map((item, index) => index));
        }
        else if (value !== selectedIndex.length && selectedIndex.length !== 0) {
            let disabledIndex = [];
            eipsource.forEach((item, index) => {
                if (item.region !== region || item.routeType !== routeType || item.bandwidthInMbps !== bandwidthInMbps) {
                    disabledIndex.push(index);
                }
            });
            this.data.set('selection.disabledIndex', disabledIndex);
        }
        else if (selectedIndex.length === 0) {
            this.data.set('selection.disabledIndex', []);
        }
    }

    onCreateEip() {
        window.open('https://console.bce.baidu.com/eip/#/eip/instance/list', '_blank');
    };

    onPublicIpModeChange(target: {value: string}) {
        this.data.set('formData.publicIpMode', target.value);
        this.nextTick(() => {
            if (target.value === 'MANUAL_SELECT') {
                this.data.set('formData.publicIpBandwidth', 0);
                this.data.set('formData.eips', this.ref('table')?.getSelectedItems());
            }
            this.onPriceConfigChange();
        });
    }

    getEips(param) {
        const {detail, isDetail} = this.data.get('');
        api.getEips(param).then((target: {eips: Eip[]}) => {
            const eipsource = target.eips;
            this.data.set('eipsource', eipsource);
            if (isDetail && param.clusterId && detail.publicIpMode === 'MANUAL_SELECT') {
                const selectedIndex = [];
                eipsource.forEach((item, index) => {
                    if (_.findIndex(detail.eips, eip => eip.name === item.name) !== -1) {
                        selectedIndex.push(index);
                    }
                });
                const selection = {
                    selectedIndex: selectedIndex,
                    disabledIndex: new Array(eipsource.length).fill(0).map((item, index) => index)
                };
                this.data.set('oldSelection', {...selection});
                this.data.merge('selection', {...selection});
            }
        });
    }

    onSelectChange(event: {value: { selectedIndex: number[], selectedItems: object[]}}) {
        this.data.set('selection.selectedIndex', event.value.selectedIndex);
        this.data.set('formData.eips', event.value.selectedItems);
        const {eipsource, singleNumberTextNumber, isDetail, oldSelection} = this.data.get('');
        if (event.value.selectedItems.length === 0) {
            this.data.set('selection.disabledIndex', []);
            return;
        }
        const detail = this.data.get('detail');
        const {
            region,
            routeType,
            bandwidthInMbps
        } = event.value.selectedItems[0];

        if (!isDetail || !detail.publicIpEnabled) {
            const disabledIndex = [];

            if (event.value.selectedIndex.length === singleNumberTextNumber) {
                eipsource.forEach((item, index) => {
                    if (!_.find(event.value.selectedItems, i => item.name === i.name)) {
                        disabledIndex.push(index);
                    }
                });
                this.data.set('selection.disabledIndex', disabledIndex);
                return;
            }
            eipsource.forEach((item, index) => {
                if (item.region !== region || item.routeType !== routeType || item.bandwidthInMbps !== bandwidthInMbps) {
                    disabledIndex.push(index);
                }
            });
            this.data.set('selection.disabledIndex', disabledIndex);
        }
        else {
            if (event.value.selectedIndex.length === singleNumberTextNumber) {
                eipsource.forEach((item, index) => {
                    if (!_.find(event.value.selectedItems, i => item.name === i.name)) {
                        this.data.push('selection.disabledIndex', index);
                    }
                });
                return;
            }
            else {
                this.data.merge('selection', {disabledIndex: oldSelection?.selectedIndex});
            }
        }
    }

    // 包了一层方法加节流事件是为了避免slider组件拖拽时，频繁触发change事件

    @debounce(500)
    onBandWidthChange() {
        // this.checkISChange();
        this.setFrozenField([FieldsType.BandWidth, FieldsType.PublicIpEnabled]);
        this.onPriceConfigChange();
    }

    // 根据可选区变化，进行文本变化
    handleZones(zones: string[]) {
        (this.ref('network') as NetworkType).handleZonesChange(zones);
    }

    // 根据部署方式，进行设置内部部署方式
    handleDeployType(deployType: Deployment) {
        (this.ref('network') as NetworkType).handleDeployTypeChange(deployType);
    }

    checkISChange() {
        const {originalData, formData, isDetail} = this.data.get('');
        if (!isDetail) {
            return true;
        }

        const ref = (this.ref('network') as NetworkType);
        const params = ref.getConfirmData();
        const changeItem1 = originalData.publicIpEnabled !== formData.publicIpEnabled;
        const changeItem2 = !changeItem1 && originalData.publicIpBandwidth !== formData.publicIpBandwidth && originalData.publicIpBandwidth;
        const changeItem3 = !compareArr(originalData.securityGroups.map(item => item.securityGroupId), params.securityGroups);
        const changeItem4 = originalData.intranetIpEnabled !== formData.intranetIpEnabled;
        return changeItem1 || changeItem2 || changeItem3 || changeItem4;
    }

    // 公网访问
    onPublicIpEnabledChange(target: {value: boolean}) {
        this.data.set('formData.publicIpEnabled', target.value);
        if (!target.value) {
            this.data.set('formData.publicIpBandwidth', 0);
        }
        this.setFrozenField([FieldsType.PublicIpEnabled, FieldsType.BandWidth]);
        this.fire('publicIp-change', target.value);
        this.onPriceConfigChange();
    }

    onSecurityGroupChange(target: {value: string}) {
        // this.checkISChange();
        this.setFrozenField(FieldsType.SecurityGroup);
        this.fire('change-upgrade-status', {});
    }

    // 内网访问
    onPrivateIpEnabledChange(target: {value: boolean}) {
        this.data.set('formData.intranetIpEnabled', target.value);
        // this.checkISChange();
        this.setFrozenField(FieldsType.IntranetIpEnabled);
        this.fire('privateIp-change', target.value);
        this.fire('change-upgrade-status', {});
    }

    // 付费类型改变
    onPriceConfigChange() {
        // 由于设计值的onChange所以使用nextTick等待值更新后fire，后续可以优化
        const {isDetail, isUpgrading, formData, originalData} = this.data.get('');
        const isDownGrade = isDetail && isUpgrading && originalData.publicIpBandwidth > formData.publicIpBandwidth;
        this.nextTick(() => {
            this.fire('change-upgrade-status', {});
            this.fire('price-config-change', {isDownGrade});
        });
    }

    setFrozenField(formType: string | string[]) {
        if (this.data.get('isDetail') && !this.data.get('KAFKA_ClusterMultipleUpdateWhiteList')) {
            const formTypes = typeof formType === 'string' ? [formType] : formType;
            NetworkFrozenFields.forEach(field => {
                this.data.set(`disabledByModified.${field}`, !formTypes.includes(field));
            });
        }
    }

    verifyEips() {
        return new Promise<void>((resolve, reject) => {
            const {singleNumberTextNumber, showEipsTable, isDetail} = this.data.get('');
            const {eips, publicIpEnabled} = this.data.get('formData');
            const {numberOfBrokerNodesPerZone, logicalZones, publicIpEnabled: publicIpEnabledPrev} = this.data.get('detail') || {};
            if (showEipsTable && eips?.length !== singleNumberTextNumber) {
                if (!isDetail || (!publicIpEnabledPrev && publicIpEnabled)) {
                    // 新建
                    this.data.set('errTipForEips', `请选取${singleNumberTextNumber}个EIP实例`);
                    return reject();
                }
                if (numberOfBrokerNodesPerZone * logicalZones?.length < singleNumberTextNumber && publicIpEnabled) {
                    // 扩容
                    this.data.set('errTipForEips', `请选取${singleNumberTextNumber}个EIP实例`);
                    return reject();
                }
            }
            this.data.set('errTipForEips', '');
            return resolve();
        });
    }

    // 校验
    verify() {
        return Promise.all([
            (this.ref('network') as NetworkType).verify(),
            this.verifyEips()
        ]).catch((err: Error) => {
            throw (ClusterRefType.NETWORK);
        });
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        const {publicIpEnabled, publicIpBandwidth, publicIpMode} = this.data.get('formData');
        return {
            publicIpEnabled,
            publicIpMode,
            publicIpBandwidth
        };
    }

    getOldPriceData() {
        const {
            publicIpEnabled,
            publicIpBandwidth,
            publicIpMode
        } = this.data.get('originalData');
        let priceData = {
            publicIpEnabled,
            publicIpBandwidth,
            publicIpMode
        };
        return priceData;
    }

    getDetailPrice() {
        return this.getPriceData();
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData(): OrderItemObj {
        const {formData, networks} = this.data.get('');
        const {publicIpEnabled, publicIpBandwidth, network, intranetIpEnabled, publicIpMode} = formData;
        const ref = (this.ref('network') as NetworkType);
        const params = ref.getOrderItemData();
        const base = [
            ...params,
            {
                label: TextMap.publicIpEnabled,
                text: renderSwitch(publicIpEnabled)
            }
        ];
        const extra = publicIpEnabled
            ? [{
                label: TextMap.network,
                text: publicIpMode === 'AUTO_ASSIGN' ? getItemInfoInArr(networks, network) + ` ${publicIpBandwidth + CELL.Mbps}` : 'EIP实例自行查看带宽'
            }]
            : [];
        const newIntranetAccess = [{
            label: TextMap.intranetIpEnabled,
            text: renderSwitch(intranetIpEnabled)
        }];
        return {
            title: TextMap.title,
            type: ClusterRefType.NETWORK,
            isShowNetworkPrice: publicIpEnabled,
            list: [
                ...base,
                ...extra,
                ...newIntranetAccess
            ]
        };
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData(): NormalObject {
        const ref = (this.ref('network') as NetworkType);
        const params = ref.getConfirmData();
        const {publicIpEnabled, publicIpBandwidth, intranetIpEnabled, publicIpMode, eips} = this.data.get('formData');
        return {
            ...params,
            publicIpEnabled,
            publicIpBandwidth,
            intranetIpEnabled,
            publicIpMode,
            eips
        };
    }

    getUpgrageData() {
        const {
            publicIpEnabled,
            publicIpBandwidth,
            publicIpMode,
            intranetIpEnabled,
            eips
        } = this.data.get('formData');
        const {detail} = this.data.get('');
        let newEips = [];
        eips?.forEach(item => {
            if (_.findIndex(detail.eips, i => i.name === item.name) === -1) {
                newEips.push(item);
            }
        });
        const ref = (this.ref('network') as NetworkType);
        const params = ref.getUpgradeData();
        const {
            publicIpEnabled: publicIpEnabledPrev,
            publicIpBandwidth: publicIpBandwidthPrev,
            securityGroups: securityGroupPrev,
            intranetIpEnabled: intranetIpEnabledPrev
        } = this.data.get('originalData');
        let type = '';
        if (!compareArr(params.securityGroups, securityGroupPrev.map(item => item.securityGroupId))) {
            return params;
        }
        else if (publicIpEnabledPrev && !publicIpEnabled) {
            type = UpgrageType.DISABLE_CLUSTER_EIP;
        }
        else if (!publicIpEnabledPrev && publicIpEnabled) {
            type = UpgrageType.ENABLE_CLUSTER_EIP;
        }
        else if (intranetIpEnabled !== intranetIpEnabledPrev) {
            type = intranetIpEnabled ? UpgrageType.ENABLE_CLUSTER_INTRANET_IP : UpgrageType.DISABLE_CLUSTER_INTRANET_IP;
        }
        else if (publicIpBandwidth !== publicIpBandwidthPrev) {
            type = UpgrageType.RESIZE_CLUSTER_EIP_BANDWIDTH;
        }
        return {
            type,
            publicIpEnabled,
            publicIpBandwidth,
            publicIpMode,
            eips: newEips,
            intranetIpEnabled
        };
    }

    getNewUpgrageData(currentAccessParams: NormalObject) {
        const {formData, originalData} = this.data.get('');
        const {securityGroups} = (this.ref('network') as NetworkType).getUpgradeData();
        const {
            publicIpEnabled,
            publicIpBandwidth,
            publicIpMode,
            intranetIpEnabled,
            eips,
        } = formData;

        const {
            securityGroups: securityGroupsOld,
            publicIpEnabled: publicIpEnabledOld,
            publicIpBandwidth: publicIpBandwidthOld,
            publicIpMode: publicIpModeOld,
            intranetIpEnabled: intranetIpEnabledOld,
        } = originalData;

        let params: NormalObject = {};

        let types: UpgrageType[] = [];
        if (!compareArr(securityGroups, securityGroupsOld.map((item: SecurityGroup) => item.securityGroupId))) {
            types.push(UpgrageType.UPDATE_CLUSTER_SECURITY_GROUP);
            params.securityGroups = securityGroups;
        }

        // 公网开启、公网关闭、产品间存储开启、产品间存储关闭——》传入aclEnabled和authenticationMode
        let isNeedAccessParams = false;
        if (publicIpEnabled !== publicIpEnabledOld) {
            types.push(publicIpEnabled ? UpgrageType.ENABLE_CLUSTER_EIP : UpgrageType.DISABLE_CLUSTER_EIP);
            params.publicIpEnabled = publicIpEnabled;
            isNeedAccessParams = true;
        }

        if (publicIpEnabled && publicIpBandwidth !== publicIpBandwidthOld) {
            !types.includes(UpgrageType.ENABLE_CLUSTER_EIP) && types.push(UpgrageType.ENABLE_CLUSTER_EIP);
            params.publicIpBandwidth = publicIpBandwidth;
        }

        if (publicIpEnabled && publicIpMode !== publicIpModeOld) {
            params.publicIpMode = publicIpMode;
        }

        if (intranetIpEnabled !== intranetIpEnabledOld) {
            types.push(intranetIpEnabled ? UpgrageType.ENABLE_CLUSTER_INTRANET_IP : UpgrageType.DISABLE_CLUSTER_INTRANET_IP);
            params.intranetIpEnabled = intranetIpEnabled;
            isNeedAccessParams = true;
        }

        if (isNeedAccessParams) {
            params = {
                ...params,
                ...currentAccessParams,
            };
        }
        const {detail} = this.data.get('');
        let newEips: any[] = [];
        eips?.forEach((item: {name: string}) => {
            if (_.findIndex(detail.eips, (i: {name: string}) => i.name === item.name) === -1) {
                newEips.push(item);
            }
        });

        newEips.length && (params.eips = newEips);
        params.types = types;
        return params;
    }

    setData(obj: NormalObject) {
        const ref = (this.ref('network') as NetworkType);
        this.data.set('detail', obj);
        const {clusterId} = obj;
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        !isEdgeRegion && this.getEips({clusterId});
        this.data.set('originalData', {
            ...obj,
        });

        NetworkFrozenFields.forEach(field => {
            this.data.set(`disabledByModified.${field}`, false);
        });
        const {
            publicIpEnabled,
            publicIpBandwidth,
            intranetIpEnabled,
            vpc,
            subnets,
            securityGroups,
            publicIpMode,
            eips
        } = obj;
        ref?.data.set('formData', {
            vpc: vpc.vpcId,
            subnets,
            securityGroup: securityGroups.map(item => item.securityGroupId),
        });
        this.data.merge('formData', {
            publicIpEnabled,
            publicIpBandwidth,
            intranetIpEnabled,
            publicIpMode: publicIpEnabled ? publicIpMode : 'AUTO_ASSIGN',
            eips
        });
        // this.data.get('showEipsTable') && this.resetEipsource(obj?.numberOfBrokerNodesPerZone * obj?.logicalZones?.length);
    }
}
