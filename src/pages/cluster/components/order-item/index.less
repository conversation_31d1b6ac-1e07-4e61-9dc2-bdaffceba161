/**
 * @file index.less
 * <AUTHOR>
 */
@klass: bms-cluster-orderitem;
@titleColor: #151b26;
@labelColor: #5C5F66;

.@{klass} {
    margin: 16px;
    padding: 24px;
    background: var(--whiteColor);
    border-radius: 6px;

    &__header {
        height: 40px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid var(--borderColor2);

        &_left {
            flex: 1;
        }

        &_right {
            font-weight: 500;

            &_item {
                margin-left: 30px;
                font-size: 13px;
            }

            .price {
                color: var(--priceColor);
            }
        }
    }

    &__content {

        &_item {
            display: inline-flex;
            width: 33.33333%;
            margin-top: 16px;

            &_label {
                width: 89px;
                flex: 0 0 auto;
                padding-right: 5px;
                color: @labelColor;
            }

            &_text {
                color: @titleColor;
            }

            &.extra-class {
                display: flex;
                width: 100%;

                .@{klass}__content_item {

                    &_text {
                        flex: 1;

                        &_desc {
                            display: block;
                        }
                    }

                    &_extra {
                        display: flex;
                        margin: 15px 0 5px;
                        padding: 10px;
                        background-color: var(--bgColor);

                        &_item {
                            flex: 1;
                        }
                    }
                }
            }
        }
    }
}
