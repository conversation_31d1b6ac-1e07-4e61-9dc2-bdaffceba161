/**
 * 账单
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {ClusterRefType, PaymentType} from '@/common/enums/constant';

import {ShopCartTextMap, PriceCell} from '../shop-cart/base';

import './index.less';

const klass = 'bms-cluster-orderitem';

export default class OrderItem extends Component {
    static template = html`
    <div class="${klass}">
        <div class="${klass}__header">
            <div class="${klass}__header_left">
                <s-append noHighlight label="{{title}}" />
            </div>
            <div class="${klass}__header_right">
                <span class="${klass}__header_right_item" s-if="{{type === '${ClusterRefType.NODE}' && !isDetail}}">
                    <span class="mr10">${ShopCartTextMap.clusterPrice}</span>
                    <span
                        s-if="payment === '${PaymentType.POSTPAID}'"
                        class="price"
                    >
                        ¥{{clusterPrice}}/${PriceCell}
                    </span>
                    <span
                        s-else
                        class="price"
                    >
                        ¥{{clusterPrice}}/{{timeLength + (timeUnit === 'month' ? '个月' : '年')}}
                    </span>
                </span>
                <span class="${klass}__header_right_item" s-if="{{isShowNetworkPrice && !isDetail}}">
                    <span class="mr10">${ShopCartTextMap.networkPrice}</span>
                    <span
                        s-if="payment === '${PaymentType.POSTPAID}'"
                        class="price"
                    >
                        ¥{{publicIpPrice}}/${PriceCell}
                    </span>
                    <span
                        s-else
                        class="price"
                    >
                        ¥{{publicIpPrice}}/{{timeLength + (timeUnit === 'month' ? '个月' : '年')}}
                    </span>
                </span>
            </div>
        </div>
        <div class="${klass}__content">
            <div class="${klass}__content_item {{item.hasAddition ? 'extra-class' : ''}}" s-for="item in list">
                <span class="${klass}__content_item_label">{{item.label}}</span>
                <span class="${klass}__content_item_text">
                    <span class="${klass}__content_item_text_desc">{{item.text}}</span>
                    <span class="${klass}__content_item_extra"
                        s-if="{{item.hasAddition}}">
                        <span class="${klass}__content_item_extra_item"
                            s-for="addItem in item.addition">
                            <span class="addition-label">{{addItem.label}}</span>
                            <span class="addition-label">{{addItem.text}}</span>
                        </span>
                    </span>
                </span>
            </div>
        </div>
    </div>`;

    static components = {
        's-append': AppLegend
    };
}
