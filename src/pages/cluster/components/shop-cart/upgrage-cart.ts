/**
 * 变配集群购物车
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import Tip from '@/components/tip';
import {formatTime} from '@/common/util';
import {PaymentType} from '@/common/enums/constant';

import ClusterShopCart, {klass, UpgrageShopCartTextMap, NextInputHeight} from './base';

export default class UpgrageShopCart extends ClusterShopCart {
    static template = html`
    <template>
        <base-shopcart
            on-click="onClickHandle"
            isSubmiting="{{isSubmiting}}"
            totalPrice="{{totalPrice}}"
            decountPrice="{{decountPrice}}"
            finalPrice="{{finalPrice}}"
            current="{{current}}"
            payment="{{payment}}">
            <template slot="wrap_left">
                <s-button
                    class="ml16"
                    width="88"
                    height="{{${NextInputHeight}}}"
                    on-click="onReset">
                    重置
                </s-button>
            </template>
            <div slot="wrap_right">
                <div class="${klass}_wrap_right_item shopcart">
                    <span class="shopcart_top">${UpgrageShopCartTextMap.oldClusterPrice}</span>
                    <span class="shopcart_bottom">
                        <div class="shopcart_bottom_price">
                            {{oldClusterPrice | getPrice(payment, timeLength, timeUnit)}}
                        <div>
                    </span>
                </div>
                <div class="${klass}_wrap_right_item shopcart">
                    <span class="shopcart_top">${UpgrageShopCartTextMap.clusterPrice}</span>
                    <span class="shopcart_bottom">
                        <div class="shopcart_bottom_price">
                            {{clusterPrice | getPrice(payment, timeLength, timeUnit)}}
                        <div>
                    </span>
                </div>
                <div class="${klass}_wrap_right_item shopcart" s-if="{{payment === '${PaymentType.PREPAID}'}}">
                    <span class="shopcart_top">
                        ${UpgrageShopCartTextMap.margin}
                        <tip-cmpt>
                            <div s-if="{{expirationTime}}">当前预付费集群到期日期：{{expirationTime | formatTime}}</div>
                            <div>
                                应付金额 = (新配置费用-原配置费用)
                                <template s-if="{{payment === '${PaymentType.PREPAID}'}}"> x (剩余天数)</template>
                            </div>
                        </tip-cmpt>
                    </span>
                    <span class="shopcart_bottom">
                        <div class="shopcart_bottom_price">
                            ¥{{margin || 0}}
                        <div>
                    </span>
                </div>
            </div>
        </base-shopcart>
    </template>
    </div>`;

    static components = {
        ...ClusterShopCart.components,
        'tip-cmpt': Tip,
    };

    static filters: SanFilterProps = {
        ...ClusterShopCart.filters,
        formatTime
    }

    onReset() {
        this.fire('reset', {});
    }
}