/**
 * @file index.less
 * <AUTHOR>
 */
@klass: bms-cluster-shopcart;

.@{klass} {
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    height: 80px;
    padding: 16px;
    background: var(--whiteColor);
    justify-content: flex-start;
    box-shadow: 0 1px 10px 0 var(--shadowColor);
    z-index: 365613;

    &_wrap {
        display: flex;
        align-items: center;

        &_left {
            float: left;

            button {
                font-size: 14px;
                padding: 0;
            }
        }

        &_right {
            margin-left: 40px;
            float: left;

            &_item {
                float: left;
                margin-left: 40px;

                &:first-child {
                    margin-left: 0;
                }
            }

            .shopcart {

                &_top {
                    font-size: 12px;
                    line-height: 20px;
                    color: var(--priceDescColor);
                }

                &_bottom {

                    &_price {
                        font-size: 20px;
                        color: var(--priceColor);
                        line-height: 28px;
                        font-weight: 500;
                    }

                    &_desc {
                        font-size: 12px;
                    }
                }
            }
        }
    }
}
