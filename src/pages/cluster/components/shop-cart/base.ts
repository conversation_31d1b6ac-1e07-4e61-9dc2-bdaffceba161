/**
 * 购物车
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button} from '@baidu/sui';

import {PaymentType} from '@/common/enums/constant';
import {UtilHandler} from '../../util/util';

import './index.less';

export const klass = 'bms-cluster-shopcart';

export enum ShopcartClickType {
    NEXT = 'next',
    PREV = 'prev',
    CONFIRM = 'confirm',
    CANCEL ='cancel'
}

export const UpgrageShopCartTextMap = {
    oldClusterPrice: '原配置集群费用：',
    clusterPrice: '新配置集群费用：',
    margin: '应付金额：'
};

export const ShopCartTextMap = {
    clusterPrice: '集群费用：',
    networkPrice: '网络费用：',
    totalPrice: '总费用：',
    totalMoney: '总金额：',
    couponPrice: '抵扣金额：',
    finalPrice: '实际金额：'
};

export const PriceCell = '分钟';

export const NextInputWidth = 86;
export const NextInputHeight = 38;

export default class ClusterShopCart extends Component {

    static template = html`
    <div class="${klass}">
        <div class="${klass}_wrap">
            <div class="${klass}_wrap_left">
                <s-button
                    width="{{${NextInputWidth}}}"
                    height="{{${NextInputHeight}}}"
                    skin="primary"
                    s-if="{{current === 1}}"
                    on-click="onClickHandle('${ShopcartClickType.NEXT}')">
                    下一步
                </s-button>
                <s-button
                    width="{{${NextInputWidth}}}"
                    height="{{${NextInputHeight}}}"
                    s-if="{{current === 2}}"
                    on-click="onClickHandle('${ShopcartClickType.PREV}')">
                    上一步
                </s-button>
                <s-button
                    width="{{${NextInputWidth}}}"
                    height="{{${NextInputHeight}}}"
                    disabled="{{isSubmiting}}"
                    skin="primary"
                    s-if="{{current === 2}}"
                    class="ml10"
                    on-click="onClickHandle('${ShopcartClickType.CONFIRM}')">
                    {{isSubmiting ? '提交中' : '提交订单'}}
                </s-button>
                <slot name="wrap_left" s-if="{{current === 1}}"></slot>
                <s-button
                    class="ml16"
                    width="{{${NextInputWidth}}}"
                    height="{{${NextInputHeight}}}"
                    s-if="{{current === 1}}"
                    on-click="onClickHandle('${ShopcartClickType.CANCEL}')">
                    取消
                </s-button>
            </div>
            <div class="${klass}_wrap_right">
                <slot name="wrap_right" s-if="{{current === 1}}"></slot>
                <div class="${klass}_wrap_right_item shopcart" s-if="{{current === 2}}">
                    <span class="shopcart_top">
                        {{getTotalPriceText}}
                    </span>
                    <span class="shopcart_bottom">
                        <div class="shopcart_bottom_price">
                            <span s-if="payment === '${PaymentType.POSTPAID}'">¥{{totalPrice}}/${PriceCell}</span>
                            <span s-else>¥{{totalPrice}}</span>
                            <span s-if="payment === '${PaymentType.POSTPAID}'" class="shopcart_bottom_desc desc">{{totalPrice | getPriceDesc}}</span>
                        <div>
                    </span>
                </div>
                <div class="${klass}_wrap_right_item shopcart" s-if="current === 2 && payment === '${PaymentType.PREPAID}'">
                    <span class="shopcart_top">${ShopCartTextMap.couponPrice}</span>
                    <span class="shopcart_bottom">
                        <div class="shopcart_bottom_price">
                            ¥{{decountPrice}}
                        <div>
                    </span>
                </div>
                <div class="${klass}_wrap_right_item shopcart" s-if="current === 2 && payment === '${PaymentType.PREPAID}'">
                    <span class="shopcart_top">${ShopCartTextMap.finalPrice}</span>
                    <span class="shopcart_bottom">
                        <div class="shopcart_bottom_price">
                            ¥{{finalPrice}}
                        <div>
                    </span>
                </div>
            </div>
        </div>
    </div>`;

    static components = {
        's-button': Button,
        'base-shopcart': this
    };

    initData() {
        return {
            current: 1,
            isSubmiting: false,
            clusterPrice: 0,
            publicIpPrice: 0
        };
    }

    static computed: SanComputedProps = {
        getTotalPriceText() {
            return this.data.get('payment') === PaymentType.POSTPAID
                ? ShopCartTextMap.totalPrice
                : ShopCartTextMap.totalMoney;
        }
    };

    static filters: SanFilterProps = {
        getPrice: UtilHandler.getPrice,
        getPriceDesc: UtilHandler.getPriceDesc
    };

    onClickHandle(type: string) {
        this.fire('click', type);
    }
}
