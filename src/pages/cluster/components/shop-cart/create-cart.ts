/**
 * 创建集群购物车
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {PaymentType} from '@/common/enums/constant';

import ClusterShopCart, {klass, ShopCartTextMap} from './base';

export default class CreateShopCart extends ClusterShopCart {
    static template = html`
    <template>
        <base-shopcart
            on-click="onClickHandle"
            isSubmiting="{{isSubmiting}}"
            current="{{current}}"
            totalPrice="{{totalPrice}}"
            decountPrice="{{decountPrice}}"
            finalPrice="{{finalPrice}}"
            payment="{{payment}}">
            <div slot="wrap_right">
                <div class="${klass}_wrap_right_item shopcart" s-if="{{current === 1}}">
                    <span class="shopcart_top">${ShopCartTextMap.clusterPrice}</span>
                    <span class="shopcart_bottom">
                        <div class="shopcart_bottom_price">
                            {{clusterPrice | getPrice(payment, timeLength, timeUnit)}}
                            <span 
                                s-if="payment === '${PaymentType.POSTPAID}'"
                                class="shopcart_bottom_desc desc">
                                {{clusterPrice | getPriceDesc}}
                            </span>
                        <div>
                    </span>
                </div>
                <div class="${klass}_wrap_right_item" s-if="{{publicIp && current === 1}}">
                    <span class="shopcart_top">${ShopCartTextMap.networkPrice}</span>
                    <span class="shopcart_bottom">
                        <div class="shopcart_bottom_price">
                            {{publicIpPrice | getPrice(payment, timeLength, timeUnit)}}
                            <span
                                s-if="payment === '${PaymentType.POSTPAID}'"
                                class="shopcart_bottom_desc desc">
                                {{publicIpPrice | getPriceDesc}}
                            </span>
                        <div>
                    </span>
                </div>
            </div>
        </base-shopcart>
    </template>
    </div>`;
}
