// 监控指标分类
export const clusterTypeList = [
    {label: '常用', value: 'comman', checked: true},
    {label: '其他', value: 'other'}
];

export const brokerByserviceTypeList = [
    {label: '消息', value: 'message', checked: true},
    {label: '分区', value: 'partition'},
    {label: 'GC', value: 'gc'},
    {label: 'ZK', value: 'zk'},
    {label: '其他', value: 'other'}
];

export const brokerByhostTypeList = [
    {label: 'CPU', value: 'cpu', checked: true},
    {label: '内存', value: 'ram'},
    {label: '网络', value: 'network'},
    {label: '磁盘', value: 'disk'},
    {label: '其他', value: 'other'},
];

export const consumerTypeList = [
    {label: '常用', value: 'comman', checked: true},
    {label: '其他', value: 'other', disabled: true}
];

export const topicTypeList = [
    {label: '常用', value: 'comman', checked: true},
    {label: '其他', value: 'other', disabled: true}
]


