/**
 * 指标筛选弹窗组件
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Radio, Checkbox} from '@baidu/sui';
import {clusterTypeList, brokerByserviceTypeList, brokerByhostTypeList, consumerTypeList, topicTypeList} from './config';
import myStore, {connectMyStore} from '@/store/index';
import {TabKeyType} from '../../detail/monitor/base';
import './index.less';

const klass = 'monitor-filter';
@connectMyStore({
    originalconsumerIndicators: 'originalconsumerIndicators',
    originalclusterIndicators: 'originalclusterIndicators',
    originaltopicIndicators: 'originaltopicIndicators',
    originalbrokerIndicators: 'originalbrokerIndicators',
    consumerIndicators: 'consumerIndicators',
    clusterIndicators: 'clusterIndicators',
    topicIndicators: 'topicIndicators',
    brokerIndicators: 'brokerIndicators',
})
export default class extends Component {
    static template = html`
    <template>
        <s-dialog
            title="指标筛选"
            open="{{open}}"
            mask="{{true}}"
            width="1100"
            class="${klass}"
            on-close="onClose">
            <s-radio-group
                on-change="handleTypeChange"
                value="{{type}}"
                radioType="button"
                datasource="{{typeList}}"
            >
            </s-radio-group>
            <s-checkbox
                label="全选"
                class="mt16"
                checked="{=checkBoxValue=}"
                indeterminate="{{true}}"
                on-change="onAllSelectChange"
            />
            <div class="checkbox-area">
                <s-checkbox s-for="item, index in checkList" checked="{{item.checked}}" disabled="{{item.disabled}}" value="{{item}}" on-change="onCheckBoxChange($event, index)">
                    {{item.label}}
                    <p class="alias">{{item.alias}}</p>
                </s-checkbox>
            </div>
            <div slot="footer">
                <s-button on-click="onConfirm" skin="primary">确定</s-button>
                <s-button on-click="onClose">取消</s-button>
            </div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup
    };

    initData() {
        return {
            open: true,
            type: 'comman',
            typeList: clusterTypeList,
            checkList: [],
            brokerByserviceTypeList: brokerByserviceTypeList,
            brokerByhostTypeList: brokerByhostTypeList,
            consumerTypeList: consumerTypeList,
            topicTypeList: topicTypeList,
            showCheckList: true,
            checkBoxValue: false
        }
    }

    attached() {
        const {current, monitorType} = this.data.get('');
        if (current === 'broker') {
            const typeList = this.data.get(`brokerBy${monitorType}TypeList`);
            const type = typeList[0].value;
            const indicatorList = this.data.get(`originalbrokerIndicators.${monitorType}Indicators`);
            this.data.set('typeList', typeList);
            this.data.set('type', type);
            this.data.set('checkList', indicatorList[type]);
        } else if(current !== 'cluster') {
            if (current === 'topic') {
                const typeList = this.data.get('topicTypeList');
                const type = typeList[0].value;
                const indicatorList = this.data.get(`originaltopicIndicators.${monitorType}Indicators`);
                this.data.set('typeList', typeList.map((item: any) => ({
                    ...item,
                    // 主题监控-》基础监控中不显示「其他」 其余显示
                    disabled: item.value === 'other' && monitorType === 'basic'
                })));
                this.data.set('type', type);
                this.data.set('checkList', indicatorList[type]);
            } else {
                const typeList = this.data.get('consumerTypeList');
                const type = typeList[0].value;
                const indicatorList = this.data.get(`originalconsumerIndicators.${monitorType}Indicators`);
                this.data.set('typeList', typeList);
                this.data.set('type', type);
                this.data.set('checkList', indicatorList[type]);
            }
        } else {
            const typeList = clusterTypeList;
            const type = typeList[0].value;
            const indicatorList = this.data.get('originalclusterIndicators');
            this.data.set('checkList', indicatorList[type]);
        }
        const hasNotSelect = this.data.get('checkList').some(item => !item.checked);
        this.data.set('checkBoxValue', !hasNotSelect);
    }

    handleTypeChange(target: {value: string}) {
        const {current, monitorType} = this.data.get('');
        this.data.set('type', target.value);
        this.data.set('checkList', []);
        let indicatorList = [];
        if (current === TabKeyType.CLUSTER) {
            indicatorList = this.data.get(`${current}Indicators`);
        } else {
            indicatorList = this.data.get(`${current}Indicators.${monitorType}Indicators`);
        }
        this.data.set('checkList', indicatorList[target.value]);
        const hasNotSelect = this.data.get('checkList').some(item => !item.checked);
        this.data.set('checkBoxValue', !hasNotSelect);
    }

    onCheckBoxChange(target: {value: string}, index: number) {
        const {current, monitorType, type} = this.data.get('');
        let changeType = current.charAt(0).toUpperCase() + current.slice(1);
        myStore.dispatch(`change${changeType}Indicators`, {monitorType, type, index, target});
        const hasNotSelect = this.data.get('checkList').some(item => !item.checked);
        this.data.set('checkBoxValue', !hasNotSelect);
    }

    onAllSelectChange(target: {value: string}) {
        const {current, monitorType, type, checkList} = this.data.get('');
        let changeType = current.charAt(0).toUpperCase() + current.slice(1);
        checkList.forEach((item, index: number) => {
            if (!checkList[index].disabled) {
                this.data.set(`checkList[${index}].checked`, target.value);
                myStore.dispatch(`change${changeType}Indicators`, {monitorType, type, index, target});
            }
        });
    }

    // 确认
    async onConfirm() {
        const {current, monitorType} = this.data.get('');
        const changeType = current.charAt(0).toUpperCase() + current.slice(1);
        myStore.dispatch(`update${changeType}Indicators`, monitorType);
        this.fire('confirm', {});
        this.onClose();
    }

    // 取消
    onClose() {
        const {current, monitorType} = this.data.get('');
        const changeType = current.charAt(0).toUpperCase() + current.slice(1);
        myStore.dispatch(`reset${changeType}Indicators`, monitorType);
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
