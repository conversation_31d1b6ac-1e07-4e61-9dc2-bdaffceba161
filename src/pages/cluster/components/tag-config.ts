/**
 * 集群标签
 *
 * @file index.js
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {Form} from '@baidu/sui';
import {AppLegend} from '@baidu/sui-biz';
import {TagEditPanel} from '@baiducloud/tag-sdk/san';
import {TagSDK} from '@baiducloud/tag-sdk';
import HttpClient from '@baiducloud/httpclient';
import {DOCS_LINK} from '@/common/config';
const klass = 'bms-cluster-create-tag';
export default class ClusterTag extends Component {
    static template = html`
    <div class="${klass} bms-form-panel">
        <s-append noHighlight label="集群标签" />
        <s-form label-align="left">
            <s-form-item
                label="绑定标签：">
                <tag-edit-panel
                    s-ref="tagPanel"
                    instances="{{defaultInstances}}"
                    sdk="{{tagSDK}}"
                    helpDocUrl="${DOCS_LINK.tagHelp}"
                />
            </s-form-item>
        </s-form>
    </div>
    `;

    static components = {
        's-append': AppLegend,
        's-form': Form,
        's-form-item': Form.Item,
        'tag-edit-panel': TagEditPanel
    };

    initData() {
        return {
            tagSDK: new TagSDK({
                serviceType: 'KAFKA',
                client: new HttpClient({}, {
                    getCsrfToken() {
                        return this.$cookie.get('bce-user-info');
                    },
                    getCurrentRegion() {
                        return window.$context.getCurrentRegion();
                    }
                }),
                context: window.$context
            }),
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: 'KAFKA-Cluster',
                            tagValue: ''
                        }
                    ]
                }
            ]
        };
    }

    // 获取标签,提供外部获取标签实例的能力
    getTagPanel() {
        return this.ref('tagPanel');
    }

    // 校验
    verify() {
        return this.ref('tagPanel').validate(false);
    }

    // 获取价格参数(注意，这个是给予父组件使用)，不能随便删除
    getPriceData() {
        return {};
    }

    getDetailPrice() {
        return {};
    }

    // 获取订单信息页面参数(注意，这个是给予父组件使用)，不能随便删除
    getOrderItemData(): NormalObject {
        return {};
    }

    // 获取创建订单参数(注意，这个是给予父组件使用)，不能随便删除
    getConfirmData() {

    }
}
