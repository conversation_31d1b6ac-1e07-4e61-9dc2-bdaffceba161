import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Form, Radio, Loading, Badge, Switch, Select, Alert} from '@baidu/sui';
import {DOCS_LINK} from '@/common/config';
import {Timechoices, autoLengthOfMon, autoLengthOfYear} from '@/common/enums';
import './index.less';

const klass = 'bms-price-config';
const timeChoices = Timechoices.toArray();
export default class PriceConfig extends Component {
    static template = html`
    <div class="bms-form-panel ${klass}">
        <s-append noHighlight label="后付费转为预付费" />
        <s-alert skin="info" class="mb16">
            目前产品支持由后付费变更为预付费模式，您需要确定预付费的购买时长和续费方式
        </s-alert>
        <s-form
            s-ref="form"
            rules="{{rules}}"
            data="{{formData}}">
            <s-formitem label="购买时长：">
                <s-radio-group
                    value="{= formData.timeLength =}"
                    radioType="button"
                    on-change="onPriceConfigChange"
                >
                    <s-radio s-for="item, index in timeChoices" value="{{item.value}}">
                        <s-badge text="折扣" offset="{{[-3, -16]}}" s-if="item.value > 9">
                            {{item.text}}
                        </s-badge>
                        <span s-else>{{item.text}}</span>
                    </s-radio>
                </s-radio-group>
            </s-formitem>
            <s-formitem
                label="自动续费："
                class="form-item-center">
                <template>
                    <s-switch
                        checked="{= formData.autoRenew.renew =}"
                        on-change="onChange">
                    </s-switch>
                    <a class="inline-desc" href="${DOCS_LINK.autoRenew}" target="blank">
                        什么是自动续费
                    </a>
                </template>
            </s-formitem>
            <s-formitem
                s-if="formData.autoRenew.renew"
                label="续费周期："
                help="系统将于到期7天前进行扣费，扣费时长为{{formData.autoRenew.renewTime}}{{formData.autoRenew.renewTimeUnit === 'month' ? '月' : '年'}}"
            >
                <s-select
                    datasource="{{ autoUnits }}"
                    value="{=formData.autoRenew.renewTimeUnit=}"
                    on-change="onRenewTimeUnitChange"></s-select>
                <s-select
                    datasource="{{ formData.autoRenew.renewTimeUnit === 'month' ? autoLengthOfMon : autoLengthOfYear }}"
                    value="{=formData.autoRenew.renewTime=}"
                    class="ml16">
                </s-select>
            </s-formitem>
        </s-form>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-form': Form,
        's-formitem': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-loading': Loading,
        's-badge': Badge,
        's-switch': Switch,
        's-select': Select,
        's-alert': Alert,
    };

    initData() {
        return {
            timeChoices: timeChoices,
            autoUnits: [{text: '按月', value: 'month'}, {text: '按年', value: 'year'}],
            autoLengthOfMon: autoLengthOfMon.toArray(),
            autoLengthOfYear: autoLengthOfYear.toArray(),
            formData: {
                timeLength: 1,
                timeUnit: 'month',
                autoRenew: {
                    renew: true,
                    renewTimeUnit: 'month',
                    renewTime: 1
                }
            },
        };
    }

    attached() {
        this.watch('formData.timeLength', () => {
            this.fire('change', {});
        });
    }

    getUpgradeData() {
        const formData = this.data.get('formData');
        return {
            ...formData
        };
    }
}
