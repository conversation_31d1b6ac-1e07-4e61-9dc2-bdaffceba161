/**
 * 配置推荐弹窗
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {Checkbox, Dialog, Form, Radio, Select, Loading, Alert, Badge} from '@baidu/sui';

import api from '@/common/client';

import {ZoneType, Deployments} from '../node-config/conf';
import {renderZoneLabel} from '@/common/util';
import {Deployment, RecommandClusterType} from '@/common/enums/constant';
import {prePaidParam, postPaidParam} from '@/common/config';
import recommandCard from '../recommand-card';

import './index.less';
const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
const klass = 'specification-recommand';

export type RecommandType = {
    nodeType: string;
    nodeName: string;
    numberOfBrokerNodesPerZone: number;
    storageMeta: {
        storageType: string;
        storageSize: number;
        numberOfDisk: number;
    };
    partitionLimit: number;
    available: boolean;
    type: string;
};

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="${klass}"
            title="规模预估工具"
            width="1000"
            okText="应用"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
            confirmButtonDisable="{{active === 0}}"
            loadingAfterConfirm="{{false}}">
            <span class="title">业务信息</span>
            <s-form
                class="form-format"
                s-ref="form"
                rules="{{rules}}"
                data="{= formData =}">
                <s-form-item
                    label="流量峰值："
                    prop="throughput"
                    required
                    class="${klass}__throughput">
                    <s-select
                        value="{{ formData.throughput }}"
                        datasource="{{throughputs}}"
                        getPopupContainer="{{getPopupContainer}}"
                        on-change="onThroughputChange"
                        filterable
                    />
                    <span class="unit-desc">MB/s</span>
                    <p slot="help" class="${klass}__throughput__help">流量峰值 = （<span>写入业务流量</span> * 预估副本数）* 120%。为确保集群稳定性，建议额外购买高于业务流量20%左右的流量作为 buffer。如需支持更高流量规格，请提交工单进行咨询。</p>
                </s-form-item>
                <s-form-item label="部署方式：" prop="deployType">
                    <s-radio-group
                        value="{= formData.deployType =}"
                        datasource="{{deployments}}"
                        radioType="button"
                        on-change="onDeployChange"
                    />
                </s-form-item>
                <s-form-item
                    label="可用区："
                    class="available-areas"
                    required
                    prop="zone">
                        <template s-if="{{!availableLoading}}">
                            <s-radio-group
                                value="{= formData.zone =}"
                                on-change="onRadioChange"
                                s-if="{{formData.deployType === '${Deployment.PERFROMANCE}'}}"
                            >
                                <s-radio
                                    s-for="item in availableAreasFirst"
                                    disabled="{{item.soldout}}"
                                    value="{=item.value=}"
                                >
                                    <s-badge
                                        s-if="{{item.soldout}}"
                                        text="售罄"
                                        skin="normal"
                                        offset="{{[-10, 0]
                                    }}">
                                        {{item.text}}
                                    </s-badge>
                                    <span s-else>{{item.text}}</span>
                                </s-radio>
                            </s-radio-group>
                            <s-checkbox-group
                                s-else
                                class="available-areas__checkbox"
                                value="{{formData.zoneList}}"
                                on-change="onCheckBoxChange"
                            >
                                <s-checkbox
                                    s-for="item in availableAreasSecond"
                                    disabled="{{item.disabled || item.soldout}}"
                                    value="{{item.value}}"
                                >
                                    <s-badge
                                        s-if="{{item.soldout}}"
                                        text="售罄"
                                        skin="normal"
                                        offset="{{[-10, 0]
                                    }}">
                                        {{item.text}}
                                    </s-badge>
                                    <span s-else>{{item.text}}</span>
                                </s-checkbox>
                            </s-checkbox-group>
                            <p
                                class="err"
                                s-if="zoneErr && formData.deployType === '${Deployment.AVAILABILITY}'">
                                {{zoneErr}}
                            </p>
                        </template>
                        <s-loading s-else loading />
                </s-form-item>
            </s-form>
            <div class="${klass}__specifition__content">
                <div class="${klass}__specifition__content__header">
                    <span class="title">集群规模</span>
                    <s-alert class="${klass}__specifition__content__header_alert">预估的集群规模如无出现无资源的情况，请尝试选择其他可用区</s-alert>
                </div>
                <div s-if="needCheck && formData.deployType === '${Deployment.AVAILABILITY}'" class="errTip"></div>
                <div
                    class="${klass}__specifition__content__card"
                    s-else-if="!listLoading"
                >
                    <recommand-card
                        clusterObj="{{costEffectiveCluster}}"
                        final-price="{{price.costEffective}}"
                        payment="{{payment}}"
                        timeLength="{{timeLength}}"
                        timeUnit="{{timeUnit}}"
                        type="${RecommandClusterType.COSTEFFECTIVE}"
                        active="{{active === 1}}"
                        on-select-card="onSelectCard($event)"
                    ></recommand-card>
                    <recommand-card
                        clusterObj="{{highPerformanceCluster}}"
                        final-price="{{price.highPerformance}}"
                        payment="{{payment}}"
                        timeLength="{{timeLength}}"
                        timeUnit="{{timeUnit}}"
                        type="${RecommandClusterType.HIGHPERFORMANCE}"
                        active="{{active === 2}}"
                        on-select-card="onSelectCard($event)"
                    >
                    </recommand-card>
                </div>
                <div s-else class="loading"><s-loading class="loading" loading /></div>
            </div>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        'recommand-card': recommandCard,
        's-radio-group': Radio.RadioGroup,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-loading': Loading,
        's-alert': Alert,
        's-badge': Badge,
        's-checkbox': Checkbox,
        's-radio': Radio
    };

    initData() {
        return {
            rules: {
            },
            open: true,
            formData: {
                zone: '',
                zoneList: [],
                deployType: Deployment.PERFROMANCE,
                throughput: ''
            },
            throughputs: [],
            selectZones: [],
            availableAreasFirst: [],
            availableAreasSecond: [],
            deployments: Deployments,
            availableLoading: true,
            costEffectiveCluster: {},
            highPerformanceCluster: {},
            listLoading: true,
            needCheck: false,
            active: 0,
            price: {
                costEffective: 0,
                highPerformance: 0
            }
        };
    }


    async attached() {
        await this.getThroughputs();
        this.getZones();
    }

    async getThroughputs() {
        await api.listThroughputs({})
            .then((result: {throughputs: number[]}) => {
                this.data.set('formData.throughput', result.throughputs[0]);
                const throughputs = result.throughputs.map(item => ({
                    value: item,
                    text: item
                }));
                this.data.set('throughputs', throughputs);
            });
    }

    // 应用推荐规格
    onConfirm() {
        const {formData, selectCardNode} = this.data.get('');
        const {numberOfDisk, storageSize, storageType} = this.data.get('selectCardNode.storageMeta');
        this.fire('success', {
            formData: {
                ...formData,
                singleNumber: selectCardNode.numberOfBrokerNodesPerZone,
                storageType: storageType,
                nodeType: selectCardNode.nodeType
            },
            storageMetaSource: [
                {diskNum: numberOfDisk, diskStorage: storageSize, total: numberOfDisk * storageSize}
            ],
            nodeTypes: []
        });
        this.onClose();
    }

    onSelectCard(target: {type: string}) {
        if (target.type === RecommandClusterType.COSTEFFECTIVE) {
            this.data.set('selectCardNode', this.data.get('costEffectiveCluster'));
            this.data.set('active', 1);
        } else {
            this.data.set('selectCardNode', this.data.get('highPerformanceCluster'));
            this.data.set('active', 2);
        }
    }

    // 获取推荐数据
    async getRecommandList() {
        this.data.set('listLoading', true);
        this.data.set('active', 0);
        const {formData, selectZones} = this.data.get('');
        // 请求推荐的数据
        await api.getClusterRecommandList(formData.throughput, {
            deployType: formData.deployType,
            logicalZones: selectZones
        }).then((result: {costEffective: RecommandType, highPerformance: RecommandType}) => {
            this.data.set('costEffectiveCluster', result.costEffective);
            this.queryPrice(result.costEffective, 'costEffective');
            this.data.set('highPerformanceCluster', result.highPerformance);
            this.queryPrice(result.highPerformance, 'highPerformance');
            this.data.set('listLoading', false);

        });
    }

    // 获取可用区列表
    getZones() {
        api.listZone({})
            .then((data: {ha: string[][], hp: string[], all: string[], sellout: string[]}) => {
                this.data.set('ha', data.ha);
                this.data.set('deployments[1].disabled', data.ha.length <= 0);
                const area = _.map(data.all, i => ({
                    value: i,
                    text: renderZoneLabel(i),
                    soldout: _.includes(data.sellout, i)
                }));
                this.data.set('availableAreasFirst', area);
                this.data.set('availableAreasSecond', area);
                const value = data.hp[0] ? data.hp[0] : '';
                this.data.set('formData.zone', value);
                this.setSelectZones();
                this.data.set('availableLoading', false);
            })
            .finally(() => this.data.set('availableLoading', false));
    }

    // 设置选中区域
    setSelectZones() {
        const {zone, zoneList, deployType} = this.data.get('formData');
        const isSimple = deployType === Deployment.PERFROMANCE;
        const type = isSimple ? ZoneType.SINGLE : ZoneType.MULTI;
        const tem = deployType === Deployment.PERFROMANCE ? zone : zoneList;
        const zones = type === ZoneType.SINGLE ? [tem] : tem;
        this.data.set('selectZones', zones);
        if (deployType === Deployment.PERFROMANCE) {
            this.getRecommandList();
        } else {
            this.verifyZone().then(() => {
                this.data.set('needCheck', false);
                this.getRecommandList();
            }).catch(() => {
                this.data.set('needCheck', true);
                this.data.set('active', 0);
            });
        }
    }

    queryPrice(cluster: RecommandType, type: string) {
        const {payment, timeLength, timeUnit} = this.data.get('');
        const {deployType, zoneList} = this.data.get('formData');
        const {numberOfDisk, storageSize} = cluster.storageMeta;
        const priceData = {
            payment,
            timeLength,
            timeUnit,
            count: deployType === Deployment.AVAILABILITY ? cluster.numberOfBrokerNodesPerZone * zoneList.length : cluster.numberOfBrokerNodesPerZone,
            nodeType: cluster.nodeType,
            storageMeta: {
                numberOfDisk,
                storageSize,
                storageType: cluster.storageMeta.storageType
            },
            publicIpBandwidth: 0,
            publicIpEnabled: false
        };
        const configsFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1,
            },
            {
                name: 'type',
                value: cluster.nodeName.slice(6).toUpperCase(),
                scale: priceData.count,
            },
            {
                name: priceData.storageMeta.storageType.toLowerCase(),
                value: priceData.storageMeta.storageSize + 'G',
                scale: priceData.count,
            },
        ];
        let publicParam;
        if (priceData.payment === 'Postpaid') {
            publicParam = {
                ...postPaidParam,
                region: this.$context.getCurrentRegionId(),
                scene: 'NEW',
            };
        }
        else {
            publicParam = {
                ...prePaidParam,
                duration: priceData.timeLength,
                timeUnit: priceData.timeUnit?.toUpperCase(),
                region: this.$context.getCurrentRegionId(),
                orderType: 'NEW',
            };
        }
        const configs = [{
            ...publicParam,
            flavor: configsFlavor
        }];
        if (isXushang) {
            api.queryPriceInXS({configs}).then(
                (result: [{price: number}]) => {
                    if (type === RecommandClusterType.HIGHPERFORMANCE) {
                        this.data.set('price.highPerformance', result[0].price);
                    } else {
                        this.data.set('price.costEffective', result[0].price);
                    }
                });
        }
        else {
            api.queryPrice({...priceData}).then(
                (result: {payment: string, cluster: number, publicIp: string}) => {
                    if (type === RecommandClusterType.HIGHPERFORMANCE) {
                        this.data.set('price.highPerformance', result.cluster);
                    } else {
                        this.data.set('price.costEffective', result.cluster);
                    }
                });
        }
    }

    onThroughputChange(target: {value: number}) {
        this.data.set('formData.throughput', target.value);
        this.getRecommandList();
    }

    // 部署方式的改变
    onDeployChange(target: {value: Deployment}) {
        this.data.set('formData.deployType', target.value);
        this.setSelectZones();
    }

    // radio 改变
    onRadioChange(target: {value: string}) {
        this.data.set('formData.zone', target.value);
        this.setSelectZones();
    }

    // 处理下可用区数据使得多选时，数据可以按照显示的多选顺序来显示
    handleZones(zones: string[]) {
        const availableAreasSecond = this.data.get('availableAreasSecond');
        const newMap = new Map(zones.map((value: string) => ([value, true])));
        return _.map(_.filter(availableAreasSecond, item => newMap.get(item.value)), i => i.value)
    }

    // checkbox 改变，限制最多只能选取三个
    onCheckBoxChange(target: {value: string[]}) {
        target.value = this.handleZones(target.value);
        this.data.set('formData.zoneList', target.value);
        const firstMap = new Map(target.value.map((value: string) => ([value, true])));
        // ha: Array<string[]>
        const {ha, availableAreasSecond} = this.data.get('');
        const remainMap = new Map();
        // 为了可读性，使用两个if方式
        if (target.value.length === 1) {
            _.each(ha, item => {
                _.some(item, i => firstMap.get(i))
                    && _.each(item, i => {
                        // 判断项目中有选中后，单独设置没设置的项为true
                        !firstMap.get(i) && remainMap.set(i, true);
                    });
            });
        }
        if (target.value.length === 2) {
            _.each(ha, item => {
                let count = 0;
                // 先判断是否两个存在的项
                _.each(item, i => {
                    firstMap.get(i) && count++;
                });
                // 如果是存在，单独设置没选中的项为true
                if (count === 2) {
                    _.each(item, i => {
                        !firstMap.get(i) && remainMap.set(i, true);
                    });
                }
            });
        }
        // 判断禁用逻辑：
        // 1.当长度为0时，所有都不禁用
        // 2.当长度为3时，如果在firstMap存在的才不禁用，其余禁用
        // 3.当长度少于3，如果在firstMap或remainMap存在，就不禁用，其余禁用
        _.each(availableAreasSecond, (item: {value: string, disabled?: boolean}, index: number) => {
            const ha = this.data.get('ha');
            const value = item.value;
            const notZoneBool = target.value.length !== 0;
            const maxLength = _.some(ha, haitem => {
                haitem.length === 2 && target.value.length === 2
                    && haitem.sort().toString() === target.value.sort().toString();
            }) ? 2 : 3;
            const maxLengthBool = target.value.length >= maxLength && !firstMap.get(value);
            const checkBool = !(firstMap.get(value) || remainMap.get(value));
            this.data.set(`availableAreasSecond[${index}].disabled`, notZoneBool && (maxLengthBool || checkBool));
        });
        this.setSelectZones();
        this.verifyZone();
    }

    // 手动校验可用区
    verifyZone() {
        const {zoneList, deployType} = this.data.get('formData');
        if (deployType === Deployment.PERFROMANCE) {
            this.data.set('zoneErr', '');
            return Promise.resolve();
        }
        const bool = deployType === Deployment.AVAILABILITY && zoneList.length < 2;
        this.data.set('zoneErr', bool ? '请您选取2-3个可用区' : '');
        return bool ? Promise.reject() : Promise.resolve();
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
