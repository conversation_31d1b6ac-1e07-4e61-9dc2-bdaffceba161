@klass: specification-recommand;

.@{klass} {
    .available-areas {
        padding-top: 5px;

        .s-form-item-label {
            line-height: 17px;
        }

        .s-row {
            display: flex;
            align-items: flex-start;

            .s-radio {
                margin-right: 40px;
            }

            .s-checkbox {
                margin-right: 40px;
            }
        }

        &__checkbox {
            height: 18px;
        }

        .err {
            margin-top: 8px;
        }
    }

    .form-format {
        padding: 24px 0;
    }

    .title {
        font-size: 14px;
        color: #151B26;
        line-height: 24px;
        font-weight: 500;
    }

    &__throughput {
        .s-form-item-help {
            padding-bottom: 0;
        }
        &__help {
            width: 664px;
            span {
                color: #FF9326;
            }
        }
    }

    .unit-desc {
        margin-left: 4px;
        color: var(--descColor3)!important;
    }

    &__specifition__content {
        border-top: 1px #E8E9EB solid;

        &__header {
            padding: 24px 0 16px;

            &_alert {
                margin-top: 16px;
            }
        }

        &__card {
            width: 644px;
            display: flex;
            justify-content: space-between;
        }

        .loading {
            margin: 0 auto;
            text-align: center;
        }
    }
}
