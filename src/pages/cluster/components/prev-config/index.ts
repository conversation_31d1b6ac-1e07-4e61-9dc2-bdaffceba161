/* eslint-disable max-len */
import {Component} from 'san';
import {html, ServiceFactory} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Table} from '@baidu/sui';;
import {ROUTE_PATH} from '@/common/config';
import api from '@/common/client';
import {PaymentType} from '@/common/enums/constant';
import {postPaidParam} from '@/common/config';

const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
import './index.less';

const klass = 'pre-config';

const paymentMap = {
    prepaid: '预付费',
    postpaid: '后付费'
};
export default class PreConfig extends Component {
    static template = html`
    <div class="bms-form-panel">
        <s-append noHighlight label="原套餐配置" />
        <s-table
            columns="{{colunms}}"
            loading="{{loading}}"
            datasource="{{datasource}}"
        >
            <div slot="c-name" class="${klass}__instant">
                <div>
                    <a href="#${ROUTE_PATH.clusterDetailInfo}?name={{row.name}}&clusterId={{row.clusterId}}"
                        class="a-btn" target="_blank">
                        {{row.name}}
                    </a>
                </div>
                <div>{{row.clusterId}}</div>
            </div>
            <div slot="c-price">
                <span class="price-text">￥{{row.price}}</span>/分钟
            </div>
        </s-table>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-table': Table
    };

    initData() {
        return {
            colunms: [
                {
                    label: '集群名称/ID',
                    name: 'name',
                    width: 600
                },
                {
                    label: '付费类型',
                    name: 'payment',
                    width: 350,
                    render: (row) => paymentMap[row.payment]
                },
                {
                    label: '付费单价',
                    name: 'price',
                }
            ],
            datasource: [],
            loading: true
        };
    }

    async attached() {
        const {clusterName, clusterId} = this.data.get('');
        this.watch('priceDataBase', async (value: object) => {
            if (value) {
                await this.onQueryPrice();
                this.data.push('datasource', {name: clusterName, clusterId, payment: 'postpaid', price: this.data.get('oldPrice')});
                this.data.set('loading', false);
            }
        });
    }

    formateXuShangQueryParam(priceData: object) {
        const configsFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1,
            },
            {
                name: 'type',
                value: priceData.xsType?.slice(6).toUpperCase(),
                scale: priceData.count,
            },
            {
                name: priceData.storageMeta.storageType.toLowerCase(),
                value: (priceData.storageMeta.storageSize * priceData.storageMeta.numberOfDisk) + 'G',
                scale: priceData.count,
            },
        ];
        const ipConfigFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1,
            },
            {
                name: 'eip',
                value: priceData.publicIpBandwidth + 'M',
                scale: priceData.count
            }
        ];
        let publicParam;
        if (priceData.payment === 'Postpaid') {
            publicParam = {
                ...postPaidParam,
                region: this.$context.getCurrentRegionId(),
                scene: 'NEW',
            };
        }
        else {
            publicParam = {
                ...prePaidParam,
                duration: priceData.timeLength,
                region: this.$context.getCurrentRegionId(),
                timeUnit: priceData.timeUnit?.toUpperCase(),
                orderType: 'NEW',
            };
        }
        const configs = {
            ...publicParam,
            flavor: configsFlavor
        };
        const ipConfig = {
            ...publicParam,
            flavor: ipConfigFlavor
        };
        return [configs, ipConfig];
    }

    // 查询价格
    async onQueryPrice() {
        const priceDataBase = this.data.get('priceDataBase');
        const priceData = {
            ...priceDataBase,
            'payment': 'Postpaid'
        };
        if (isXushang) {
            const [configs, ipConfig] = this.formateXuShangQueryParam(priceData);
            await api.queryPriceInXS({configs: [configs, ipConfig]}).then((
                result: [{price: number}, {price: number}]) => {
                this.data.set('oldPrice', parseFloat(result[0]?.price + result[1]?.price).toFixed(5));
            });
        }
        else {
            return api.queryPrice({...priceData}).then((
                target: {payment: PaymentType, cluster: number, publicIp: string}) => {
                this.data.set('oldPrice', parseFloat(target?.cluster + target?.publicIp).toFixed(5));
            });
        }
    }
}
