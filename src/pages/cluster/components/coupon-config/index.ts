/**
 * 代金券选择模块
 *
 * @file index.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, Button, Input} from '@baidu/sui';
import {OutlinedCheck, OutlinedClose, OutlinedPlus, OutlinedLink} from '@baidu/sui-icon';
import {AppLegend} from '@baidu/sui-biz';
import {Coupon1} from '@baidu/xicon-san';
import tipSelect from '@/components/tip-select';

import './index.less';

const klass = 'bms-cluster-create-coupon';

const TextMap = {
    title: '代金券配置',
    activeCoupon: '代金券号码：'
};

export default class CouponType extends Component {

    static template = html`
    <div class="${klass}">
        <div class="${klass}__header">
            <div class="${klass}__header_left">
                <s-append noHighlight label="${TextMap.title}">
                    <div slot="extra" class="label-extra">
                        <a s-if="{{!showActiveInput}}" href="javascript:void(0);" on-click="addCoupon">
                            <s-icon-add color="#2468f2" class="${klass}__header_left_icon"/> 激活代金券
                        </a>
                        <a href="{{couponManage}}" target="blank" class="ml20">
                            <s-coupon1 theme="line" color="#2468f2" size="{{16}}" strokeLinejoin="round" class="${klass}__header_left_icon"/> 代金券管理
                        </a>
                    </div>
                </s-append>
            </div>
            <div class="${klass}__header_right">
                <span class="${klass}__header_right_item">
                    <span class="mr10">抵扣金额：</span>
                    <span class="price">¥{{decountPrice}}</span>
                </span>
            </div>
        </div>
        <div class="${klass}__content">
            <s-form
                s-ref="form"
                class="${klass}__content__form"
                rules="{{rules}}"
                data="{{formData}}"
            >
                <s-form-item
                    label="${TextMap.activeCoupon}"
                    prop="couponName"
                    s-if="{{showActiveInput}}"
                >
                    <s-input
                        placeholder="{{placeholder}}"
                        value="{= formData.couponName =}"
                    />
                    <s-button skin="primary" on-click="activeCoupon">
                        <s-icon-confirm class="button-icon" is-button="{{false}}"/>
                    </s-button>
                    <s-button on-click="clearCoupon">
                        <s-icon-cancel class="button-icon" is-button="{{false}}"/>
                    </s-button>
                    <span class="errMsg mt10">{{errMsg}}</span>
                </s-form-item>
                <p class="mb20">代金券（{{couponList.length > 0 ? couponList.length - 1 : 0}}张可用）</p>
                <s-form-item
                    label="产品名称： kafka"
                    prop="coupon"
                >
                    <tip-select
                        placement="left"
                        datasource="{{couponList}}"
                        value="{= formData.coupon =}"
                        on-change="onSelectChange"
                    />
                </s-form-item>
            </s-form>
        </div>
    </div>`;

    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-append': AppLegend,
        'tip-select': tipSelect,
        's-button': Button,
        's-icon-confirm': OutlinedCheck,
        's-icon-cancel': OutlinedClose,
        's-icon-add': OutlinedPlus,
        's-icon-link': OutlinedLink,
        's-input': Input,
        's-coupon1': Coupon1,
    };

    initData() {
        return {
            formData: {
                coupon: '',
                couponName: ''
            },
            couponList: [],
            placeholder: '请输入代金券号码',
            showActiveInput: false,
            couponManage: '/finance/#/finance/coupon/list',
            showError: false
        };
    }

    addCoupon() {
        this.data.set('showActiveInput', true);
    }

    onSelectChange(target: {value: string}) {
        const couponId = target.value;
        this.fire('coupon-change', {couponId: couponId});
    }

    // 激活代金券事件
    activeCoupon() {
        const {couponName} = this.data.get('formData');
        this.fire('active-coupon', {couponName});
    }

    // 关闭代金券输入框
    clearCoupon() {
        this.data.set('formData.couponName', '');
        this.data.set('showActiveInput', false);
        this.fire('clear-coupon', {});
    }

}
