/**
 * 配置推荐卡片
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';

import {Card} from '@baidu/sui';
import Tip from '@/components/tip';

import {PaymentType} from '@/common/enums/constant';
import {UtilHandler} from '../../util/util';

import './index.less';

const klass = 'specification-recommand-card';

export default class extends Component {
    static template = html`
    <template>
        <div
            on-click="selectCard"
            class="{{cardClass}}"
        >
            <s-card
                width="314"
            >
                <div slot="title">
                    <span class="${klass}__title">{{type | formateTitle}}</span>
                    <p class="${klass}__title_desc">{{type | formateDesc}}</p>
                </div>
                <div class="${klass}__cluster-info">
                    <p class="${klass}__cluster-info-item">
                        <span>节点类型：</span>
                        <span>{{clusterObj.nodeName}}</span>
                    </p>
                    <p class="${klass}__cluster-info-item">
                        <span>单区节点数：</span>
                        <span>{{clusterObj.numberOfBrokerNodesPerZone}}</span>
                    </p>
                    <p class="${klass}__cluster-info-item">
                        <span>可支持分区数：</span>
                        <span>{{clusterObj.partitionLimit}}</span>
                    </p>
                    <p class="${klass}__cluster-info-item">
                        <span>
                            磁盘类型：
                            <tip-cmpt s-if="clusterObj.storageMeta.storageType === 'SSD'" type="question">在较低流量场景下，高性能云磁盘即可支撑业务需求。此磁盘类型比增强型SSD优惠约60%</tip-cmpt>
                        </span>
                        <span>{{clusterObj.storageMeta.storageType | getDiskType}}</span>
                    </p>
                    <p class="${klass}__cluster-info-item">
                        <span>
                            磁盘大小：
                            <tip-cmpt type="question">默认以当前流量下保留6小时</tip-cmpt>
                        </span>
                        <span>{{clusterObj.storageMeta.storageSize}}</span>
                    </p>
                </div>
                <div class="${klass}__cluster-price">
                    <p class="price">{{finalPrice | getPrice(payment, timeLength, timeUnit)}}</p>
                    <p class="price_desc" s-if="payment === '${PaymentType.POSTPAID}'">{{finalPrice | getPriceDesc}}</p>
                </div>
            </s-card>
            <div class="no_resource" s-if="!clusterObj.available">
                <img src="{{no_resource}}">
                <p class="text">暂无资源</p>
            </div>
        </div>
    </template>`;

    static components = {
        's-card': Card,
        'tip-cmpt': Tip
    };

    initData() {
        return {
            clusterObj: {},
            type: '',
            active: false,
            no_resource: require('./no-resource.svg')
        }
    }

    static filters: SanFilterProps = {
        formateTitle: UtilHandler.title,
        formateDesc: UtilHandler.desc,
        getPrice: UtilHandler.getPrice,
        getPriceDesc: UtilHandler.getPriceDesc,
        getDiskType: UtilHandler.storageType
    }

    static computed:SanComputedProps =  {
        cardClass(): string {
            const clusterObj = this.data.get('clusterObj');
            const active = this.data.get('active');
            return !clusterObj.available ? `${klass}_not_avaliable ${klass}` : active ? `${klass} ${klass}_active` : `${klass}`
        }
    }

    selectCard() {
        const type = this.data.get('type');
        this.fire('select-card', {type});
    }
}
