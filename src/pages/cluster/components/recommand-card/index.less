@klass: specification-recommand-card;
@unavalibleColor: #B8BABF;

.@{klass} {
    position: relative;

    &:hover {
        border: 1px solid rgba(36,104,242,1);
        border-radius: 6px;
    }

    &_active {
        background: #EEF3FE;
        border: 1px solid rgba(36,104,242,1);
        border-radius: 6px;
    }

    &_not_avaliable {
        background: #F7F7F9;
        border: 1px solid rgba(232,233,235,1);
        border-radius: 6px;
        pointer-events: none;
        
        .no_resource {
            position: absolute;
            top: 0;
            right: 0;

            .img {
                float: right;
            }
            .text {
                position: absolute;
                top: 2px;
                right: 22px;
                color: #FFFFFF;
            }
        }

        .specification-recommand-card {
            &__title {
                color: @unavalibleColor;

                &_desc {
                    color: @unavalibleColor;
                }
            }
            &__cluster-info {
                color: @unavalibleColor;
            }
            
        }

        .specification-recommand-card__cluster-price {
            .price {
                color: #FFB6B3;
            }
        }
    }

    .s-card {
        background-color: transparent;
    }

    &__title{
        font-size: 14px;
    }

    &__title_desc {
        font-size: 12px;
        color: #5C5F66;
        line-height: 20px;
        font-weight: 400;
        margin-top: 4px;
    }

    &__cluster-info {
        padding: 0 0 14px;
        
        &-item {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;

            &:first-child {
                margin-top: 0;
            }
        }
    }

    &__cluster-price {
        border-top: 1px solid rgba(232,233,235,1);
        padding: 12px 0 0;
        vertical-align: middle;

        .price {
            font-size: 20px;
            color: #F33E3E;
            line-height: 28px;
            font-weight: 500;
        }

        .price_desc {
            font-size: 12px;
            color: #5C5F66;
            line-height: 20px;
            font-weight: 400;
            margin-top: 4px;
        }
    }
}