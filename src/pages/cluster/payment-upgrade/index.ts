/**
 * 集群计费变更
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {decorators, html, ServiceFactory, redirect} from '@baiducloud/runtime';
import {Steps, Button} from '@baidu/sui';
import BigNumber from 'bignumber.js';
import {Component} from 'san';
import {ROUTE_PATH, prePaidParam, postPaidParam, SERVICE_TYPE} from '@/common/config';
import CreateNav from '@/components/nav/create';
import {PaymentType} from '@/common/enums/constant';
import api from '@/common/client';
import {formatePrice} from '@/common/util';
import Coupon from '../components/coupon-config';
import PreConfig from '../components/prev-config';
import PriceConfig from '../components/price-config';
import AllConfig from '../components/all-config';
import CreateShopCart from '../components/shop-cart/create-cart';
import {noCoupon} from '../components/base-create';
import {ClusterCreateSuccUrl} from '../components/base-create';
import {XuShangPostPayMessage} from '@/common/util';
const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
import './index.less';

const klass = 'bms-cluster-payment';

@decorators.asPage(ROUTE_PATH.clusterPaymentUpgrage)
class PaymentUpgrade extends Component {
    pageTitle = '计费变更';

    static template = html`
    <div class="${klass}">
        <create-nav
            backUpTitle="集群计费变更"
            back="#${ROUTE_PATH.clusterList}"
        />
        <div class="${klass}__step">
            <s-steps current="{{current}}" type="normal">
                <s-step title="计费变更" />
                <s-step title="确认订单" />
            </s-steps>
        </div>
        <div class="${klass}__create" style="{{current | isShow(1)}}">
            <pre-config clusterName="{{clusterName}}" clusterId="{{clusterId}}" priceDataBase="{{priceDataBase}}"/>
            <price-config s-ref="price-config" on-change="onQueryPrice"/>
        </div>
        <div class="${klass}__order" style="{{current | isShow(2)}}">
            <div class="${klass}__order_panel">
                <all-config
                    clusterId="{{clusterId}}"
                    current="{{current}}"
                    confirmData="{{confirmData}}"
                    clusterPrice="{{clusterPrice}}"
                    publicIpPrice="{{publicIpPrice}}"
                />
                <coupon-config
                    s-if="!${isXushang}"
                    class="coupon-config"
                    couponList="{{couponList}}"
                    errMsg="{{errMsg}}"
                    decountPrice="{{couponValue > totalPrice ? totalPrice : couponValue}}"
                    totalPrice="{{totalPrice}}"
                    finalPrice="{{finalPrice}}"
                    on-active-coupon="activeCoupon"
                    on-coupon-change="onCouponChange"
                    on-clear-coupon="onClearCoupon"
                >
                </coupon-config>
            </div>
        </div>
        <shop-cart
            current="{{current}}"
            payment="{{payment}}"
            timeLength="{{timeLength}}"
            timeUnit="{{timeUnit}}"
            isSubmiting="{{isSubmiting}}"
            on-click="onShopCartClick"
            publicIp="{{publicIp}}"
            totalPrice="{{totalPrice}}"
            finalPrice="{{finalPrice}}"
            clusterPrice="{{clusterPrice}}"
            publicIpPrice="{{publicIpPrice}}"
            decountPrice="{{couponValue > totalPrice ? totalPrice : couponValue}}"
        />
    </div>`;

    static components = {
        's-steps': Steps,
        's-step': Steps.Step,
        's-button': Button,
        'create-nav': CreateNav,
        'shop-cart': CreateShopCart,
        'coupon-config': Coupon,
        'pre-config': PreConfig,
        'price-config': PriceConfig,
        'all-config': AllConfig
    };

    static filters = {
        isShow(current: number, index: number) {
            return current === index ? '' : 'display:none';
        }
    };

    static computed: SanComputedProps = {
        finalPrice() {
            const clusterPrice = new BigNumber(this.data.get('clusterPrice'));
            const publicIpPrice = new BigNumber(this.data.get('publicIpPrice'));
            const couponValue = new BigNumber(this.data.get('couponValue'));
            const res = clusterPrice.plus(publicIpPrice).minus(couponValue).toNumber();
            return res <= 0 ? 0 : formatePrice(res);
        },
        totalPrice() {
            const clusterPrice = new BigNumber(this.data.get('clusterPrice'));
            const publicIpPrice = new BigNumber(this.data.get('publicIpPrice'));
            const total = clusterPrice.plus(publicIpPrice).toNumber();
            return total ?? formatePrice(total);
        },
        clusterId(): string {
            return this.data.get('route.query.clusterId');
        },
        clusterName(): string {
            return this.data.get('route.query.clusterName');
        }
    };

    initData() {
        return {
            current: 1,
            priceDataBase: null,
            couponValue: 0,
        };
    }

    async inited() {
        await this.getClusterDetail();
        await this.onQueryPrice();
    }

    onShopCartClick(type: string) {
        const current = this.data.get('current');
        if (type === 'cancel') {
            redirect(`#${ROUTE_PATH.clusterList}`);
        }
        if (current === 1) {
            const confirmData = this.ref('price-config').getUpgradeData();
            this.data.set('confirmData', {
                clusterId: this.data.get('clusterId'),
                ...confirmData
            });
            this.getCouponList();
            this.data.set('current', 2);
        }
        else if (current === 2) {
            // 数据处理?
            if (type === 'confirm') {
                this.onConfirm();
            }
            else {
                this.data.set('current', 1);
            }
        }
    }

    // 确认订单
    onConfirm() {
        const {confirmData, clusterId, couponIds} = this.data.get('');
        this.data.set('isSubmiting', true);
        api.clusterPaymentUpdate(clusterId, {
            ...confirmData,
            couponIds
        }).then((target: {orderId: string}) => {
        // 提示成功
            this.data.set('isSubmiting', false);
            this.renderOrder(confirmData.payment, target.orderId, 'NEW');
        }).catch(() => this.data.set('isSubmiting', false));
    }

    // 确认订单
    renderOrder(payment: PaymentType, orderId: string, orderType: string) {
        if (isXushang) {
            XuShangPostPayMessage({
                orderId,
                orderType
            });
            return;
        }
        redirect(`${ClusterCreateSuccUrl.Prepaid}${SERVICE_TYPE}&orderType=NEW&orderId=${orderId}`);
    }

    formateXuShangQueryParam(priceData: object) {
        const configsFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1,
            },
            {
                name: 'type',
                value: priceData.xsType?.slice(6).toUpperCase(),
                scale: priceData.count,
            },
            {
                name: priceData.storageMeta.storageType.toLowerCase(),
                value: (priceData.storageMeta.storageSize * priceData.storageMeta.numberOfDisk) + 'G',
                scale: priceData.count,
            },
        ];
        const ipConfigFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1,
            },
            {
                name: 'eip',
                value: priceData.publicIpBandwidth + 'M',
                scale: priceData.count
            }
        ];
        let publicParam;
        if (priceData.payment === 'Postpaid') {
            publicParam = {
                ...postPaidParam,
                region: this.$context.getCurrentRegionId(),
                scene: 'NEW',
            };
        }
        else {
            publicParam = {
                ...prePaidParam,
                duration: priceData.timeLength,
                region: this.$context.getCurrentRegionId(),
                timeUnit: priceData.timeUnit?.toUpperCase(),
                orderType: 'NEW',
            };
        }
        const configs = {
            ...publicParam,
            flavor: configsFlavor
        };
        const ipConfig = {
            ...publicParam,
            flavor: ipConfigFlavor
        };
        return [configs, ipConfig];
    }

    async getClusterDetail() {
        await api.getClusterDetail(this.data.get('clusterId'), {})
            .then((detail: ClusterDetail) => {
                this.data.set('detail', detail);
                const priceDataBase = {
                    publicIpEnabled: detail.publicIpEnabled,
                    publicIpBandwidth: detail.publicIpBandwidth,
                    storageMeta: detail.storageMeta,
                    nodeType: detail.nodeType,
                    count: detail.numberOfBrokerNodes,
                    xsType: detail.nodeSpec
                };
                this.data.set('priceDataBase', priceDataBase);
            });
    }

    // 查询价格
    async onQueryPrice() {
        const ref = this.ref('price-config');
        const upgradeData = ref.getUpgradeData();
        let priceData = {
            ...this.data.get('priceDataBase'),
            'payment': 'Prepaid',
            'timeLength': upgradeData.timeLength,
            'timeUnit': upgradeData.timeUnit,
        };
        this.data.set('timeLength', priceData.timeLength || 1);
        this.data.set('timeUnit', priceData.timeUnit || 'month');
        this.data.set('payment', priceData.payment);
        this.data.set('publicIp', priceData.publicIpBandwidth);
        if (isXushang) {
            const [configs, ipConfig] = this.formateXuShangQueryParam(priceData);
            await api.queryPriceInXS({configs: [configs, ipConfig]}).then((
                result: [{price: number}, {price: number}]) => {
                this.data.set('clusterPrice', result[0].price);
                this.data.set('publicIpPrice', result[1].price);
            });
        }
        else {
            return api.queryPrice({...priceData}).then((
                target: {payment: PaymentType, cluster: number, publicIp: string}) => {
                this.data.set('clusterPrice', target.cluster);
                this.data.set('publicIpPrice', target.publicIp);
            });
        }
    }

    getContent(coupon: {balance: string, productRuleDescription: string, conditionEffectDescription: string}) {
        let content
            = `￥${coupon.balance}` + ' '
            + coupon.productRuleDescription
            + (coupon.conditionEffectDescription
                ? '（' + coupon.conditionEffectDescription + '）'
                : '');
        return content;
    }

    async getCouponList() {
        let param = {
            serviceType: 'KAFKA',
            orderItemPrice: this.data.get('clusterPrice'),
            region: this.$context.getCurrentRegion().id,
            totalPrice: this.data.get('clusterPrice'),
            properties: []
        };
        const result = await api.getCouponList(param);

        let couponList = [noCoupon];
        _.each(result, coupon => {
            couponList.push({
                text: this.getContent(coupon),
                value: coupon.id,
                title: coupon.balance
            });
        });

        this.data.set('couponList', couponList);
    }

    async onCouponChange(target: {couponId: string}) {
        if (target.couponId === '') {
            this.data.set('couponIds', []);
            this.data.set('couponValue', 0);
        }
        else {
            let couponIds = [];

            couponIds.push(target.couponId.toString());
            this.data.set('couponIds', couponIds);

            const couponList = this.data.get('couponList');

            let coupon = _.find(couponList, item => {
                return item.value === target.couponId;
            });

            this.data.set('couponValue', coupon.title);
        }
    }

    onClearCoupon() {
        this.data.set('errMsg', '');
    }

    activeCoupon(target: {couponName: string}) {
        let param = {
            couponName: target.couponName
        };
        this.data.set('errMsg', '');
        api.activeCoupon(param).then((result: NormalObject) => {
            let list = result.result;
            let couponList = [noCoupon];
            _.each(list, coupon => {
                couponList.push({
                    text: '￥' + coupon.balance,
                    value: coupon.id,
                    title: coupon.balance
                });
            });
            this.data.set('couponList', result.result);
            Notification.success('代金券激活成功');
        }).catch((e: Error) => {
            this.data.set('errMsg', '代金券无效');
        });
    }

    onRegionChange() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }
}

export default PaymentUpgrade;
