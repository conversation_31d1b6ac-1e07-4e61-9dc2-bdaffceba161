/**
 * @file index.less
 * <AUTHOR>
 */

@klass: bms-cluster-list;

.@{klass} {

    .s-table-row {

        .instance-editor {
            display: none;
        }

        &:hover {

            .instance-editor {
                display: inline-block;
            }
        }
    }

    .cluster-search-box {
        .s-cascader-column {
            max-height: 300px;
            overflow-y: scroll;
        }

        .s-cascader-multiple-icon {
            width: 16px;
            height: 16px;
        }
    }

    .columns-toggle {
        display: inline-flex;
    }
}

.tag-tip {
    .tag-tip-content {
        max-width: 300px;
        max-height: 200px;
        overflow: auto;
        white-space: nowrap;
    }
}

.tag-text {
    background-color: #f7f7f7;
}

.operation-dropdown{
    .s-button.s-button-skin-normal-stringfy {
        &:hover {
            color: var(--titleColor);
        }

        &.s-button-disabled:hover {
            color: var(--failColor);
        }
    }
}
