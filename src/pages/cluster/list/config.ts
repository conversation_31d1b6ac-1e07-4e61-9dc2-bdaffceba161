import {formatTime} from '@/common/util';
import {ClusterStatus, AllEnum, Payments} from '@/common/enums';

export type ClusterItem = {
    canStart: any;
    canDelete: any;
    canStop: any;
    canUpgrage: any;
    canUpgradePayment: any;
    name: string;
    clusterSid: string;
    clusterId: string;
    version: string;
    status: string;
    logicalZones: string[];
    createTime: string;
    payment: string;
};

const allEnum = AllEnum.toArray();

export const columnsSource = [
    {
        value: 'name',
        text: '集群名称/ID',
        disabled: true
    },
    {
        value: 'status',
        text: '状态'
    },
    {
        value: 'payment',
        text: '付费方式'
    },
    {
        value: 'version',
        text: '版本',
    },
    {
        value: 'logicalZones',
        text: '可用区'
    },
    {
        value: 'createTime',
        text: '创建时间'
    },
    {
        value: 'tags',
        text: '标签'
    },
    {
        value: 'operation',
        text: '操作',
        disabled: true
    }
];
export const columns = [
    {
        name: 'name',
        label: '集群名称/ID',
        fixed: 'left',
        width: 200
    },
    {
        name: 'status',
        label: '状态',
        filter: {
            options: [
                ...allEnum,
                ...ClusterStatus.toArray()
            ],
            value: allEnum[0].value
        },
        width: 130
    },
    {
        name: 'payment',
        label: '付费方式',
        filter: {
            options: [
                ...allEnum,
                ...Payments.toArray()
            ],
            value: allEnum[0].value
        },
        width: 110
    },
    {
        name: 'version',
        label: '版本',
        filter: {},
        width: 90
    },
    {
        name: 'logicalZones',
        label: '可用区',
        width: 150
    },
    {
        name: 'createTime',
        label: '创建时间',
        sortable: true,
        render: (item: ClusterItem) => formatTime(item.createTime),
        width: 150
    },
    {
        name: 'tags',
        label: '集群标签',
        width: 150
    },
    {
        name: 'operation',
        label: '操作',
        fixed: 'right',
        width: 120,
    }
];
