/**
 * 集群列表
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component, defineComponent} from 'san';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {
    Dialog,
    Pagination,
    Table,
    Button,
    Notification,
    Menu,
    Dropdown,
    Loading,
    Tooltip
} from '@baidu/sui';
import {AppListPage, SearchBox, Empty, TableColumnToggle} from '@baidu/sui-biz';
import {OutlinedFilter, OutlinedRefresh, OutlinedDown} from '@baidu/sui-icon';
import {ClusterStatusType, PaymentType} from '@/common/enums/constant';
import api from '@/common/client';
import {TABLE_SUI, PAGER_SUI, ROUTE_PATH, CONFIG_TYPE, TICKET_LINK} from '@/common/config';
import {pickEmpty, getTimer, TagFilter, isOneCloudId} from '@/common/util';
import {ClusterStatus, AllEnum, Payments} from '@/common/enums';
import {VersionTypeEnum} from '@/common/enums/constant';
import CommonTable from '@/components/common-table';
import {renderStatus} from '@/common/html';
import CreateBtn from '@/components/create-btn';
import {FORBID_HANDLER, LIST_OPERATION_SETTING} from '@/common/rules';
import ListTitle from '@/components/list-title';
import ShowEndpoint from '@/pages/cluster/detail/info/show-endpoint';
import EllipsisTip from '@/components/ellipsis-tip';
import HelpDoc from '@/components/help-doc';
import InstantEditor from '@/components/instant-editor';
import TagDialog from './tag-edit';
import {debounce} from '@/common/decorator';
import {UtilHandler} from '../util/util';
import {UpgrageType} from '../util/conf';
import {columns, columnsSource, ClusterItem} from './config';
import './index.less';

const klass = 'bms-cluster-list';

export type versionType = {
    version: string;
    status?: VersionTypeEnum;
    tieredStorageSupported: boolean;
    [prop: string]: any;
};

const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;

const BackSelectPrefix = 's-table';
const allEnum = AllEnum.toArray();
let timeCount: number;
export class BackSelect extends Component {
    static template = html`
    <span>
        <s-loading class="mt5" loading s-if="{{loading}}" size="small" />
        <s-dropdown class="${BackSelectPrefix}-filter" s-else>
            <s-menu
                slot="overlay"
                class="${BackSelectPrefix}-filter-menu"
                on-click="onFilter($event)">
                <s-menu-item
                    s-for="item, index in options"
                    class="${BackSelectPrefix}-filter-item {{value === item.value ? 'item-selected': ''}}"
                    key="{{item.value}}">
                    <span>{{item.text}}</span>
                </s-menu-item>
            </s-menu>
            <s-outlined-filtering />
        </s-dropdown>
    </span>`;

    static components = {
        's-outlined-filtering': OutlinedFilter,
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-loading': Loading
    };

    initData() {
        return {
            value: AllEnum.getValueFromAlias(allEnum[0].alias),
            options: []
        };
    }

    attached() {
        this.getVersions();
    }

    // 获取版本
    getVersions() {
        this.data.set('loading', true);
        api.listAvailableVersion({})
            .then((target: {versions: versionType[]}) => {
                this.data.set('options',
                    [
                        ...allEnum,
                        ..._.map(target.versions, (i: versionType) => ({
                            value: i.version,
                            text: i.version
                        }))
                    ]);
            })
            .finally(() => this.data.set('loading', false));
    }

    // 过滤
    onFilter(target: {value: string}) {
        this.data.set('value', target.value);
        this.fire('filter', {version: target.value});
    }
}

@decorators.asPage(ROUTE_PATH.clusterList)
@decorators.withSidebar({active: ROUTE_PATH.clusterList})
export default class ClusterList extends CommonTable {
    static template = html`
    <div class="${klass} bms-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <list-title
                    title="集群列表"
                    type="${CONFIG_TYPE.VIP}"
                    hasOnlineAlert
                />
            </div>
            <div slot="bulk">
                <create-btn text="创建集群" on-click="onCreate" />
                <s-button
                    s-if="{{!isOneCloud}}"
                    class="ml8"
                    disabled="{= !selection.selectedIndex.length =}"
                    on-click="onEditTag"
                >编辑标签</s-button>
            </div>
            <div slot="filter">
                <s-searchbox
                    class="cluster-search-box"
                    placeholder="{{searchbox.placeholder}}"
                    on-keywordTypeChange="onKeywordTypeChange"
                    text-datasource="{{searchbox.textDataSource}}"
                    value="{= searchbox.keyword =}"
                    keyword-type="{= searchbox.keywordType =}"
                    datasource="{{searchbox.keywordTypes}}"
                    on-search="onSearch"
                    width="170"
                />
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
                <s-table-column-toggle
                    datasource="{{columnsSource}}"
                    value="{{columnsSelected}}"
                    on-change="tableColumnSelect"
                    class="columns-toggle ml5"
                />
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                selection="{{selection}}"
                on-selected-change="onSelectChange"
                on-sort="onSort"
                on-filter="onFilter">
                <div slot="c-name" class="${klass}__instant">
                    <div>
                        <a href="#${ROUTE_PATH.clusterDetailInfo}?name={{row.name}}&clusterId={{row.clusterId}}"
                            class="a-btn">
                            {{row.name}}
                        </a>
                        <instant-editor
                            value="{{row.name}}"
                            info="{{rowIndex}}"
                            disabled="{{row.status !== 'ACTIVE'}}"
                            request="{{editName}}"
                            check="{{check}}"
                            placeholder="请输入集群名称"
                            desc="不超过65个字符，仅支持数字/字母/特殊符号(_/-.)"
                        />
                    </div>
                    <div>{{row.clusterId}}</div>
                </div>
                <div slot="c-payment" class="${klass}__instant">
                    <div>{{row.payment | filterPayment}}</div>
                    <div s-if="row.payment === 'Prepaid'">{{row.expiration}}</div>
                </div>
                <back-select
                    slot="h-version-filter"
                    on-filter="onVersionFilter"
                />
                <div slot="c-status">{{row.status | filterStatus | raw}}</div>
                <div slot="c-tags">
                    <span class="tag-text" s-if="{{!row.tags || row.tags.length < 1}}">-</span>
                    <s-tooltip s-else class="tag-tip">
                        <div slot="content" class="tag-tip-content">
                            {{row | tagTip | raw}}
                        </div>
                        <div class="tag-text">{{row | tagText | raw}}</div>
                    </s-tooltip>
                </div>
                <div slot="c-operation">
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onIpGet(row)">
                        接入点
                    </s-button>
                    <s-dropdown class="operation-dropdown">
                        <s-menu slot="overlay">
                            <s-menu-item  s-for="n,index in data" key="{{index}}">
                                <s-tooltip content="{{n | tipText(row)}}" placement="left">
                                    <s-button
                                        on-click="handleMenuItemClick(row, n)"
                                        disabled="{{n | filterDisable(row)}}"
                                        skin="normal-stringfy"
                                        style="padding: 0;"
                                    >
                                        {{n}}
                                    </s-button>
                                </s-tooltip>
                            </s-menu-item>
                        </s-menu>
                        <s-button skin="stringfy">更多 <s-icon-down /></s-button>
                    </s-dropdown>
                </div>
                <div slot="empty">
                    <s-empty vertical on-click="onCreate"/>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    initData() {
        return {
            searchbox: {
                keyword: '',
                keywordType: ['NAME'],
                keywordTypes: [
                    {
                        value: 'NAME',
                        text: '集群名称'
                    },
                    {
                        value: 'ID',
                        text: '集群ID'
                    }
                ],
                allTextDataSource: []
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: columns
            },
            selection: {
                mode: 'multi',
                selectedIndex: []
            },
            pager: {...PAGER_SUI},
            editName: this.editName.bind(this),
            check: this.check.bind(this),
            isOneCloud: isOneCloudId(),
            data: ['配置变更', '计费变更', '监控信息', '提交工单', '重启集群', '删除集群', '停止集群', '启动集群'],
            regions: window._regions_,
            isEdgeRegion: false,
            columnsSource: columnsSource,
            columnsSelected: columnsSource.map(item => item.value),
        };
    }

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        'ellipsis-tip': EllipsisTip,
        'help-doc': HelpDoc,
        'create-btn': CreateBtn,
        'back-select': BackSelect,
        'instant-editor': InstantEditor,
        's-tooltip': Tooltip,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty,
        'list-title': ListTitle,
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-icon-down': OutlinedDown,
        's-loading': Loading,
        's-table-column-toggle': TableColumnToggle,
    };

    static filters: SanFilterProps = {
        filterStatus(status: ClusterStatusType) {
            let temp = renderStatus(ClusterStatus.fromValue(status));
            if (_.includes([
                ClusterStatusType.DEPLOYING,
                ClusterStatusType.UPDATING,
                ClusterStatusType.REBOOTING,
                ClusterStatusType.PRE_REBOOTING,
                ClusterStatusType.PRE_UPDATING
            ], status)) {
                temp += html`<div class="desc">预计需要10~30分钟</div>`;
            }
            return temp;
        },
        filterPayment(payment: PaymentType) {
            switch (payment) {
                case PaymentType.PREPAID:
                    return '预付费';
                case PaymentType.POSTPAID:
                    return '后付费';
                default:
                    return '-';
            }
        },
        filterDisable(type: string, row: ClusterItem) {
            switch (type) {
                case '计费变更':
                    return !row.canUpgradePayment;
                case '配置变更':
                    return !row.canUpgrage;
                case '重启集群':
                    return !row.canUpgrage;
                case '删除集群':
                    return !row.canDelete;
                case '停止集群':
                    return !row.canStop;
                case '启动集群':
                    return !row.canStart;
            }
        },
        tipText(type: string, row: ClusterItem) {
            switch (type) {
                case '计费变更':
                    return !row.canUpgradePayment ? '仅服务中的后付费集群支持计费变更' : '';
                case '删除集群':
                    return !row.canDelete
                        // eslint-disable-next-line max-len
                        ? `集群删除，请<a s-if="!${isXushang}" href="${TICKET_LINK}" target="blank">提交工单</a><span s-else>提交工单</span>`
                        : '';
            }
            return '';
        },
        ...TagFilter
    };

    async attached() {
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        this.data.set('isEdgeRegion', isEdgeRegion);
        if (isEdgeRegion) {
            await this.getEdgeZones();
        }
        this.getKAFKATags();
        await this.getComList();
        timeCount = getTimer(() => this.getComList(false));
    }

    async getEdgeZones() {
        await api.getEdgeNodeList({})
            .then((data: {regions: object[]}) => {
                this.data.set('regions', data.regions);
                window._regions_ = [...data.regions];
            });
    }

    detached() {
        clearInterval(timeCount);
    }

    onRefresh() {
        this.getComList();
        clearInterval(timeCount);
        timeCount = getTimer(() => this.getComList(false));
    }

    /**
     * 获取表格，重写是为了支持自动刷新不展示loading
     */
    async getComList(loadStatus = true) {
        this.data.set('table.loading', loadStatus);
        this.data.set('table.error', false);
        // 清除多选
        this.data.set('selection.selectedIndex', []);
        try {
            await this.getTableList();
            this.data.set('table.loading', false);
        }
        catch (error) {
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    /**
     * 分页，重写是为了支持换页后自动刷新定时器重置
     * @param {Number} page page
     * @param {Number} pageSize pageSize
     */
    onPageChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', args.value.page);
        this.onRefresh();
    }

    /**
     * 分页 pageSize 设置，重写是为了支持改变页面大小后自动刷新定时器重置
     */
    onPageSizeChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', 1);
        this.onRefresh();
    }

    /**
     * 搜索，重写是为了支持搜索后自动刷新定时器重置
     */
    onSearch(args?: {value: string}): void {
        this.data.set('pager.page', 1);
        this.onRefresh();
    }

    onSelectChange(event: {value: { selectedIndex: number[], selectedItems: Array<Object>}}) {
        this.data.set('selection.selectedIndex', event.value.selectedIndex);
    }

    async onRegionChange(event: {id: string}) {
        this.data.set('pager.page', 1);
        event.id === 'edge' ? await this.getEdgeZones() : '';
        this.onRefresh();
    }

    // 获取标签列表
    async getKAFKATags() {
        const result = await api.getSearchTagList({
            serviceType: 'KAFKA',
            region: [this.$context.getCurrentRegionId()]
        });
        let allTextDataSource = [{text: '所有值', value: '所有值'}];
        result.forEach(item => {
            const data = {
                text: item.tagValue || '空值',
                value: item.tagValue|| ''
            };
            const index = _.findIndex(allTextDataSource, i => i.value === data.value);
            index === -1 && allTextDataSource.push(data);
        });

        this.data.set('searchbox.allTextDataSource', allTextDataSource);
        let tags = _.groupBy(result, 'tagKey');
        _.each(tags, (tag, key) => {
            tags[key] = _.map(tag, item => {
                return {name: item.tagValue || '空值', value: item.tagValue};
            });
            tags[key].unshift({name: '所有值', value: '@@@'});
        });
        let tagKeys = _.map(_.keys(tags), key => {
            if (key === '@@@') {
                return {text: '(全部)', value: '@@@'};
            }
            else if (key === '') {
                return {text: '(无标签)', value: ''};
            }
            return {
                text: key,
                value: key,
                textDataSource: tags[key].map(i => ({
                    text: i.name,
                    value: i.name
                }))
            };
        });
        tagKeys.unshift({text: '(无标签)', value: '', textDataSource: result.map(i => ({
            text: i.name,
            value: i.name
        }))});
        tagKeys.unshift({text: '(全部)', value: '@@@', textDataSource: result.map(i => ({
            text: i.name,
            value: i.name
        }))});
        this.data.push('searchbox.keywordTypes', {
            text: '标签',
            value: 'TAG',
            children: tagKeys
        });
        return tags;
    }

    // search-box 的 keyword type改变时调用
    onKeywordTypeChange({value}) {
        let textDataSource = [];
        if (Array.isArray(value) && value.indexOf('TAG') > -1) {
            let keywordTypes = this.data.get('searchbox.keywordTypes');
            let tags = _.find(keywordTypes, item => item.value === 'TAG').children;
            if (value[1] === '@@@') {
                textDataSource = this.data.get('searchbox.allTextDataSource');
            }
            else if (value[1] === '') {
                textDataSource = [{text: '空值', value: ''}];
            }
            else {
                textDataSource = _.find(tags, tag => tag.text === value[1]).textDataSource;
            }
            this.data.set('searchbox.textDataSource', textDataSource);
        }
        else if (value.indexOf('TAG') === -1) {
            this.data.merge('searchbox', {
                keywordType: [value[0]],
                keyword: ''
            });
            this.data.set('searchbox.textDataSource', textDataSource);
            this.onRefresh();
        }
    }

    // 生成搜索关键字
    createKeywordParams() {
        let {keyword, keywordType} = this.data.get('searchbox');
        if (keywordType.length > 1) {
            keyword = keyword === '所有值' ? '@@@' : keyword === '空值' ? '' : keyword;
        }
        keywordType = keywordType[1];
        return {
            keywordType,
            keyword
        };
    }

    async getTableList() {
        const {searchbox, pager, status, orderBy, order, version, payment} = this.data.get('');
        const {keywordType, keyword} = this.createKeywordParams();
        let param = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            status,
            payment,
            order,
            orderBy,
            version,
            keywordType: searchbox.keywordType[0],
        });
        const params = {
            ...param,
            subKeywordType: keywordType || '',
            keyword: keyword || ''
        };
        const {totalCount, result} = await api.listCluster(params);
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', _.map(result as ClusterItem[], item => ({
            ...item,
            canUpgrage: FORBID_HANDLER.checkClusterUpgrage(item.status),
            showStart: LIST_OPERATION_SETTING.showStart(item.status),
            canUpgradePayment: FORBID_HANDLER.checkPaymentUpgrage(item),
            logicalZones: this.filterLogicalZone(item.logicalZones),
        })));
    }

    filterLogicalZone(logicalZones: string[]) {
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        const regions = window._regions_;
        if (isEdgeRegion && logicalZones.length) {
            let res = '';
            regions?.forEach(item => {
                item.cities?.forEach(i => {
                    i.serviceProviders?.forEach(j => {
                        if (j.regionId === logicalZones[0]) {
                            res = item.name + '-' + i.name + '-' + j.name;
                        }
                    });
                });
            });
            return res || logicalZones[0];
        }
        else {
            return UtilHandler.logicalZones(logicalZones);
        }
    };

    handleMenuItemClick(row: ClusterItem, n: string) {
        const {name, clusterId} = row;
        switch (n) {
            case '配置变更':
                // 配置变更
                redirect(`#${ROUTE_PATH.clusterUpgrage}?clusterId=${row.clusterId}`);
                break;
            case '计费变更':
                // 配置变更
                redirect(`#${ROUTE_PATH.clusterPaymentUpgrage}?clusterId=${row.clusterId}&clusterName=${row.name}`);
                break;
            case '监控信息':
                // 监控信息
                window.open(`#${ROUTE_PATH.clusterDetailMonitor}?name=${name}&clusterId=${clusterId}`, '_blank');
                break;
            case '提交工单':
                // 提交工单
                window.open(TICKET_LINK, '_blank');
                break;
            case '重启集群':
                this.restart(row);
                break;
            case '删除集群':
                this.onDelete(row);
                break;
            case '停止集群':
                this.onStop(row);
                break;
            case '启动集群':
                this.onStart(row);
                break;
        }
    }

    // 创建按钮点击
    async onCreate() {
        redirect(`#${ROUTE_PATH.clusterCreate}`);
    }

    @debounce(500)
    async onIpGet(row: ClusterItem) {
        const {clusterId, name: clusterName} = row;
        await api.getAccessPoints(clusterId, {})
            .then((target: {
                kafkaEndpoints: Array<{securityProtocol: string, endpoints: string}>;
                sslEnabled: boolean;
            }) => {
                const dialog = new ShowEndpoint({data: {...target, clusterId, clusterName}});
                dialog.attach(document.body);
            });
    }

    // 变更
    onUpgrage(row: ClusterItem) {
        redirect(`#${ROUTE_PATH.clusterUpgrage}?clusterId=${row.clusterId}`);
    }

    async onStart(row: ClusterItem) {
        Dialog.warning({
            content: defineComponent({
                template: html`
                <div>
                    <div>集群：${row.name}</div>
                    <div>您确定启动此集群吗？</div>
                </div>`
            }),
            okText: '确定',
            onOk: async () => {
                await api.startCluster(row.clusterId, {});
                Notification.success(`集群${row.name}启动成功`);
                this.onRefresh();
            }
        });
    }

    async onStop(row: ClusterItem) {
        Dialog.warning({
            content: defineComponent({
                template: html`
                <div>
                    <div>集群：${row.name}</div>
                    <div>您确定停止此集群吗？</div>
                </div>`
            }),
            okText: '确定',
            onOk: async () => {
                await api.stopCluster(row.clusterId, {});
                Notification.success(`集群${row.name}停止成功`);
                this.onRefresh();
            }
        });
    }

    async restart(row: ClusterItem) {
        Dialog.warning({
            content: defineComponent({
                template: html`
                <div>
                    <div>集群：${row.name}</div>
                    <div>您确定重启此集群吗？</div>
                </div>`
            }),
            okText: '确定',
            onOk: async () => {
                await api.restartCluster(row.clusterId, {});
                Notification.success(`集群${row.name}重启成功`);
                this.onRefresh();
            }
        });
    }

    // 删除
    onDelete(row: ClusterItem) {
        Dialog.warning({
            content: defineComponent({
                template: html`
                <div>
                    <div>集群：${row.name}</div>
                    <div>删除后集群无法恢复，您确定删除此集群吗？</div>
                </div>`
            }),
            okText: '确定',
            onOk: async () => {
                await api.deleteCluster(row.clusterId, {});
                Notification.success(`集群${row.name}删除成功`);
                this.onRefresh();
            }
        });
    }

    // 版本过滤
    onVersionFilter(target: {version: string}) {
        this.data.set('version', target.version);
        this.onRefresh();
    }

    // 名称输入校验
    check(name: string, callback: (str: string) => void) {
        if (!name) {
            return callback('请输入');
        }
        if (name.length > 65) {
            return callback('不能超过65个字符');
        }
        if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(name)) {
            return callback('输入字符格式有误');
        }
        callback('');
    }

    // 名称编辑
    async editName(name: string, rowIndex: number) {
        const item = this.data.get(`table.datasource[${rowIndex}]`);
        await api.updateCluster(item.clusterId, {
            name,
            type: UpgrageType.UPDATE_CLUSTER_NAME
        });
        Notification.success('修改成功');
        this.data.set(`table.datasource[${rowIndex}].name`, name);
    }

    // 编辑标签
    onEditTag() {
        const {datasource} = this.data.get('table');
        const {selectedIndex} = this.data.get('selection');
        const selects = datasource.filter((item, index) => selectedIndex.includes(index));
        let data = {
            tags: [],
            mode: 'single',
            originTags: [],
        };

        if (selects.length === 1) {
            data.tags = selects[0].tags;
            data.params = [
                {
                    id: selects[0].clusterId,
                    resourceId: selects[0].clusterSid,
                    tags: selects[0].tags
                }
            ];
        } else {
            data.mode = 'multi';
            data.originTags = selects.reduce((tags, item) => {
                if (item.tags) {
                    return tags.concat(item.tags);
                }
                return tags;
            }, []);
            data.params = selects.map(item => ({
                id: item.clusterId,
                resourceId: item.clusterSid,
                tags: item.tags
            }));
        };

        const dialog = new TagDialog({
            data,
        });
        dialog.on('success', () => {
            this.data.merge('searchbox', {
                keyword: '',
                keywordType: ['NAME'],
                keywordTypes: [
                    {
                        value: 'NAME',
                        text: '集群名称'
                    },
                    {
                        value: 'ID',
                        text: '集群ID'
                    }
                ],
                allTextDataSource: []
            });
            this.getKAFKATags();
            this.onRefresh();
        });
        dialog.attach(document.body);
    }

    tableColumnSelect(e: {value: string[]}) {
        this.data.set('columnsSelected', e.value);
        this.setTableColumn(e.value);
    }

    setTableColumn(value: any) {
        let temp = _.filter(columns, item => value.indexOf(item.name) > -1);
        const {status, orderBy, order, payment} = this.data.get('');
        if (order) {
            temp = _.map(temp, item => {
                if (item.name === orderBy) {
                    return {
                        ...item,
                        order: order,
                    };
                }
                return {...item};
            });
        }
        if (payment) {
            temp = _.map(temp, item => {
                if (item.name === 'payment') {
                    return {
                        ...item,
                        filter: {
                            options: [
                                ...allEnum,
                                ...Payments.toArray()
                            ],
                            value: payment
                        }
                    };
                }
                return {...item};
            });
        }
        if (status) {
            temp = _.map(temp, item => {
                if (item.name === 'status') {
                    return {
                        ...item,
                        filter: {
                            options: [
                                ...allEnum,
                                ...ClusterStatus.toArray()
                            ],
                            value: status
                        },
                    };
                }
                return {...item};
            });
        }
        this.data.set('table.columns', temp);
    }
}
