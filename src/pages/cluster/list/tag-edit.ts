/**
 * 编辑标签
 *
 * @file index.js
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import _ from 'lodash';
import {Component} from 'san';
import {Dialog, Notification, Form} from '@baidu/sui';
import {TagEditPanel} from '@baiducloud/tag-sdk/san';
import {TagSDK} from '@baiducloud/tag-sdk';
import HttpClient from '@baiducloud/httpclient';
import {DOCS_LINK} from '@/common/config';
import api from '@/common/client';

class TagDialog extends Component {
    static template = html`
    <template>
        <s-dialog
            open="{= open =}"
            title="编辑标签"
            on-confirm="onConfirm"
            loadingAfterConfirm="{{false}}"
        >
            <s-form label-align="left">
                <s-form-item>
                    <tag-edit-panel
                        s-ref="tagPanel"
                        instances="{{defaultInstances}}"
                        sdk="{{tagSDK}}"
                        helpDocUrl="${DOCS_LINK.tagHelp}"
                    />
                </s-form-item>
            </s-form>
        </s-dialog>
    </template>
    `;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        'tag-edit-panel': TagEditPanel,
    };

    initData() {
        return {
            open: true,
            tagSDK: new TagSDK({
                serviceType: 'KAFKA',
                client: new HttpClient({}, {
                    getCsrfToken() {
                        return this.$cookie.get('bce-user-info');
                    },
                    getCurrentRegion() {
                        return window.$context.getCurrentRegion();
                    }
                }),
                context: window.$context
            }),
            mode: 'multi',
            id: ''
        };
    }

    static computed = {
        defaultInstances() {
            const tags = this.data.get('tags');
            const mode = this.data.get('mode');
            // 批量编辑时，集群原绑定的标签集合。
            const originTags = this.data.get('originTags');
            let instances = [
                {tags},
            ];
            if (mode === 'multi') {
                // 添加模式下
                instances.push({
                    tags: originTags
                });
            }
            return instances;
        }
    }

    async onConfirm() {
        const tags = await this.ref('tagPanel').getTags();
        const {params = []} = this.data.get('');
        const single = params.length <= 1;
        const computeParams = {
            resources: params.map(item => ({
                id: item.id,
                resourceId: item.resourceId,
                tags: single ? tags : item.tags.concat(tags)
            })),
            insertTags: tags,
        };
        await api.KAFKATagUpdate(computeParams);
        Notification.success('编辑成功！');
        this.fire('success');
        this.onClose();
    }

    onClose() {
        this.data.set('open', true);
        this.dispose();
    }
}

export default TagDialog;
