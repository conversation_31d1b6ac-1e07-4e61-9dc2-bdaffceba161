/**
 * @file index.less
 * <AUTHOR>
 */

@klass: bms-cluster-create;

.@{klass} {
    display: block;
    height: 100%;
    padding-bottom: 80px;
    background: var(--backgroundColor);

    &__create {
        width: 100%;
        min-height: 100%;
    }

    &__step {
        z-index: 1;
        width: 600px;
        margin: 24px auto;
        position: relative;

        .s-steps {

            .s-step {

                .s-step-line-real{
                    background-color: var(--stepLineBackgroundColor) !important;
                }

                .s-step-content-icon {
                    background: var(--backgroundColor) !important;
                }
                .s-step-content-title {
                    background: var(--backgroundColor) !important;
                }
            }
        }
    }

    &__order {
        width: 100%;
        height: auto;

        &_panel {
            min-height: 100%;
        }
    }

    .config-option {
        .s-option {
            height: auto;
            padding: 8px 12px;
            width: 300px;
            overflow-x: auto;
            div {
                display: block!important;
                height: auto;
                line-height: 20px;
                font-size: 12px;
            }
            .label {
                color: #151B26;
                font-weight: 500;
            }
            .desc {
                color: #5C5F66;
                font-weight: 400;
            }
        }
    }

    .bms-cluster-create-tag {
        .tag-edit-panel {
            width: 100%;
            .tag-text-tip {
                width: 100%;
            }
        }
    }

    .error {
        color: #d0021b;
    }
}
