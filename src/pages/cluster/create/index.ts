/**
 * 集群创建
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html, ServiceFactory, redirect} from '@baiducloud/runtime';
import {Steps, Button} from '@baidu/sui';
import BigNumber from 'bignumber.js';

import {ROUTE_PATH, prePaidParam, postPaidParam} from '@/common/config';
import CreateNav from '@/components/nav/create';
import {ClusterRefType, PaymentType} from '@/common/enums/constant';
import api from '@/common/client';
import {formatePrice} from '@/common/util';

import {Region} from '../components/region';
import OrderItem from '../components/order-item';
import {ClusterConfig} from '../components/cluster-config';
import NetWork from '../components/network-config';
import Access from '../components/access-config';
import NodeConfig from '../components/node-config';
import DiskConfig from '../components/disk-config';
import Coupon from '../components/coupon-config';
import CreateShopCart from '../components/shop-cart/create-cart';
import TagConfig from '../components/tag-config';

import BaseCreate, {noCoupon} from '../components/base-create';
const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
import './index.less';
import {versionType} from '../list';

const klass = 'bms-cluster-create';

const Refs = Object.values(ClusterRefType);

type NodeClass = Region | ClusterConfig | NetWork | NodeConfig | DiskConfig;

@decorators.asPage(ROUTE_PATH.clusterCreate)
export default class extends BaseCreate {
    pageTitle = '创建集群';

    static template = html`
    <div class="${klass}">
        <create-nav
            backUpTitle="创建集群"
            back="#${ROUTE_PATH.clusterList}"
        />
        <div class="${klass}__step">
            <s-steps current="{{current}}" type="normal">
                <s-step title="信息配置" />
                <s-step title="确认订单" />
            </s-steps>
        </div>
        <div class="${klass}__create" style="{{current | isShow(1)}}">
            <region-config
                s-ref="${ClusterRefType.REGION}"
                on-price-config-change="onQueryPrice"
            />
            <cluster-config
                s-ref="${ClusterRefType.CLUSTER}"
                isUpgrading="{{true}}"
                on-config-change="handleConfigChange"
                on-version-change="handleVersionChange"
            />
            <node-config
                s-ref="${ClusterRefType.NODE}"
                payment="{{payment}}"
                timeLength="{{timeLength}}"
                timeUnit="{{timeUnit}}"
                isUpgrading="{{true}}"
                flavorloading="{{flavorloading}}"
                on-price-config-change="onQueryPrice"
                on-select-zones="onSeletZones"
                on-deploy-change="onDeployChange"
                on-flavor-loaded="onFlavorLoaded"
                on-payload-change="onPayloadChange"
                on-singleNumberTextNumber-change="onSingleNumberTextNumberChange"
                on-storageMetaSource-apply="onStorageMetaSourceApply"
            />
            <disk-config
                s-ref="${ClusterRefType.DISK}"
                cdsArr="{{cdsArr}}"
                singleNumberTextNumber="{{singleNumberTextNumber}}"
                flavorloading="{{flavorloading}}"
                payload="{{payload}}"
                isUpgrading="{{true}}"
                applyData="{{applyData}}"
                on-price-config-change="onQueryPrice"
            />
            <network-config
                s-ref="${ClusterRefType.NETWORK}"
                singleNumberTextNumber="{{singleNumberTextNumber}}"
                on-price-config-change="onQueryPrice"
                on-publicIp-change="onPublicIpChange"
                on-privateIp-change="onPrivateIpChange"
                isUpgrading="{{true}}"
            />
            <access-config
                s-ref="${ClusterRefType.ACCESS}"
                isUpgrading="{{true}}"
            />
            <tag-config
                s-ref="${ClusterRefType.TAG}"
                s-if="{{!isOneCloud}}"
            />
        </div>
        <div class="${klass}__order" style="{{current | isShow(2)}}">
            <div class="${klass}__order_panel">
                <order-item
                    s-for="item in orderItems"
                    title="{{item.title}}"
                    type="{{item.type}}"
                    list="{{item.list}}"
                    payment="{{payment}}"
                    timeLength="{{timeLength}}"
                    timeUnit="{{timeUnit}}"
                    isShowNetworkPrice="{{item.isShowNetworkPrice}}"
                    clusterPrice="{{clusterPrice}}"
                    publicIpPrice="{{publicIpPrice}}"
                    publicIp="{{publicIp}}"
                />
                <coupon-config
                    s-if="showCoupon && !${isXushang}"
                    couponList="{{couponList}}"
                    errMsg="{{errMsg}}"
                    decountPrice="{{couponValue > totalPrice ? totalPrice : couponValue}}"
                    totalPrice="{{totalPrice}}"
                    finalPrice="{{finalPrice}}"
                    on-active-coupon="activeCoupon"
                    on-coupon-change="onCouponChange"
                    on-clear-coupon="onClearCoupon"
                >
                </coupon-config>
            </div>
        </div>
        <shop-cart
            current="{{current}}"
            payment="{{payment}}"
            timeLength="{{timeLength}}"
            timeUnit="{{timeUnit}}"
            isSubmiting="{{isSubmiting}}"
            on-click="onShopCartClick"
            publicIp="{{publicIp}}"
            totalPrice="{{totalPrice}}"
            finalPrice="{{finalPrice}}"
            clusterPrice="{{clusterPrice}}"
            publicIpPrice="{{publicIpPrice}}"
            decountPrice="{{couponValue > totalPrice ? totalPrice : couponValue}}"
        />
    </div>`;

    static components = {
        's-steps': Steps,
        's-step': Steps.Step,
        's-button': Button,
        'create-nav': CreateNav,
        'network-config': NetWork,
        'cluster-config': ClusterConfig,
        'node-config': NodeConfig,
        'disk-config': DiskConfig,
        'region-config': Region,
        'access-config': Access,
        'shop-cart': CreateShopCart,
        'order-item': OrderItem,
        'coupon-config': Coupon,
        'tag-config': TagConfig
    };

    static computed: SanComputedProps = {
        finalPrice() {
            const clusterPrice = new BigNumber(this.data.get('clusterPrice'));
            const publicIpPrice = new BigNumber(this.data.get('publicIpPrice'));
            const couponValue = new BigNumber(this.data.get('couponValue'));
            const res = clusterPrice.plus(publicIpPrice).minus(couponValue).toNumber();
            return res <= 0 ? 0 : formatePrice(res);
        },
        totalPrice() {
            const clusterPrice = new BigNumber(this.data.get('clusterPrice'));
            const publicIpPrice = new BigNumber(this.data.get('publicIpPrice'));
            const total = clusterPrice.plus(publicIpPrice).toNumber();
            return total ?? formatePrice(total);
        }
    };

    attached() {
        this.onDeployChange(
            (this.ref(ClusterRefType.NODE) as NodeConfig).data.get('formData.deployType'));
    }

    handleVersionChange(selectVersion: versionType) {
        const refDiskConfig = this.ref(ClusterRefType.DISK) as DiskConfig;
        refDiskConfig && refDiskConfig.handleVersionChange(selectVersion);
    }

    handleConfigChange(e: {value: string}) {
        const refDiskConfig = this.ref(ClusterRefType.DISK) as DiskConfig;
        refDiskConfig && refDiskConfig.handleConfigChange(e);
    }

    onPayloadChange(target: {payloadType: string}) {
        this.data.set('payload', target.payloadType);
    }

    onFlavorLoaded(target: {cdsArr: any[], singleNumberTextNumber: number, payloadType: string}) {
        this.data.set('cdsArr', [...target.cdsArr]);
        this.data.set('singleNumberTextNumber', target.singleNumberTextNumber);
        this.data.set('payload', target.payloadType);
        this.data.set('flavorloading', false);
    }

    onStorageMetaSourceApply(target: {storageMetaSource: any, storageType: string}) {
        this.data.set('applyData', {
            storageMetaSource: target.storageMetaSource,
            storageType: target.storageType
        });
    }

    onSingleNumberTextNumberChange(target: {singleNumberTextNumber: number}) {
        this.data.set('singleNumberTextNumber', target.singleNumberTextNumber);
    }

    // 确认订单
    onConfirm() {
        const confirmData = {
            ...this.data.get('confirmData'),
            couponIds: this.data.get('couponIds')
        };
        this.data.set('isSubmiting', true);
        api.createCluster(confirmData).then((target: {orderId: string, clusterId: string}) => {
            // 提示成功
            this.data.set('isSubmiting', false);
            this.renderOrder(confirmData.payment, target.orderId, 'NEW');
        }).catch(() => this.data.set('isSubmiting', false));
    }

    checkHasEmpty(priceData): boolean {
        let flag = false;
        if (!priceData || Object.keys(priceData).length === 0) {
            return true;
        }
        for (let key of Object.keys(priceData)) {
            if (!/publicIpEnabled|publicIpBandwidth/.test(key) && !priceData[key]) {
                flag = true;
            }
            if (typeof priceData[key] === 'object') {
                flag = this.checkHasEmpty(priceData[key]) || flag;
            }
        }
        return flag;
    }

    formateXushangQueryPirce(priceData: object) {
        const configsFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1,
            },
            {
                name: 'type',
                value: priceData.xsType?.slice(6).toUpperCase(),
                scale: priceData.count,
            },
            {
                name: priceData.storageMeta.storageType.toLowerCase(),
                value: (priceData.storageMeta.storageSize * priceData.storageMeta.numberOfDisk) + 'G',
                scale: priceData.count,
            },
        ];
        const ipConfigFlavor = [
            {
                name: 'subServiceType',
                value: 'kafkacluster',
                scale: 1,
            },
            {
                name: 'eip',
                value: priceData.publicIpBandwidth + 'M',
                scale: priceData.count
            }
        ];
        let publicParam;
        if (priceData.payment === 'Postpaid') {
            publicParam = {
                ...postPaidParam,
                region: this.$context.getCurrentRegionId(),
                scene: 'NEW',
            };
        }
        else {
            publicParam = {
                ...prePaidParam,
                duration: priceData.timeLength,
                region: this.$context.getCurrentRegionId(),
                timeUnit: priceData.timeUnit?.toUpperCase(),
                orderType: 'NEW',
            };
        }
        const configs = {
            ...publicParam,
            flavor: configsFlavor
        };
        const ipConfig = {
            ...publicParam,
            flavor: ipConfigFlavor
        };
        return [configs, ipConfig];
    }

    // 查询价格
    async onQueryPrice() {
        if (!window.location.href.includes(`#${ROUTE_PATH.clusterCreate}`)) {
            // 解决新建集群页打开后快速返回集群列表页，仍在询价问题的初步方案
            return;
        }
        let priceData: NormalObject = {};
        _.each(Refs, ref => {
            const node: NodeClass = this.ref(ref);
            if (node) {
                priceData = {
                    ...node.getPriceData(),
                    ...priceData
                };
            }
        });
        if (priceData.payment === 'Prepaid') {
            this.data.set('timeLength', priceData.timeLength || 1);
            this.data.set('timeUnit', priceData.timeUnit || 'month');
        }
        this.data.set('payment', priceData.payment);
        // 检测是否有值有空 除了publicIpEnabled、publicIpBandwidth
        const hasEmpty = this.checkHasEmpty(priceData);
        if (hasEmpty) {
            return Promise.reject();
        }
        if (isXushang) {
            const [configs, ipConfig] = this.formateXushangQueryPirce(priceData);
            await api.queryPriceInXS({configs: [configs, ipConfig]}).then((
                result: [{price: number}, {price: number}]) => {
                this.data.set('clusterPrice', result[0].price);
                this.data.set('publicIpPrice', result[1].price);
            });
        }
        else {
            return api.queryPrice({...priceData}).then((
                target: {payment: PaymentType, cluster: number, publicIp: string}) => {
                this.data.set('clusterPrice', target.cluster);
                this.data.set('publicIpPrice', target.publicIp);
            });
        }
    }

    async getCouponList() {
        let param = {
            serviceType: 'KAFKA',
            orderItemPrice: this.data.get('clusterPrice'),
            region: this.$context.getCurrentRegion().id,
            totalPrice: this.data.get('clusterPrice'),
            properties: []
        };
        const result = await api.getCouponList(param);

        let couponList = [noCoupon];
        _.each(result, (coupon) => {
            couponList.push({
                text: this.getContent(coupon),
                value: coupon.id,
                title: coupon.balance
            });
        });

        this.data.set('couponList', couponList);
    }

    onRegionChange() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }
}
