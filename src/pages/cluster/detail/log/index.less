/**
* @file index.less
* <AUTHOR>
*/

@klass: bms-cluster-detail-log;
@descColor: #f39000;
@tab-nav-height: 44px;
@log-info-height: 35px;
@footer-height: 40px;
@header-height: 47px;
@contentColor: #151B26;
@labelColor: #5C5F66;

.@{klass} {

    &_content {
        height: 100%;
        overflow: hidden;

        .title {
            border: 0;
            color: @contentColor;
        }

        .page-title {
            display: flex;
            justify-content: space-between;
            background-color: var(--whiteColor);
            margin-right: -12px;
        }

        &_wrap {
            border: 1px solid rgba(232,233,235,1);
            border-radius: 4px;
            display: flex;
            height: 100%;

            .left_wrap {
                width: 180px;
                border-right: 1px solid rgba(232,233,235,1);

                &_header {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 47px;
                    border-bottom: 1px solid rgba(232,233,235,1);
                }

                .tree-area {
                    height: calc(~"100% - @{header-height}");
                    position: relative;
                    padding: 16px 0;

                    .s-tree {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        overflow-y: scroll;
                        padding: 16px 0;

                        .s-tree-node-item-icon-left {
                            display: none;
                        }

                        .custom-tree-node {
                            padding-left: 16px;
                        }
                    }
                }
            }
            
            .right_wrap {
                flex: 1;
                width: 0;
                overflow-x: scroll;
                
                .log-tabs {
                    padding: 16px;
                    height: 100%;

                    .s-tabpane-wrapper {
                        height: calc(~"100% - @{tab-nav-height}");
                    }

                    .log-tabs-tabpane {
                        width: 100%;
                        height: 100%;

                        .logInfo {
                            padding: 9px 0 8px;
                            color: @contentColor;

                            .label {
                                color: @labelColor;
                            }
                        }

                        .loading-area {
                            width: 100%;
                            height: calc(~"100% - @{log-info-height}");

                            .s-loading-slot-wrap {
                                height: 100%;

                                .log-detail {
                                    height: calc(~"100% - @{footer-height}");
                                    position: relative;
                                    padding: 16px;
                                    border: 1px solid rgba(232,233,235,1);
                                    border-radius: 4px;
                                    font-size: 12px;
                                    color: #151B26;
                                    letter-spacing: 0;
                                    font-weight: 400;

                                    .scroll-area {
                                        position: absolute;
                                        top: 16px;
                                        left: 16px;
                                        right: 16px;
                                        bottom: 0;
                                        overflow-y: scroll;
                                    }
                                }
        
                                .footer {
                                    margin-top: 8px;
                                    display: flex;
                                    justify-content: space-between;
        
                                    .offset-area {
                                        display: flex;
                                        align-items: center;
        
                                        .extra-btn {
                                            width: 6px;
                                        }
        
                                        .extra-load-desc, .full-load-desc {
                                            margin-left: 16px;
                                            color: @descColor;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .empty-area {
                    margin-top: 80px;
                }
            }
        }

        .foot-pager {
            margin: 0;
        }
    }
}