/**
 * 集群日志
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html, Enum} from '@baiducloud/runtime';
import {AppListPage, Empty} from '@baidu/sui-biz';
import {Tabs, Tree, Dialog, Pagination, Select, Button, Tooltip, Loading} from '@baidu/sui';
import {OutlinedUp, OutlinedDown, OutlinedRefresh} from '@baidu/sui-icon';
import {COLOR_CONF} from '@/common/config';

import api from '@/common/client';
import {formatTime} from '@/common/util';
import TipSelect from '@/components/tip-select';

import './index.less';

const klass = 'bms-cluster-detail-log';

type LogType = {
    filename: string,
    totalSize: number,
    modifyTime: string,
    pageNo: number,
    pageSize: number,
    content: string,
    startOffset: number,
    endOffset: number,
    broker: string
}

const pageSizeEnum = new Enum(
    {value: 256 * 1024, text: '256KB/页', alias: '256KB/页'},
    {value: 512 * 1024, text: '512KB/页', alias: '512KB/页'},
    {value: 1024 * 1024, text: '1M/页', alias: '1M/页'},
    {value: 2 * 1024 * 1024, text: '2M/页', alias: '2M/页'},
);

const extraUnit = 64 * 1024;

export default class Log extends Component {
    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="page-title">
                <h2 class="title">集群日志</h2>
                <s-button
                    disabled="{{disabled}}"
                    on-click="refreshLog"
                    skin="stringfy"
                >
                    <s-outlined-refresh color="${COLOR_CONF.defaultColor}" width="14" class="mr5"/>
                    刷新日志
                </s-button>
            </div>
            <div class="${klass}_content_wrap">
                <div class="left_wrap">
                        <div class="left_wrap_header">
                            节点ID：
                            <tip-select
                                placement="right"
                                width="92"
                                datasource="{{brokerList}}"
                                value="{= broker =}"
                                on-change="onSelectChange"
                                getPopupContainer="{{getPopupContainer}}"
                            />
                        </div>
                        <div class="tree-area">
                            <s-tree
                                s-ref="log-tree"
                                s-if="showTree"
                                empty-text=""
                                node-key="key"
                                data="{{logList}}"
                                lazy="{{false}}"
                                on-node-click="onNodeClick"
                            />
                        </div>
                </div>
                <div class="right_wrap">
                    <s-tabs
                        s-ref="log-tab"
                        class="log-tabs"
                        s-if="{{logTabs.source.length}}"
                        active="{{logTabs.active}}"
                        type="card"
                        closable="{{true}}"
                        on-change="onTabChange"
                        before-remove="{{beforeRemove}}"
                    >
                        <s-tabpane
                            class="log-tabs-tabpane"
                            style="display:{{logTabs.active === item.broker + item.filename ? 'block' : 'none'}}"
                            s-for="item, tabIndex in logTabs.source"
                            label="{{item.broker | getTabLabel(item.filename)}}"
                            key="{{item.broker + item.filename}}"
                            text="{{item.broker + item.filename}}"
                        >
                            <div class="logInfo">
                            <span class="label">最近更新时间：</span><span class="ml8">{{item.modifyTime | getTime}}</span>
                            <span class="ml24 label">文件大小:</span><span class="ml8">{{item.totalSize | getTotalSize}}</span>
                            </div>
                            <s-loading loading="{{detailLoading}}" size="large" class="loading-area">
                                <div class="log-detail">
                                    <div class="scroll-area" s-ref="{{'scrollArea'+ tabIndex}}">{{item.content | raw}}</div>
                                </div>
                                <div class="footer">
                                    <div class="offset-area">
                                        <s-tooltip content="当前页面内额外加载较旧的64KB数据">
                                            <s-button
                                                class="extra-btn"
                                                on-click="downloadOlder"
                                                disabled="{{!canLoadExtra || item.pageNo === 1}}"
                                            >
                                                <outlined-up/>
                                            </s-button>
                                        </s-tooltip>
                                        <s-tooltip content="当前页面内额外加载较新的64KB数据">
                                            <s-button
                                                class="ml8 extra-btn"
                                                on-click="downloadNewer"
                                                disabled="{{!canLoadExtra || hasGotNewstContent}}"
                                            >
                                                <outlined-down/>
                                            </s-button>
                                        </s-tooltip>
                                        <span class="extra-load-desc" s-if="canLoadExtra && item.totalExtraTimes !== 0">已额外加载{{extra}}KB数据</span>
                                        <span class="full-load-desc" s-else-if="!canLoadExtra && item.totalExtraTimes !== 0 && !isLoading">当前页面已达到额外加载上限</span>
                                    </div>
                                    <div class="pagination">
                                        <s-pagination
                                            page="{{item.pageNo}}"
                                            total="{{item.totalSize}}"
                                            pageSize="{{item.pageSize}}"
                                            layout="pageSize, pager, go"
                                            on-pagerChange="onPageChange"
                                        >
                                            <span slot='size' style="line-height: 30px; padding-left: 8px;width: 114px">
                                                <s-select
                                                    value="{= item.pageSize =}"
                                                    datasource="{{pageSizeArray}}"
                                                    on-change="onPageSizeChange"
                                                >
                                                </s-select>
                                            </span>
                                        </s-pagination>
                                    </div>
                                </div>
                            </s-loading>
                        </s-tabpane>
                    </s-tabs>
                    <s-empty
                        s-if="logList.length === 0"
                        class="empty-area"
                        vertical
                        actionText="节点下还没有任何日志文件，请选择其他节点"
                    ></s-empty>
                </div>
            </div>
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        'tip-select': TipSelect,
        's-tree': Tree,
        's-pagination': Pagination,
        's-select': Select,
        's-empty': Empty,
        's-button': Button,
        'outlined-up': OutlinedUp,
        'outlined-down': OutlinedDown,
        's-tooltip': Tooltip,
        's-outlined-refresh': OutlinedRefresh,
        's-loading': Loading
    };

    initData() {
        return {
            brokerList: [],
            logList: [],
            broker: '',
            logTabs: {
                source: [],
                active: ''
            },
            showTree: false,
            pageSizeArray: pageSizeEnum.toArray(),
            isLoading: false,
            detailLoading: false,
            beforeRemove: this.beforeRemove.bind(this),
            getPopupContainer: () => document.body
        }
    }

    static computed: SanComputedProps = {
        canLoadExtra(): boolean {
            const isLoading = this.data.get('isLoading');
            if (isLoading) {
                return false;
            }
            const totalExtraTimes = this.data.get('currentTabDetail.totalExtraTimes');
            const pageSize = this.data.get('currentTabDetail.pageSize');
            return totalExtraTimes < pageSize / extraUnit;
        },
        extra(): number {
            const totalExtraTimes = this.data.get('currentTabDetail.totalExtraTimes');
            return totalExtraTimes * 64;
        },
        hasGotNewstContent(): boolean {
            const totalSize = this.data.get('currentTabDetail.totalSize');
            const currentEndOffset = this.data.get('currentTabDetail.currentEndOffset');
            return currentEndOffset === totalSize;
        },
        disabled() {
            const source = this.data.get('logTabs.source');
            return source.length === 0;
        }
    };

    static filters: SanFilterProps = {
        getTotalSize(totalSize: number): string {
            if (totalSize >= 1024*1024) {
                return Math.floor(totalSize / (1024 * 1024)) + 'MB';
            } else if(totalSize >= 1024) {
                return Math.floor(totalSize / 1024) + 'KB';
            } else {
                return totalSize + 'Byte';
            }
        },
        getTime: formatTime,
        getTabLabel(broker, filename) {
            const brokerList = this.data.get('brokerList');
            const brokerObj = _.find(brokerList, item => item.value === broker);
            return '节点' + brokerObj.text + '-' + filename;
        }
    };

    async attached() {
        await this.getController();
        await this.getBrokers();
    }

    async getController() {
        await api.getControllerBroker(this.data.get('clusterId'), {}).then((result: {timestamp: String, brokerId: Number}) => {
            this.data.set('controller', result.brokerId);
        });
    }

    // 获取节点
    async getBrokers() {
        const controller = this.data.get('controller');
        await api.listClusterBrokers(this.data.get('clusterId'), {})
            .then((target: Array<{brokerId: number, instanceId: string}>) => {
                const arr = _.map(target, s => ({
                    text: controller == s.brokerId ? s.brokerId + ' Controller' : s.brokerId,
                    value: s.instanceId,
                    alias: s
                }));
                this.data.set('brokerList', arr);
                if (arr.length) {
                    const nodeId = this.data.get('nodeId');
                    this.data.set('broker', nodeId === '' ? arr[0].value : nodeId);
                    this.getLogList('');
                }
            });
    }

    // 获取日志列表
    async getLogList(key: string) {
        const {clusterId, broker} = this.data.get('');
        await api.listClusterLogList(clusterId, broker, {})
        .then((target: Array<{filename: string, modifyTime: string, totalSize: number}>) => {
            const arr = _.map(target, s => ({label: s.filename, key: s.filename, name: broker + s.filename}));
            this.data.set('logList', arr);
            this.data.set('showTree', true);
            key && this.nextTick(()=> this.nodeClick(key));
        });
    }

    async getLogDetail(filename: string) {
        const {clusterId, broker} = this.data.get('');
        const {pageNo = 1, pageSize = 256 * 1024, index} = this.data.get('currentTabDetail');
        await api.getLogDetail(clusterId, broker, filename, {
            pageNo,
            pageSize
        }).then((target: LogType) => {
                target.content = target.content.replace(/\n/g,"<br/>")
                const logDetail = {
                    content: target.content,
                    filename: target.filename,
                    modifyTime: target.modifyTime,
                    totalSize: target.totalSize,
                    currentStartOffset: target.startOffset,
                    currentEndOffset: target.endOffset,
                    totalExtraTimes: 0,
                    broker: broker,
                    pageNo: pageNo,
                    pageSize: pageSize
                }
                this.data.set(`logTabs.source[${index}]`, logDetail);
                this.data.set('logTabs.active', broker + target.filename);
                this.data.set('currentTabDetail', {...logDetail, index: index});
            });
    }

    onSelectChange(target: {value: string}) {
        this.data.set('broker', target.value);
        const refTree = this.ref('log-tree');
        refTree.data.set('selectId', '');
        if (target.value) {
            this.getLogList('');
        }
    }

    onNodeClick(target: {value: {data: {name: string, label: string, key: string}}}) {
        const {name, label} = target.value.data;
        const index = _.findIndex(this.data.get('logTabs.source'), (i: LogType) => i.broker + i.filename === name);
        const length = this.data.get('logTabs.source').length;
        if (index !== -1) {
            // 如果选中的日志已经被打开
            const currentTabDetail = this.data.get('logTabs.source')[index];
            this.data.set('logTabs.active', currentTabDetail.broker + currentTabDetail.filename);
            this.data.set('currentTabDetail', {...currentTabDetail, index});
        } else if(length >= 20){
            // 如果当前tab个数大于等于20
            Dialog.confirm({
                title: '提示',
                content: '打开窗口数已达20上限，请关闭已打开窗口。',
                okText: '确认',
                cancelText: '取消'
            });
        } else {
            // tab个数小于20且选中日志尚未被打开
            this.data.set('currentTabDetail', {index: length});
            this.getLogDetail(label);
        }
    }

    async onTabChange(target: {value: {key: string}}) {
        const key = target.value.key;
        const index = _.findIndex(this.data.get('logTabs.source'), (i: LogType) => i.broker + i.filename === key);
        const targetTabDetail = this.data.get('logTabs.source')[index];
        const currentTabDetail = this.data.get('currentTabDetail');
        if (currentTabDetail.broker !== targetTabDetail.broker) {
            this.data.set('broker', targetTabDetail.broker);
            this.data.set('showTree', false);
            await this.getLogList(targetTabDetail.filename);
        } else {
            this.nodeClick(targetTabDetail.filename);
        }
        this.data.set('currentTabDetail', {...targetTabDetail, index});
        this.data.set('logTabs.active', key);
        const node = this.ref('log-tree').getCurrentNode();
        node && node.el.parentNode.scrollTo({
            top: node.el.offsetTop - 185,
            behavior: 'smooth'
        });
    }

    nodeClick(key: string) {
        // 切换一下左侧树那里的选中状态
        const refTree = this.ref('log-tree');
        const newNode = refTree?.getNode(key);
        if (!newNode.node) {
            return;
        }
        refTree.data.set('selectId', newNode.node.data.get('data.__node__.selectId'));
    }

    beforeRemove(key: string) {
        const source = this.data.get('logTabs.source');
        const index = _.findIndex(source, (i: LogType) => i.broker + i.filename === key);
        const currentIndex = this.data.get('currentTabDetail.index');
        const broker = source[index].broker;
        this.data.removeAt('logTabs.source', index);
        const newSource = this.data.get('logTabs.source');
        const firstKey = newSource[0]?.broker + newSource[0]?.filename;
        if(this.data.get('logTabs.active') === key) {
            this.data.set('logTabs.active', firstKey);
            this.data.set('currentTabDetail', newSource[0]);
            if (broker !== newSource[0].broker) {
                this.data.set('broker', newSource[0].broker);
                this.getLogList(newSource[0].filename);
            }
        } else if (currentIndex > index) {
            this.data.set('currentTabDetail.index', currentIndex - 1);
        }
    }

    // 分页 page 设置
    onPageChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('detailLoading', true);
        this.data.set('currentTabDetail.pageNo', args.value.page);
        this.refreshLog();
    }

    // 分页 pageSize 设置
    onPageSizeChange(target: {value: number}) {
        this.data.set('detailLoading', true);
        this.data.set('currentTabDetail.pageSize', target.value);
        this.data.set('currentTabDetail.pageNo', 1);
        this.refreshLog();
    }

    // 加载较旧的64KB数据
    downloadOlder() {
        this.data.set('isLoading', true);
        const {currentStartOffset, filename, index} = this.data.get('currentTabDetail');
        const {totalExtraTimes} = this.data.get('currentTabDetail');
        this.data.set('currentTabDetail.totalExtraTimes', totalExtraTimes + 1);
        const {clusterId, broker} = this.data.get('');
        api.getLogDetail(clusterId, broker, filename, {
            currentStartOffset,
            backwardOffset:  extraUnit
        }).then((target: LogType) => {
                const content = target.content.replace(/\n/g,"<br/>") + this.data.get('currentTabDetail.content');
                this.data.set('currentTabDetail.content', content);
                this.data.set('currentTabDetail.currentStartOffset', target.startOffset);
                this.data.set(`logTabs.source[${index}]`, this.data.get('currentTabDetail'));
                this.data.set('isLoading', false);
            });
        this.ref(`scrollArea${index}`)?.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // 加载较新的64KB数据
    downloadNewer() {
        this.data.set('isLoading', true);
        const {currentEndOffset, filename, index} = this.data.get('currentTabDetail');
        const {totalExtraTimes} = this.data.get('currentTabDetail');
        this.data.set('currentTabDetail.totalExtraTimes', totalExtraTimes + 1);
        const {clusterId, broker} = this.data.get('');
        api.getLogDetail(clusterId, broker, filename, {
            currentEndOffset,
            forwardOffset: extraUnit
        }).then((target: LogType) => {
                const content = this.data.get('currentTabDetail.content') + target.content.replace(/\n/g,"<br/>");
                this.data.set('currentTabDetail.content', content);
                this.data.set('currentTabDetail.currentEndOffset', target.endOffset);
                this.data.set(`logTabs.source[${index}]`, this.data.get('currentTabDetail'));
                this.data.set('isLoading', false);
            });
        const scrollHeight = this.ref(`scrollArea${index}`)?.scrollHeight;
        this.ref(`scrollArea${index}`)?.scrollTo({
            top: scrollHeight,
            behavior: 'smooth'
        });
    }
    
    async refreshLog() {
        // 重新请求数据
        const {index} = this.data.get('currentTabDetail');
        this.ref(`scrollArea${index}`)?.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
        this.data.set('currentTabDetail.totalExtraTimes', 0);
        const filename = this.data.get('currentTabDetail.filename');
        await this.getLogDetail(filename);
        this.data.set('detailLoading', false);
    }
}