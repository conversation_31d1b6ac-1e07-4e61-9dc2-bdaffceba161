/**
 * 新建流控
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Select, Notification, InputNumber, Checkbox, Input, Loading} from '@baidu/sui';
import {ClusterEntityType} from '@/common/enums';
import {EntityType} from '@/common/enums/constant';
import {VAILDITE_ITEMS} from '@/common/rules';
import api from '@/common/client';
import {RULES} from '@/common/rules';
import Tip from '@/components/tip';
import './index.less';

const kDialogClass = 'create-cluster-process';
const max = 1024000;
export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="${kDialogClass} ue-dialog"
            title="{{ mode === 'edit' ? '编辑流控' : '新建流控'}}"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            width="400"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <s-loading loading="{{loading}}">
                <s-form
                    s-ref="form"
                    rules="{{rules}}"
                    data="{= formData =}">
                    <s-form-item label="流控类型：" prop="entityType">
                        <s-select
                            width="300"
                            datasource="{{clusterEntityType}}"
                            value="{=formData.entityType=}"
                            getPopupContainer="{{getPopupContainer}}"
                            disabled="{{mode === 'edit'}}"
                        ></s-select>
                    </s-form-item>
                    <s-form-item
                        label="流控目标："
                        class="labelWidth"
                        prop="username"
                        s-if="{{formData.entityType === EntityType.USER}}"
                    >
                        <s-select
                            width="300"
                            datasource="{{userList}}"
                            value="{=formData.username=}"
                            disabled="{{mode === 'edit'}}"
                            getPopupContainer="{{getPopupContainer}}"
                        ></s-select>
                    </s-form-item>
                    <s-form-item
                        label="流控目标："
                        prop="clientId"
                        class="labelWidth"
                        s-if="{{formData.entityType === EntityType.CLINET}}"
                    >
                        <s-input
                            value="{=formData.clientId=}"
                            disabled="{{mode === 'edit'}}"
                            width="300"
                            class="s-input-item"
                            placeholder='请输入客户端ID，可输入"*"选择所有客户端'
                        />
                        <p slot="help">
                            大小写字母、数字以及_-特殊字符，长度1-125
                        </p>
                    </s-form-item>
                    <s-form-item
                        label="流控目标："
                        prop="username_clientId"
                        class="labelWidth"
                        s-if="{{formData.entityType === EntityType.USER_CLIENT}}"
                    >
                        <s-select
                            datasource="{{userList}}"
                             width="300"
                            value="{=formData.username=}"
                            disabled="{{mode === 'edit'}}"
                            getPopupContainer="{{getPopupContainer}}"
                        ></s-select>
                        <s-input
                            style="display: block"
                            class="mt8 s-input-item"
                            value="{=formData.clientId=}"
                            width="300"
                            disabled="{{mode === 'edit'}}"
                            placeholder='请输入客户端ID，可输入"*"选择所有客户端'
                        />
                        <p slot="help">
                            大小写字母、数字以及_-特殊字符，长度1-125
                        </p>
                    </s-form-item>
                    <s-form-item prop="byteRate" isRequired>
                        <span slot="label">
                            上限配置：
                            <tip-cmpt>
                                限流是以节点维度进行限流，该速率上限的配置仅为单个节点的速率上限
                            </tip-cmpt>
                        </span>
                        <div>
                            <s-checkbox
                                label="生产速率上限"
                                checked="{=produceCheck=}"
                                class="custom-input"
                            />
                            <span class="ml16" s-if="{{produceCheck}}">
                                <s-input-number
                                    min="{{1}}"
                                    max="{{max}}"
                                    value="{=formData.producerByteRate=}"
                                    step="{{1}}"
                                    stepStrictly
                                />MB/s
                            </span>
                        </div>
                        <div class="mt8">
                            <s-checkbox
                                label="消费速率上限"
                                checked="{=consumeCheck=}"
                                class="custom-input"
                            />
                            <span class="ml16" s-if="{{consumeCheck}}">
                                <s-input-number
                                    min="{{1}}"
                                    max="{{max}}"
                                    value="{=formData.consumerByteRate=}"
                                    step="{{1}}"
                                    stepStrictly
                                />MB/s
                            </span>
                        </div>
                    </s-form-item>
                </s-form>
            </s-loading>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-input': Input,
        's-input-number': InputNumber,
        's-loading': Loading,
        'tip-cmpt': Tip,
    };

    static computed = {
        clusterEntityType(): any[] {
            const userManage: boolean = this.data.get('userManage');
            const arr = ClusterEntityType.toArray().map(item => ({label: item.text, value: item.value}));
            return userManage ? [...arr] : arr.filter(item => item.value === EntityType.CLINET);
        }
    };

    initData() {
        return {
            open: true,
            userList: [],
            poduceCheck: false,
            consumeCheck: false,
            max: max,
            formData: {
                entityType: '',
                username: null,
                clientId: null,
                producerByteRate: 3,
                consumerByteRate: 3
            },
            loading: true,
            EntityType: EntityType,
            getPopupContainer: () => document.body,
            rules: {
                entityType: [VAILDITE_ITEMS.requiredSelect],
                username: [VAILDITE_ITEMS.requiredSelect],
                clientId: [
                    VAILDITE_ITEMS.requiredInput,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (value === '*') {
                                callback();
                            }
                            else if (!RULES.clusterTopicName.test(value) && this.data.get('mode') !== 'edit') {
                                return callback('clientId输入格式错误');
                            }
                            callback();
                        }
                    }
                ],
                username_clientId: [{
                    validator: (rule: any, value: string, callback: Function) => {
                        if (!this.data.get('formData.username')) {
                            return callback('请选择用户名');
                        }
                        if (!this.data.get('formData.username') || !this.data.get('formData.clientId')) {
                            return callback('请输入客户端ID');
                        }
                        if (this.data.get('formData.clientId') === '*') {
                            callback();
                        }
                        else if (!RULES.clusterTopicName.test(this.data.get('formData.clientId')) && this.data.get('mode') !== 'edit') {
                            return callback('clientId输入格式错误');
                        }
                        callback();
                    }
                }],
                byteRate: [
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (!this.data.get('produceCheck') && !this.data.get('consumeCheck')) {
                                return callback('请至少设置一项速率上限');
                            }
                            callback();
                        }
                    }
                ]
            },
        };
    }

    async attached() {
        const {mode, detail, userManage} = this.data.get('');
        try {
            if (userManage) {
                const userList = await api.getClusterUserList(this.data.get('clusterId'), {});
                this.data.set('userList', userList.map((
                    {username}: {username: string}) => ({label: username, value: username})));
                this.data.unshift('userList', {label: '全部用户', value: 'defaultUser'});
            }
            if (mode === 'edit') {
                this.data.merge('formData', {...detail});
            }
        }
        finally {
            this.data.set('loading', false);
        }
    }


    // 确认
    async onConfirm() {
        try {
            await this.ref('form').validateFields();
            this.data.set('confirming', true);
            const formData = this.data.get('formData');
            const {mode, clusterId, produceCheck, consumeCheck} = this.data.get('');
            let param = {
                entityType: formData.entityType,
                producerByteRate: formData.producerByteRate || 1,
                consumerByteRate: formData.consumerByteRate || 1,
                username: null,
                clientId: null,
                userDefault: false,
                clientDefault: false
            };
            switch (formData.entityType) {
                case EntityType.USER:
                    param = {
                        ...param,
                        username: formData.username === 'defaultUser' ? null : formData.username,
                        userDefault: formData.username === 'defaultUser'
                    };
                    break;
                case EntityType.CLINET:
                    param = {
                        ...param,
                        clientId: formData.clientId === '*' ? null : formData.clientId,
                        clientDefault: formData.clientId === '*'
                    };
                    break;
                case EntityType.USER_CLIENT:
                    param = {
                        ...param,
                        username: formData.username === 'defaultUser' ? null : formData.username,
                        userDefault: formData.username === 'defaultUser',
                        clientId: formData.clientId === '*' ? null : formData.clientId,
                        clientDefault: formData.clientId === '*'
                    };
                    break;
            }
            !produceCheck && delete param.producerByteRate;
            !consumeCheck && delete param.consumerByteRate;
            mode === 'create'
                ? await api.createQuotas(clusterId, param)
                : await api.updateQuotas(clusterId, param);
            Notification.success(mode === 'create' ? '新增成功' : '编辑成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
