.bms-cluster-detail-process {
    .user {
        background: #E6FFFD;
        border-radius: 2px;
        padding: 2px 6px;
    }
    .clientId {
        background: #FFF4E6;
        border-radius: 2px;
        padding: 2px 6px;
    }
    .user-clientId {
        background: #E6F0FF;
        border-radius: 2px;
        padding: 2px 6px;
    }
    .s-dialog {
        &.s-dialog-instance-custom {
            & > .s-dialog-wrapper {
                width: auto;
            }
        }
    }
}

.create-cluster-process {
    .labelWidth {
        label {
            width: 84px;
            display: inline-block;
        }
    }
    .custom-input {
        .s-radio-text {
            line-height: 30px;
        }
    }

    .s-loading {
        width: 100%;

        .s-loading-wrap {
            background: transparent;
        }
    }
}
