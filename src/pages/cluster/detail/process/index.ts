/* eslint-disable max-len */
/**
 * 流控管理
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Dialog, Pagination, Table, Notification, Button} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import CreateBtn from '@/components/create-btn';
import {OutlinedRefresh} from '@baidu/sui-icon';
import PaginationTable from '@/components/common-table/pagination-table';
import {TABLE_SUI, PAGER_SUI} from '@/common/config';
import {AllEnum, ClusterEntityType} from '@/common/enums';
import Permission from '@/common/permission';
import CreateProcess from './create-process';
import {EntityType} from '@/common/enums/constant';
import api from '@/common/client';

import './index.less';

const klass = 'bms-cluster-detail-process';

type ClusterProcessItem = {
    entityType: string;
    username: string;
    clientId: string;
    producerByteRate: number;
    consumerByteRate: number;
    userDefault: boolean;
    clientDefault: boolean;
};

type ColumnsFilter = {
    field: {
        name: string;
        label: string;
    };
    filter: {
        value: string;
    };
};

const columns = [
    {
        name: 'processName',
        label: '流控目标',
    },
    {
        name: 'entityType',
        label: '流控类型',
        filter: {
            options: [
                ...AllEnum.toArray(),
                ...ClusterEntityType.toArray()
            ],
            value: ''
        },
        render(item: ClusterProcessItem) {
            return `<span class="${item.entityType}">${ClusterEntityType.getTextFromValue(item.entityType)}</span>`;
        },
        width: 150,
    },
    {
        name: 'producerByteRate',
        label: '生产速率上限',
        render: 'producerByteRate'
    },
    {
        name: 'consumerByteRate',
        label: '消费速率上限',
        render: 'consumerByteRate'
    },
    {
        name: 'operation',
        label: '操作',
        width: 280
    }
];

export default class Process extends PaginationTable implements DetailRefresh {

    searchKey = 'processName';

    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">流控管理</h2>
            </div>
            <div slot="bulk">
                <create-btn
                    text="新建流控"
                    on-click="onCreate"
                    disabled="{{detail.status | createDisabled}}"
                />
            </div>
            <div slot="filter">
                <s-searchbox
                    class="searchbox"
                    placeholder="请输入流控目标名称进行搜索"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="{{170}}"
                >
                    <div slot="prefix"></div>
                </s-searchbox>
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                on-filter="tableFilter"
                datasource="{{table.datasource}}">
                <div slot="c-processName">
                    {{row | processName}}
                </div>
                <div slot="c-producerByteRate">{{row.producerByteRate ? (row.producerByteRate + ' MB/s') : '-'}}</div>
                <div slot="c-consumerByteRate">{{row.consumerByteRate ? (row.consumerByteRate + ' MB/s') : '-'}}</div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{detail.status | disabledDelete}}"
                            on-click="edit(row)">
                            编辑
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{detail.status | disabledDelete}}"
                            on-click="onDelete(row)">
                            删除
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty
                        vertical
                    >
                        <span slot="action">
                            <s-button
                                skin="stringfy"
                                on-click="onCreate"
                                disabled="{{detail.status | createDisabled}}">
                                创建流控>
                            </s-button>
                        </span>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'create-btn': CreateBtn,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty
    };

    initData() {
        return {
            searchbox: {
                keyword: ''
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [...columns]
            },
            pager: {...PAGER_SUI},
            detail: {},
        };
    }

    static filters = {
        createDisabled(state: string) {
            return !Permission.CLUSTER.TOPIC.canCreate(state);
        },
        disabledDelete(status: string) {
            return Permission.CLUSTER.TOPIC.disabledDelete(status);
        },
        processName(row: ClusterProcessItem) {
            const username = row.userDefault ? '全部用户' : row.username;
            const clientId = row.clientDefault ? '全部客户端' : row.clientId;
            return row.entityType === 'user'
                ? username : row.entityType === 'clientId'
                    ? clientId : username + '-' + clientId;
        }
    };

    attached() {
        this.getComList();
    }

    pickEmptyWithoutBoolean(obj: object) {
        const newObj = {};
        for (const key in obj) {
            if (typeof obj[key] === 'boolean' || obj[key]) {
                newObj[key] = obj[key];
            }
        }
        return newObj;
    }

    async refreshInfo(): Promise<any> {
        await this.getComList();
    }

    async getNewTableList() {
        return await api.getQuotas(this.data.get('clusterId'), {});
    }

    // 新建主题
    onCreate() {
        const {clusterId} = this.data.get('');
        const dialog = new CreateProcess({data: {
            clusterId,
            mode: 'create',
            userManage: this.data.get('userManage'),
        }});
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.resetTable();
            this.getNewTableList();
        });
    }

    edit(row: ClusterProcessItem) {
        const {clusterId} = this.data.get('');
        const dialog = new CreateProcess({data: {
            clusterId,
            detail: {
                entityType: row.entityType,
                username: row.userDefault ? 'defaultUser' : row.username,
                clientId: row.clientDefault ? '*' : row.clientId,
                producerByteRate: Math.ceil(row.producerByteRate),
                consumerByteRate: Math.ceil(row.consumerByteRate),
            },
            produceCheck: Boolean(row.producerByteRate),
            consumeCheck: Boolean(row.consumerByteRate),
            mode: 'edit',
            userManage: this.data.get('userManage'),
        }});
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.resetTable();
            this.getNewTableList();
        });
    }

    async tableFilter(e: ColumnsFilter) {
        const {filter} = e;
        this.data.set('searchbox.keyword', '');
        const datasource = this.totalList;
        switch (filter.value) {
            case EntityType.USER:
                this.list = datasource.filter((i: ClusterProcessItem) => i.entityType === EntityType.USER);
                break;
            case EntityType.CLINET:
                this.list = datasource.filter((i: ClusterProcessItem) => i.entityType === EntityType.CLINET);
                break;
            case EntityType.USER_CLIENT:
                this.list = datasource.filter((i: ClusterProcessItem) => i.entityType === EntityType.USER_CLIENT);
                break;
            default:
                this.list = datasource;
        }
        this.data.merge('pager', {
            count: this.list.length,
            page: 1
        });
        this.renderTableByPage();
    }

    // 删除
    onDelete(row: ClusterProcessItem) {
        const username = row.userDefault ? '全部用户' : row.username;
        const clientId = row.clientDefault ? '全部客户端' : row.clientId;
        const processName = row.entityType === 'user'
            ? username : row.entityType === 'clientId'
                ? clientId : username + '-' + clientId;
        Dialog.warning({
            content: `删除后无法恢复！请确定是否要删除流控目标"${processName}"`,
            okText: '确定',
            width: 352,
            onOk: async () => {
                await api.deleteQuotas(this.data.get('clusterId'), this.pickEmptyWithoutBoolean({
                    entityType: row.entityType,
                    username: row.username,
                    clientId: row.clientId,
                    userDefault: row.userDefault,
                    clientDefault: row.clientDefault,
                }));
                Notification.success('删除成功');
                this.resetTable();
            }
        });
    }

    filterSearch(key: string) {
        const keyword = this.data.get(this.searchKeyworkKey);
        this.data.set('table.columns', [...columns]);
        if (!keyword) {
            this.list = this.totalList;
            this.data.set('pager.count', this.list.length);
            this.data.set('pager.page', 1);
            this.renderTableByPage();
            return;
        }
        const all = ['全部用户', '全部客户端'];
        if (_.includes(all, keyword)) {
            this.list = _.filter(this.totalList, item => {
                return keyword === '全部用户' ? item.userDefault : item.clientDefault;
            });
            this.data.set('pager.count', this.list.length);
            this.data.set('pager.page', 1);
            this.renderTableByPage();
            return;
        }
        this.list = _.filter(this.totalList, item => item.username?.indexOf(keyword) > -1 || item.clientId?.indexOf(keyword) > -1);
        this.data.set('pager.count', this.list.length);
        this.data.set('pager.page', 1);
        this.renderTableByPage();
    }
}
