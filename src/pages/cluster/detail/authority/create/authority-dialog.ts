/**
 * authority-dialog
 *
 * @file user-dialog.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {
    Dialog,
    Notification,
    Form,
    Input,
    Select,
    Radio,
    Checkbox
} from '@baidu/sui';
import api from '@/common/client';
import {
    ClusterAuthorityOptionType,
    ClusterAuthorityPatternType,
    ClusterAuthorityResourceType
} from '@/common/enums';
import {AuthorityResourceType, OperationType} from '@/common/enums/constant';
import {RULES, TopicNameRegTip} from '@/common/rules';

import './authority-dialog.less';
import {ClusterAuthorityItem} from '../list';

export default class AuthorityDialog extends Component {
    static template = html`
    <template>
        <s-dialog
            class="cluster-authority-dialog ue-dialog-shot"
            open="{= open =}"
            on-confirm="confirm"
            on-close="close"
            loadingAfterConfirm="{{false}}"
            confirming="{{confirming}}"
            title="创建权限">
            <s-form
                data="{= formData =}"
                class="form-format"
                s-ref="form"
                label-align="left"
                rules="{{rules}}">
                <s-form-item
                    prop="username"
                    label="用户名:">
                    <s-select
                        datasource="{{selects.username}}"
                        width="{{inputWidth}}"
                        value="{= formData.username =}"
                        filterable>
                    </s-select>
                </s-form-item>
                <s-form-item
                    prop="resourceType"
                    label="资源类型:">
                    <s-radio-group
                        datasource="{{selects.resourceType}}"
                        radioType="button"
                        on-change="changeResourceType"
                        width="{{inputWidth}}"
                        value="{= formData.resourceType =}">
                    </s-radio-group>
                </s-form-item>
                <s-form-item
                    prop="patternType"
                    label="匹配模式:">
                    <s-radio-group
                        datasource="{{selects.patternType}}"
                        radioType="button"
                        disabled="{{clusterTypeDisabled}}"
                        width="{{inputWidth}}"
                        value="{= formData.patternType =}">
                    </s-radio-group>
                </s-form-item>
                <s-form-item
                    prop="resourceName"
                    label="匹配内容:">
                    <s-input
                        placeholder="请输入匹配内容{{formData.patternType === 'LITERAL' ? ',可输入“*”选择所有资源' : ''}}"
                        type="resourceName"
                        width="{{inputWidth}}"
                        disabled="{{clusterTypeDisabled}}"
                        value="{= formData.resourceName =}">
                    </s-input>
                    <p class="desc mt5">
                        ${TopicNameRegTip}
                    </p>
                </s-form-item>
                <s-form-item
                    s-if="formData.resourceType === '${AuthorityResourceType.TOPIC}'"
                    prop="operation"
                    label="操作类型:">
                    <s-checkbox-group
                        datasource="{{selects.operation}}"
                        width="{{inputWidth}}"
                        value="{= formData.operation =}"
                        on-change="onCheckBoxChange">
                    </s-checkbox-group>
                </s-form-item>
                <s-form-item
                    s-else
                    class="text-item"
                    label="操作类型:">
                    <div>{{operationText}}</div>
                </s-form-item>
            </s-form>
        </s-dialog>
    </template>
    `;
    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-textarea': Input.TextArea,
        's-select': Select,
        's-radio-group': Radio.RadioGroup,
        's-checkbox-group': Checkbox.CheckboxGroup
    };

    initData() {
        return {
            mode: 'create',
            open: true,
            confirming: false,
            inputWidth: 280,
            formData: {
                username: '',
                resourceType: 'TOPIC',
                patternType: '',
                resourceName: '',
                operation: []
            },
            selects: {
                username: [],
                patternType: ClusterAuthorityPatternType.toArray(),
                resourceType: ClusterAuthorityResourceType.toArray(),
                operation: ClusterAuthorityOptionType.toArray().slice(0, 2)
            },
            rules: {
                username: [
                    {required: true, message: '请选择用户'}
                ],
                resourceType: [
                    {required: true, message: '请选择资源类型'}
                ],
                patternType: [
                    {required: true, message: '请选择匹配模式'}
                ],
                resourceName: [
                    {required: true, message: '请输入匹配内容'},
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (value === '*') {
                                return callback();
                            }
                            if (!RULES.topicName.test(value)) {
                                return callback('输入格式错误');
                            }
                            callback();
                        }
                    }
                ],
                operation: [
                    {required: true, message: '至少选择一种操作类型'}
                ]
            }
        };
    }

    static computed: SanComputedProps = {
        operationText(): string {
            const resourceType = this.data.get('formData.resourceType');
            const operation = this.data.get('formData.operation');
            if (resourceType === AuthorityResourceType.TOPIC) {
                return '';
            }
            else {
                return ClusterAuthorityOptionType.valueIndex[operation[0]]?.text;
            }
        },
        clusterTypeDisabled(): boolean {
            const resourceType = this.data.get('formData.resourceType');
            return resourceType === AuthorityResourceType.CLUSTER;
        }
    };

    attached() {
        this.getTableList();
        const version = this.data.get('version');
        version >= '2.8.2' && this.data.set('selects.resourceType', ClusterAuthorityResourceType.toArray().filter(it => it.value !== AuthorityResourceType.CLUSTER));
    }

    // 确认
    async confirm() {
        try {
            this.data.set('confirming', true);
            await (this.ref('form') as unknown as Form).validateFields();
            const {username, resourceType, patternType, resourceName, operation} = this.data.get('formData');
            await api.createClusterAuthority(this.data.get('clusterId'), {
                username,
                resourceType,
                patternType,
                resourceName,
                operation
            });
            Notification.success('创建成功');
            this.fire('success', {});
            this.close();
        }
        finally {
            this.data.set('confirming', false);
        }
    }

    async getTableList() {
        let result = await api.getClusterUserList(this.data.get('clusterId'));
        result = result.map((item: ClusterAuthorityItem) => ({...item, text: item.username, value: item.username}));
        this.data.set('selects.username', result);
    }

    // 切换资源类型回调
    changeResourceType(target: {value: AuthorityResourceType}) {
        switch (target.value) {
            case AuthorityResourceType.GROUP:
                this.data.merge('formData', {
                    resourceName: '',
                    patternType: '',
                    operation: [OperationType.CONSUME]
                });
                break;
            case AuthorityResourceType.CLUSTER:
                this.data.merge('formData', {
                    resourceName: 'kafka-cluster',
                    patternType: ClusterAuthorityPatternType.toArray()[0].value,
                    operation: [OperationType.IDEMPOTENT_WRITE]
                });
                break;
            case AuthorityResourceType.TRANSACTIONAL_ID:
                this.data.merge('formData', {
                    resourceName: '',
                    patternType: '',
                    operation: [OperationType.WRITE]
                });
                break;
            default:
                this.data.merge('formData', {
                    resourceName: '',
                    patternType: '',
                    operation: []
                });
                break;
        }
    }

    // checkbox选中
    onCheckBoxChange(target: {value: string[]}) {
        // 这里需要使用nextTick等待组件数据更新完后，Form能拿到
        this.nextTick(() => (this.ref('form') as unknown as Form).validateFields(['operation']));
    }

    close() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
