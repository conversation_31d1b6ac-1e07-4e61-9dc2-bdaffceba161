/**
 * 权限管理
 *
 * @file list.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Dialog, Pagination, Table, Notification, Button, Select, Tooltip} from '@baidu/sui';
import {AppListPage, Empty} from '@baidu/sui-biz';
import CreateBtn from '@/components/create-btn';
import {OutlinedRefresh} from '@baidu/sui-icon';
import PaginationTable from '@/components/common-table/pagination-table';

import EllipsisTip from '@/components/ellipsis-tip';
import {TABLE_SUI, PAGER_SUI, SELECT_HEIGHT} from '@/common/config';
import {pickEmpty} from '@/common/util';
import {
    ClusterAuthorityPatternType,
    ClusterAuthorityResourceType,
    ClusterAuthorityOptionType,
    AllEnum
} from '@/common/enums';
import {OperationType} from '@/common/enums/constant';
import Permission from '@/common/permission';

import api from '@/common/client';

import AuthorityDialog from './create/authority-dialog';

export type ClusterAuthorityItem = {
    username: string;
    patternType: string;
    resourceType: string;
    resourceName: string;
    operation: OperationType;
}

enum SourceTypeEnum {
    ALL = '',
    TOPIC = 'TOPIC',
    GROUP = 'GROUP',
    CLUSTER = 'CLUSTER',
    TRANSACTIONAL_ID = 'TRANSACTIONAL_ID'
}

type ColumnsItem = {
    name: string;
    label: string;
};
type ColumnsFilter = {
    field: {
        name: string;
        label: string;
    };
    filter: {
        value: string;
    };
};

const klass = 'bms-cluster-detail-authority';
const allEnum = AllEnum.toArray();
const firstValue = allEnum[0].value;

const sourceTypeList = [
    ...allEnum,
    {text: '主题', value: SourceTypeEnum.TOPIC},
    {text: '消费组', value: SourceTypeEnum.GROUP},
    {text: '集群', value: SourceTypeEnum.CLUSTER},
    {text: '事务ID', value: SourceTypeEnum.TRANSACTIONAL_ID},
];

export default class AuthorityList extends PaginationTable implements DetailRefresh {

    searchKey = '';

    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">权限管理</h2>
            </div>
            <div slot="bulk">
                <create-btn
                    text="创建权限"
                    on-click="onCreate"
                    disabled="{{detail.status | createDisabled}}"
                />
            </div>
            <div slot="filter">
                <span>资源类型：</span>
                <s-select
                    class="ml10"
                    datasource="{{sourceTypeList}}"
                    height="{{${SELECT_HEIGHT}}}"
                    value="{= sourceType =}"
                    on-change="onSourceTypeChange"
                />
                <span class="ml15">资源名：</span>
                <s-select
                    class="ml10"
                    filterable
                    datasource="{{sourceList}}"
                    height="{{${SELECT_HEIGHT}}}"
                    value="{= source =}"
                    on-change="onSelectChange('source', $event)"
                />
                <span class="ml15">用户名：</span>
                <s-select
                    filterable
                    class="ml10"
                    datasource="{{userList}}"
                    height="{{${SELECT_HEIGHT}}}"
                    value="{= user =}"
                    on-change="onSelectChange('user', $event)"
                />
                <s-button
                    on-click="onResetSelect"
                    class="ml5"
                >
                重置
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                on-filter="tableFilter"
                datasource="{{table.datasource}}">
                <div slot="c-operationToolbar">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onDelete(row)"
                            disabled="{{detail.status | disabledDelete}}"
                        >
                            删除
                        </s-button>
                    </span>
                </div>
                <div slot="c-resourceName">
                    <s-tooltip content="{{row.resourceName}}">
                        <div class="table-cell-width">{{row.resourceName}}</div>
                    </s-tooltip>
                </div>
                <div slot="empty">
                    <s-empty
                        vertical
                    >
                        <span slot="action">
                            <s-button
                                skin="stringfy"
                                on-click="onCreate"
                                disabled="{{detail.status | createDisabled}}">
                                马上创建>
                            </s-button>
                        </span>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'ellipsis-tip': EllipsisTip,
        'create-btn': CreateBtn,
        's-select': Select,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty,
        's-tooltip': Tooltip
    };

    initData() {
        return {
            sourceTypeList: sourceTypeList,
            sourceList: [...allEnum],
            userList: [...allEnum],
            sourceType: '',
            source: '',
            user: '',
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'username',
                        label: '用户名'
                    },
                    {
                        name: 'patternType',
                        label: '匹配模式',
                        filter: {
                            options: [...allEnum, ...ClusterAuthorityPatternType.toArray()],
                            value: ''
                        },
                        render: (item: ClusterAuthorityItem) => ClusterAuthorityPatternType.getTextFromValue(item.patternType)
                    },
                    {
                        name: 'resourceType',
                        label: '资源类型',
                        render: (item: ClusterAuthorityItem) => ClusterAuthorityResourceType.getTextFromValue(item.resourceType)
                    },
                    {
                        name: 'resourceName',
                        width: 200,
                        label: '匹配内容'
                    },
                    {
                        name: 'operation',
                        label: '操作类型',
                        render: (item: ClusterAuthorityItem) => ClusterAuthorityOptionType.getTextFromValue(item.operation)
                    },
                    {
                        name: 'operationToolbar',
                        label: '操作',
                        width: 100
                    }
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        createDisabled(state: string) {
            return !Permission.CLUSTER.AUTHORITY.canCreate(state);
        },
        disabledDelete: Permission.CLUSTER.AUTHORITY.disabledDelete
    };

    attached() {
        this.getComList();
        this.getUserList();
    }

    async getNewTableList() {
        const {clusterId, table, user, source, sourceType} = this.data.get('');
        const params = pickEmpty({
            username: user,
            resourceType: sourceType,
            resourceName: source,
            patternType: table.columns[1].filter.value,
        });
        return await api.getClusterAuthorityList(clusterId, params);
    }

    // 获取用户列表
    getUserList() {
        api.getClusterUserList(this.data.get('clusterId'), {})
            .then((target: Array<{username: string, createTime: string}>) => {
                const arr = _.map(target, i => ({text: i.username, value: i.username}));
                this.data.set('userList', [...allEnum, ...arr]);
            });
    }

    // 重置用户选中信息
    onResetSelect() {
        this.data.set('source', firstValue);
        this.data.set('sourceType', firstValue);
        this.data.set('user', firstValue);
        this.getComList();
    }

    // 选择的改变
    onSelectChange(type: 'source' | 'user', target: {value: string}) {
        this.data.set(type, target.value);
        this.getComList();
    }

    // 资源类型改变
    async onSourceTypeChange(target: {value: SourceTypeEnum}) {
        this.data.set('sourceType', target.value);
        const {clusterId} = this.data.get();
        let arr: Array<{text: string, value: string}> = [];
        if (target.value === SourceTypeEnum.TOPIC) {
            await api.listClusterAllTopics(clusterId, {})
                .then((target: string[]) => (arr = _.map(target, s => ({text: s, value: s}))));
        }
        else if (target.value === SourceTypeEnum.GROUP) {
            await api.listClusterAllConsumerGroups(this.data.get('clusterId'), {})
                .then((target: Array<{
                    groupName: string;
                    topics: Array<{
                        topicName: string;
                        partitions: number[];
                    }>;
                }>) => (arr = _.map(target, i => ({text: i.groupName, value: i.groupName}))));
        }
        // 获取 主题 or 资源 列表，做资源全部处理，然后更新表格
        this.data.set('sourceList', [...allEnum, ...arr]);
        this.data.set('source', firstValue);
        this.getComList();
    }

    // 新建权限
    onCreate() {
        const dialog = new AuthorityDialog({
            data: {
                clusterId: this.data.get('clusterId'),
                version: this.data.get('detail.version')
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.resetTable());
    }

    // 删除
    onDelete(row: ClusterAuthorityItem) {
        Dialog.warning({
            content: '您确定删除此权限吗？',
            okText: '确定',
            onOk: async () => {
                await api.deleteClusterAuthority(this.data.get('clusterId'), row);
                Notification.success('删除成功');
                this.resetTable();
            }
        });
    }

    tableFilter(e: ColumnsFilter) {
        const {field, filter} = e;
        const columnsIndex = this.data.get('table.columns').findIndex((item: ColumnsItem) => item.name === field.name);
        this.data.set(`table.columns[${columnsIndex}].filter.value`, filter.value);
        this.getComList();
    }

    // refresh更新
    refreshInfo() {
        return Promise.all([this.getComList(), this.getUserList()]);
    }
}
