/**
* 用户/权限管理无权限时展示的页面
*
* @file no-right.ts
* <AUTHOR>
*/
import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {Table, Button, Form, Checkbox, Switch, Alert, Notification} from '@baidu/sui';
import {MultiToneSuccess, MultiToneError} from '@baidu/sui-icon';
import Tip from '@/components/tip';
import {AppLegend} from '@baidu/sui-biz';
import {VAILDITE_ITEMS, FORBID_HANDLER} from '@/common/rules';
import {authenticationModesEnum} from '@/common/enums/constant';
import TABLE_COLUMNS from '@/common/config/columns';
import {sasl, none, nonevpc, saslnetwork, sasl_plain, sasl_plain_network} from '@/common/config';

import api from '@/common/client';
import './index.less';

const klass = 'bms-no-right-page';

const protocolColumns = _.cloneDeep(TABLE_COLUMNS.protocolColumns);

const NoneText = {
    textAble: '客户端无需身份认证，并且允许所有操作',
    textDisabled: '暂不支持开启权限管理功能下使用此认证方式',
};

const TextMap = {
    title: '访问配置',
    access: '权限控制：',
    mode: '认证方式：',
    protocol: '访问协议：'
};

export default class NoRight extends Component {

    static template = html`
    <div class="${klass}">
        <div class="${klass}_content">
            <div class="title">{{type === 'user' ? '用户管理' : '权限管理'}}</div>
            <div class="description">{{description}}</div>
            <div class="form-area bms-form-panel">
                <s-append noHighlight label="${TextMap.title}">
                    <div slot="extra">
                        <span class="access-tip">谨慎修改认证方式，可能导致已有访问失效。</span>
                    </div>
                </s-append>
                <s-alert skin="warning" class="no-right-alert">
                    {{warn}}
                </s-alert>
                <s-form
                    s-ref="form"
                    label-align="left"
                    class="right-form"
                    rules="{{rules}}"
                    data="{{formData}}"
                >
                    <s-form-item label="${TextMap.mode}" prop="authenticationModes">
                        <s-checkbox-group
                            class="access-config_check-group"
                            value="{= formData.authenticationModes =}"
                        >
                            <s-checkbox
                                class="s-checkbox"
                                disabled="{{true}}"
                                label="{{None.label}}"
                                value="{{None.value}}"
                                on-change="onNoneChange"
                                checked="{{None.checked}}"
                            />
                            <p class="desc mt4 mb24">{{None.text}}</p>
                            <s-checkbox
                                class="s-checkbox"
                                disabled="{{Sasl.disabled || changing}}"
                                label="{{Sasl.label}}"
                                value="{{ Sasl.value }}"
                                on-change="onSaslChange"
                                checked="{{Sasl.checked}}"
                            />
                            <p class="desc mt4 mb24">{{Sasl.text}}</p>
                            <s-checkbox
                                class="s-checkbox"
                                disabled="{{SaslPlain.disabled || changing}}"
                                label="{{SaslPlain.label}}"
                                value="{{ SaslPlain.value }}"
                                on-change="onSaslPlainChange"
                                checked="{{SaslPlain.checked}}"
                            />
                            <p class="desc mt4 mb24">{{SaslPlain.text}}</p>
                        </s-checkbox-group>
                        <span slot="error" s-if="saslErr">{{type === 'user' ? '用户管理' : '权限管理'}}功能需开启Sasl访问权限</span>
                    </s-form-item>
                    <s-form-item class="form-item-center">
                        <span slot="label">
                            ${TextMap.access}
                            <tip-cmpt placement="right" type="question">ACL访问权限功能开关</tip-cmpt>
                        </span>
                        <s-switch
                            checked="{= formData.aclEnabled =}"
                            disabled="{{aclEnabledDisable}}"
                            on-change="handleSwitchAccess"
                        />
                        <span slot="error" s-if="aclErr">权限管理功能需开启权限控制按钮</span>
                    </s-form-item>
                    <s-form-item label="${TextMap.protocol}" s-if="datasource.length > 0">
                        <s-table
                            class="protocol-table"
                            columns="{{protocolColumns}}"
                            datasource="{{datasource}}"
                        >
                            <div slot="c-aclEnable">
                                <s-icon-succ s-if="row.aclEnable"></s-icon-succ>
                                <s-icon-err s-else></s-icon-err>
                            </div>
                            <div slot="c-secret">
                                <s-icon-succ s-if="row.secret"></s-icon-succ>
                                <s-icon-err s-else></s-icon-err>
                            </div>
                            <div slot="c-userVision">
                                <s-icon-succ s-if="row.userVision"></s-icon-succ>
                                <s-icon-err s-else></s-icon-err>
                            </div>
                        </s-table>
                    </s-form-item>
                </s-form>
            </div>
            <s-button skin="primary" on-click="onConfirm" disabled="{{confirmDisable}}">
                {{changing ? '开启中...' : '确定开启'}}
            </s-button>
        </div>
    </div>`;

    static components = {
        's-table': Table,
        's-button': Button,
        's-form': Form,
        's-form-item': Form.Item,
        's-switch': Switch,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-checkbox': Checkbox,
        'tip-cmpt': Tip,
        's-icon-succ': MultiToneSuccess,
        's-icon-err': MultiToneError,
        's-append': AppLegend,
        's-alert': Alert
    };

    initData() {
        return {
            type: 'user',
            rules: {
                authenticationModes: [VAILDITE_ITEMS.requiredCheckGroup],
            },
            formData: {
                aclEnabled: false,
                authenticationModes: []
            },
            None: {
                value: 'NONE',
                label: authenticationModesEnum.NONE,
                text: NoneText.textAble,
                disabled: false,
                checked: false
            },
            Sasl: {
                value: 'SASL_SCRAM',
                label: authenticationModesEnum.SASL_SCRAM,
                text: 'SCRAM使用SASL框架提供用户名和密码验证方法',
                disabled: false,
                checked: false
            },
            SaslPlain: {
                value: 'SASL_PLAIN',
                label: authenticationModesEnum.SASL_PLAIN,
                text: 'PLAIN使用SASL框架提供用户名和密码验证方法',
                disabled: false,
                checked: false
            },
            protocolColumns: protocolColumns,
            changing: false
        };
    }

    static computed: SanComputedProps = {
        description(): string {
            const type = this.data.get('type');
            if (type === 'user') {
                return '用户管理采用 SASL 认证，支持 SCRAM-SHA-512和PLAIN 机制，实现动态新增用户，不重启 Kafka 集群即可进行鉴权。客户端通过用户名、密码进行 Kafka 连接认证，当不存在或者错误的用户信息时将无法连接 Kafka 服务。';
            }
            else {
                return '权限管理与用户管理协同，为用户的资源绑定恰当的操作权限，实现客户端只能对授权的资源进行访问。其中，授权资源包括主题、消费组、集群和事务，授权粒度包括精确匹配和前缀匹配，操作类型包括读、写以及幂等写。';
            }
        },
        warn(): string {
            const type = this.data.get('type');
            if (type === 'user') {
                return '使用『用户管理』功能，需要先开启『SASL/SCRAM』 或 『SASL/PLAIN』身份认证方式，可以创建集群时开通或者创建集群后变更开启。';
            }
            else {
                return '使用『权限管理』功能，需要先开启『SASL/SCRAM』 或 『SASL/PLAIN』身份认证方式，打开『权限控制』开关，可以创建集群时开通或者创建集群后变更开通。';
            }
        },
        confirmDisable() {
            const changing = this.data.get('changing');
            const status = this.data.get('detail.status');
            return !FORBID_HANDLER.checkClusterUpgrage(status) || changing;
        },
        aclEnabledDisable(): boolean {
            const Sasl = this.data.get('Sasl.checked');
            const SaslPlain = this.data.get('SaslPlain.checked');
            const changing = this.data.get('changing');
            return changing || (!Sasl && !SaslPlain);
        }
    };

    attached() {
        const {aclEnabled, authenticationModes, publicIpEnabled, intranetIpEnabled} = this.data.get('detail');
        this.data.merge('formData', {aclEnabled, authenticationModes});
        this.data.set('Sasl.checked', _.includes(authenticationModes, 'SASL_SCRAM'));
        this.data.set('SaslPlain.checked', _.includes(authenticationModes, 'SASL_PLAIN'));
        this.data.set('None.checked', _.includes(authenticationModes, 'NONE'));
        _.includes(authenticationModes, 'SSL') && this.data.push('authenticationModes', 'SSL');
        if (!publicIpEnabled && intranetIpEnabled) {
            this.data.set('None.disabled', true);
            // this.data.set('Sasl.disabled', false);
        } else if (publicIpEnabled && intranetIpEnabled) {
            // this.data.set('Sasl.disabled', true);
            this.data.set('None.disabled', true);
        } else if (publicIpEnabled && !intranetIpEnabled) {
            // this.data.set('Sasl.disabled', true);
            this.data.set('None.disabled', false);
        } else {
            // this.data.set('Sasl.disabled', false);
            this.data.set('None.disabled', false);
        }
        this.handleChange();
    };

    handleSwitchAccess(target: {value: boolean}) {
        this.data.set('formData.aclEnabled', target.value);
        this.handleChange();
    }

    handleChange() {
        let datasource = [];
        const {publicIpEnabled, intranetIpEnabled} = this.data.get('detail');
        const aclEnabled = this.data.get('formData.aclEnabled');
        sasl.aclEnable = aclEnabled;
        saslnetwork.aclEnable = aclEnabled;
        sasl_plain.aclEnable = aclEnabled;
        sasl_plain_network.aclEnable = aclEnabled;
        if (this.data.get('None.checked')) {
            datasource = datasource.concat(intranetIpEnabled ? [none, nonevpc] : [none]);
        }
        if (this.data.get('Sasl.checked')) {
            datasource = datasource.concat(publicIpEnabled ? [sasl, saslnetwork] : [sasl]);
        }
        if (this.data.get('SaslPlain.checked')) {
            datasource = datasource.concat(publicIpEnabled ? [sasl_plain, sasl_plain_network] : [sasl_plain]);
        }
        this.data.set('datasource', _.cloneDeep(datasource));
    }

    onSaslChange(target: {value: boolean}) {
        this.data.set('Sasl.checked', target.value);
        const SaslPlain = this.data.get('SaslPlain.checked');
        this.handleSwitchAccess({value: SaslPlain || target.value});
        this.handleChange();
    }

    onSaslPlainChange(target: {value: boolean}) {
        this.data.set('SaslPlain.checked', target.value);
        const Sasl = this.data.get('Sasl.checked');
        this.handleSwitchAccess({value: Sasl || target.value});
        this.handleChange();
    }

    onNoneChange(target: {value: boolean}) {
        this.data.set('None.checked', target.value);
        this.handleChange();
    }

    onConfirm() {
        const {aclEnabled, authenticationModes} = this.data.get('formData');
        const type = this.data.get('type');
        if (!_.includes(authenticationModes, 'SASL_SCRAM') && !_.includes(authenticationModes, 'SASL_PLAIN')) {
            this.data.set('saslErr', true);
            return;
        }
        else if (type === 'authority' && !aclEnabled) {
            this.data.set('aclErr', true);
            return;
        }
        else {
            this.data.set('saslErr', false);
            this.data.set('aclErr', false);
        }
        const params = {
            aclEnabled,
            authenticationMode: authenticationModes,
            type: 'UPDATE_ACCESS_CONFIG'
        };
        api.updateCluster(this.data.get('clusterId'), params)
            .then((target: {orderId: string; clusterId: string}) => {
                Notification.success('变更提交成功');
                this.data.set('changing', true);
            });
    }
}
