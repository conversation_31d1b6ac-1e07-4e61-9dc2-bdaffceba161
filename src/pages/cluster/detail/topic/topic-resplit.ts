/* eslint-disable max-len */
/**
 * 主题批量重分区
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import m from 'moment';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Form, Notification, Select, DatePicker, Radio, InputNumber, Button, Transfer} from '@baidu/sui';
import {maxBy} from '@/common/util';
import api from '@/common/client';
import {TOPIC_RESPLIT_VALIDATE} from '@/common/rules';
import {debounce} from '@/common/decorator';

const klass = 'topic-resplit';
export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="重新分区"
            class="formate-drawer"
            open="{{open}}"
            maskClose="{{true}}"
            otherClose="{{true}}"
            on-close="onClose"
            getContainer="{{getContainer}}"
            size="700"
        >
            <s-form
                class="form-format ${klass}"
                s-ref="form"
                rules="{{rules}}"
                data="{= formData =}">
                <s-form-item label="选择主题：" prop="topics">
                    <s-select
                        multiple
                        required
                        width="400"
                        clearable
                        filterable
                        labelFilter="{{labelFilter}}"
                        value="{{formData.topics}}"
                        datasource="{{newList}}"
                        on-change="onTopicChange"
                        on-query-change="onTopicQuery"
                    ></s-select>
                </s-form-item>
                <s-form-item label="目标节点：" prop="brokerIds">
                    <s-transfer
                        titles="{{brokerTitles}}"
                        height="260"
                        datasource="{{brokerList}}"
                        filter="{{true}}"
                        on-change="onBrokerChange"
                    />
                </s-form-item>
                <s-form-item label="分批执行：" required class="form-item-center">
                    <s-radio-group
                        datasource="{{radioGroup.batchSource}}"
                        value="{= isBatch =}"
                        disabled="{{loading}}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item
                    s-if="isBatch"
                    label="分区个数："
                    class="inline-form-item"
                    prop="batchSize"
                >
                    <s-input-number
                        min="{{1}}"
                        max="{{500}}"
                        width="160"
                        disabled="{{loading}}"
                        value="{= formData.batchSize =}"
                    />个
                    <p slot="help">
                        任务分批执行时，每个批次执行的分区个数，当前涉及分区总数为
                        <span class="yellow-text">{{formData.totalPartitionNum}}</span>
                        个
                    </p>
                </s-form-item>
                <s-form-item label="流量限制：" required class="form-item-center">
                    <s-radio-group
                        datasource="{{radioGroup.throttleSource}}"
                        value="{= isThrottle =}"
                        disabled="{{loading}}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item
                    s-if="isThrottle"
                    label="流量限额："
                    class="inline-form-item"
                    prop="interBrokerThrottle"
                >
                    <s-input-number
                        min="{{interBrikerThrottleMin}}"
                        max="{{2147483647}}"
                        width="160"
                        stepStrictly
                        disabled="{{loading}}"
                        value="{{formData.interBrokerThrottle}}"
                        on-change="handleIntBrokerThrottleChange"
                    />MB/s
                    <p slot="help">节点数据迁移流量限制，最小限制为
                        <span class="yellow-text">{{interBrikerThrottleMin}}</span>
                        MB/s
                    </p>
                </s-form-item>
                <s-form-item
                    prop="duration"
                    s-if="isThrottle"
                    label="预计耗时："
                    class="form-item-center"
                >
                    <span class="yellow-text">{{formData.duration | formateDuration}}</span>
                </s-form-item>
                <s-form-item label="执行时间：" required class="form-item-center">
                    <s-radio-group
                        datasource="{{radioGroup.timeSource}}"
                        value="{= isNow =}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item s-if="!isNow" label="执行时间：" class="inline-form-item" prop="startTime">
                    <s-date-picker width="160" value="{= formData.startTime =}" range="{{range}}" mode="second" />
                    <p slot="help">可选择未来 24 小时之内的时间</p>
                </s-form-item>
            </s-form>
            <div class="${klass}__bottom">
                <s-button on-click="onClose">取消</s-button>
                <s-button
                    skin="primary"
                    class="ml12"
                    on-click="onConfirm">
                    确定
                </s-button>
            </div>
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-option': Select.Option,
        's-date-picker': DatePicker,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-input-number': InputNumber,
        's-button': Button,
        's-transfer': Transfer
    };

    static filters = {
        formateDuration(value: number) {
            const duration = m.duration(value, 'seconds');
            return `${duration.days()} 天 ${duration.hours()} 时 ${duration.minutes()} 分 ${duration.seconds()} 秒`;
        }
    };

    static computed = {
        interBrikerThrottleMin() {
            const minThrottle = this.data.get('formData.minThrottle');
            return Math.ceil(minThrottle) > 1 ? Math.ceil(minThrottle) : 1;
        }
    };

    initData() {
        return {
            open: true,
            formData: {
                startTime: null,
                brokerIds: [],
                topics: [],
                interBrokerThrottle: 1,
                batchSize: 1,
                totalPartitionNum: 0,
                minThrottle: 0
            },
            isNow: true,
            isBatch: true,
            isThrottle: true,
            radioGroup: {
                batchSource: [
                    {label: '指定批次大小', value: true},
                    {label: '否', value: false}
                ],
                throttleSource: [
                    {label: '指定限制流量', value: true},
                    {label: '否', value: false}
                ],
                timeSource: [
                    {label: '立即执行', value: true},
                    {label: '自定义时间', value: false},
                ],
            },
            labelFilter(labels: string[]) {
                if (labels.length >= 5) {
                    return labels.slice(0, 4).join(',') + ' ...';
                } else {
                    return labels.join(',');
                }
            },
            getContainer: document.getElementById('main'),
            brokerTitles: ['待选节点', '已选节点'],
            rules: {
                topics: [{required: true, message: '请选择主题'}],
                brokerIds: [
                    {required: true, message: '请选择节点'},
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            const topics = this.data.get('formData.topics');
                            const topicsSelected = _.filter(this.data.get('newList'), i => topics.includes(i.value));
                            const maxRep = maxBy(topicsSelected, 'replicaNum');
                            if (value.length < maxRep) {
                                return callback(`请至少选择${maxRep}个节点`);
                            }
                            callback();
                        }
                    }
                ],
                ...TOPIC_RESPLIT_VALIDATE
            },
        };
    }

    inited() {
        const current = new Date().getTime() + 3 * 60 * 1000;
        const nextDay = current + 24 * 60 * 60 * 1000;
        const range = {
            begin: new Date(current),
            end: new Date(nextDay)
        };
        this.data.set('range', range);
    }

    async attached() {
        await this.getTopicList();
        this.data.set('newList', this.data.get('topicList'));
        this.watch('topicList', v => this.data.set('newList', this.data.get('topicList')));
        this.watch('formData.batchSize', this.getParameters);
        this.watch('isBatch', this.getParameters);
    }

    onTopicChange(target: {value: string[]}) {
        this.data.set('formData.topics', target.value);
        this.getReSplitPlan();
    }

    handleIntBrokerThrottleChange(target: {value: number}) {
        this.data.set('formData.interBrokerThrottle', target.value);
        this.nextTick(async () => {
            await this.getParameters();
        });
    }

    async getTopicList() {
        await api.listClusterTopic(this.data.get('clusterId'), {})
            .then((target: Array<{topicName: string, replicaNum: string, readOnly: boolean}>) => {
                const arr = _.map(target, s => ({text: s.topicName, value: s.topicName, replicaNum: s.replicaNum}));
                this.data.set('topicList', arr);
            });
    }

    @debounce(500)
    getParameters() {
        const {formData, clusterId, isBatch} = this.data.get('');
        const {
            currentAssignments,
            proposedAssignments,
            batchSize,
            interBrokerThrottle,
        } = formData;
        if (!proposedAssignments || !currentAssignments) {
            return;
        }
        let params = {
            currentAssignments,
            proposedAssignments,
            interBrokerThrottle
        };
        if (isBatch) {
            params = {
                ...params,
                batchSize
            };
        }
        api.getParameters(clusterId, params).then((res) => {
            this.data.merge('formData', {
                minThrottle: res.minThrottle.toFixed(4),
                duration: res.duration
            });
        });
    }

    async getReSplitPlan() {
        const {formData, clusterId} = this.data.get('');
        const {topics, brokerIds} = formData;
        const topicsSelected = _.filter(this.data.get('newList'), i => topics.includes(i.value));
        const maxRep = maxBy(topicsSelected, 'replicaNum');
        let params = {
            topics,
            brokerIds,
        };
        if (topics.length === 0 || brokerIds.length < 1 || maxRep > brokerIds.length) {
            return;
        }
        this.data.set('loading', true);
        await api.getReSplitPlan(clusterId, params).then((res) => {
            this.data.merge('formData', {
                currentAssignments: res.currentAssignments,
                proposedAssignments: res.proposedAssignments,
                interBrokerThrottle: formData.interBrokerThrottle > Math.ceil(res.minThrottle) ? formData.interBrokerThrottle : Math.ceil(res.minThrottle),
                minThrottle: res.minThrottle.toFixed(4),
                duration: res.duration,
                totalPartitionNum: res.totalPartitionNum,
            });
            this.data.set('loading', false);
            this.getParameters();
        });
    }

    // 确认
    async onConfirm() {
        try {
            await (this.ref('form') as unknown as Form).validateFields();
            const {formData, isBatch, isNow, isThrottle} = this.data.get('');
            const {topics, brokerIds} = this.data.get('formData');
            const topicsSelected = _.filter(this.data.get('newList'), i => topics.includes(i.value));
            const maxRep = maxBy(topicsSelected, 'replicaNum');

            if (brokerIds.length < maxRep) {
                return;
            }

            let params = {
                topics,
                brokerIds,
                batchSize: isBatch ? formData.batchSize : null,
                interBrokerThrottle: isThrottle ? formData.interBrokerThrottle : null,
                proposedAssignments: formData.proposedAssignments,
                currentAssignments: formData.currentAssignments
            };
            if (!isNow) {
                params = {
                    ...params,
                    startTime: formData.startTime.getTime()
                };
            }
            await api.resplitClusterTopic(this.data.get('clusterId'), params);
            Notification.success('保存成功');
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
        }
    }

    onBrokerChange(target: {value: Array<{value: string}>}) {
        let brokerIds: number[] = [];
        target.value.forEach((item: {value: string}) => {
            brokerIds.push(parseInt(item.value));
        });
        this.data.set('formData.brokerIds', brokerIds);
        this.getReSplitPlan();
    }

    onTopicQuery(target: {value: string}) {
        const topicList = this.data.get('topicList');
        const newList = _.filter(topicList, i => i.text.indexOf(target.value) > -1);
        this.data.set('newList', !target.value ? _.cloneDeep(topicList) : _.cloneDeep(newList));
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
