/**
* 主题下的一些公用样式
*/
@white-color: #fff;
@errColor: #f33e3e;
@height: 120px;
@borderColor: #E8E9EB;

.topic__text_distance {
    margin-right: 154px;
}

.inline-form-item {
    margin-left: 83px;
}

.topic-single-resplit {
    width: 650px;
    .s-form-item-label {
        label {
            display: inline-block;
            min-width: 67px;
        }
    }

    &__bottom {
        position: absolute;
        right: 40px;
        bottom: 20px;
    }
}

.formate-drawer {
    .s-drawer-content {
        border-bottom: 1px solid @borderColor;
        height: calc(~"100% - @{height}")!important;
    }
    .s-option {
        .s-checkbox {
            width: 14px;
            display: inline-block;
        }
    }
}

.topic-resplit {
    width: 650px;
    &__bottom {
        position: absolute;
        right: 40px;
        bottom: 20px;
    }
}

.partition-table {
    width: 552px;

    .s-table-row:hover {
        .s-select {
            background-color: @white-color;
        }
    }
}

.partition-error {
    color: @errColor;
}

.error-status {
    .s-input-suffix-container {
        border-color: @errColor;
    }
}

.s-error-tooltip-content {
    .s-tooltip-content {
        color: #f33e3e;
    }
}

.yellow-text {
    color: #FFAB52;
}
