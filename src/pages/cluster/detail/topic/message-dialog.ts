/* eslint-disable max-len */
/**
 * 消息发送弹窗
 *
 * @file message-dialog.ts
 * <AUTHOR>
 */
import {Component, defineComponent} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Input, Switch, Form, Select, Notification} from '@baidu/sui';
import api from '@/common/client';
import {ROUTE_PATH} from '@/common/config';
import {pickEmpty} from '@/common/util';
const klass = 'message-send';
class MessageSend extends Component {
    static template = html`
    <template>
        <s-dialog
            open="{{open}}"
            class="${klass}"
            title="消息发送"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}"
            on-confirm="onConfirm"
        >
            <s-form
                s-ref="form"
                label-align="right"
                class="form-format"
                rules="{{rules}}"
                data="{= formData =}">
                <s-form-item label="消息内容：" prop="value">
                    <s-textarea width="300" height="100" value="{=formData.value=}" placeholder="请输入" maxLength="2000"/>
                </s-form-item>
                <s-form-item label="消息key：" prop="key">
                    <s-textarea
                        maxLength="128"
                        width="300"
                        height="100"
                        value="{=formData.key=}"
                    />
                </s-form-item>
                <s-form-item label="指定分区：" prop="partitionSwitch" class="form-item-center">
                    <s-switch value="{{formData.partitionSwitch}}" on-change="changeSwitch"></s-switch>
                </s-form-item>
                <s-form-item label="分区ID：" s-if="{{formData.partitionSwitch}}" prop="partitionId">
                    <s-select
                        class="ml12"
                        width="200"
                        value="{=formData.partitionId=}"
                        datasource="{{partitonSource}}"
                    />
                </s-form-item>
            </s-form>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-switch': Switch,
        's-textarea': Input.TextArea,
        's-select': Select
    };

    initData() {
        return {
            formData: {
                key: '',
                value: '',
                partitionSwitch: false,
                partitionId: null
            },
            rules: {
                value: [
                    {required: true, message: '请填写'},
                ],
                partitionId: [
                    {required: true, message: '请选择'},
                ]
            },
            open: true,
            confirming: false,
        };
    }

    attached() {
        this.getInfo();
    }

    changeSwitch(target: {value: string}) {
        this.data.set('formData.partitionSwitch', target.value);
    }

    getInfo() {
        const {topicName, clusterId} = this.data.get('');
        api.getClusterTopicDetail(clusterId, topicName, {})
            .then((detail: object) => {
                const partitionNum = detail.partitionNum;
                const partitonSource = Array.from({length: partitionNum}, (val, i) => ({label: i, value: i}));
                this.data.set('partitonSource', partitonSource);
            });
    }

    async onConfirm() {
        try {
            const {topicName, clusterId, formData, clusterName} = this.data.get('');
            await this.ref('form')?.validateFields();
            let params = pickEmpty({
                key: formData.key,
                value: formData.value,
            });
            params = {
                ...params,
                partitionId: formData.partitionId
            };
            this.data.set('confirming', true);
            await api.messageSend(clusterId, topicName, params);
            const tip = defineComponent({
                template: `
                    <span>
                        消息已发送成功，可前往
                        <a href='#${ROUTE_PATH.clusterDetailTopicDetailMessage}?&topic=${topicName}&clusterId=${clusterId}&name=${clusterName}' target="blank">
                            消息查询
                        </a>
                        功能进行验证
                    </span>`
            });
            Notification.success(tip);
            this.data.set('confirming', false);
            this.onClose();
        }
        catch (error) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}

export default MessageSend;
