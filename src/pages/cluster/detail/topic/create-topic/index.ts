/**
 * 创建主题（和共享版创建主题不一致）
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Input, Form, Select, Notification, InputNumber} from '@baidu/sui';
import Tip from '@/components/tip';
import {DIALOG_INPUT_WIDTH} from '@/common/config';
import {VAILDITE_ITEMS, RULES, TopicNameRegTip} from '@/common/rules';
import api from '@/common/client';

import {TopicOptions} from './topic-options';

import './index.less';

const kDialogClass = 'create-cluster-topic';

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="${kDialogClass} ue-dialog"
            title="创建主题"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            width="800"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <s-form
                s-ref="form"
                rules="{{rules}}"
                class="form-format"
                required="{{true}}"
                data="{= formData =}">
                <s-form-item label="主题名称：" prop="topicName">
                    <s-input width="{{inputWidth}}}" value="{= formData.topicName =}" />
                    <p class="desc mt4">${TopicNameRegTip}</p>
                </s-form-item>
                <s-form-item label="分区数：">
                    <s-input-number
                        value="{= formData.partitionNum =}"
                        max="{{500}}"
                        min="{{1}}"
                        step="{{1}}"
                        stepStrictly
                    />
                    <div class="desc mt4">为保障每个节点存储和流量均衡，建议分区数是节点数的整数倍，当前节点数为{{numberOfBrokerNodes}}</div>
                </s-form-item>
                <s-form-item>
                    <span slot="label">
                        副本数：
                        <tip-cmpt type="question">
                            为保障主题的高可用，主题副本数不能低于集群可用区数，当前集群可用区数为
                            <span style="color: #FF9326">{{zoneNum}}</span>。
                        </tip-cmpt>
                    </span>
                    <s-input-number
                        value="{= formData.replicationFactor =}"
                        on-change="onReplicationFactorChange"
                        min="{{replicationFactorMin}}"
                        max="{{replicationFactorMax}}"
                    />
                    <div class="desc mt5">最大副本数：{{replicationFactorMax}}</div>
                </s-form-item>
                <s-form-item class="topic-option">
                    <span slot="label">
                        高级配置：
                        <tip-cmpt type="question">
                            参数配置优先使用主题中的配置项和内容，未设置时默认使用集群级别的配置项和内容
                        </tip-cmpt>
                    </span>
                    <topic-options
                        s-ref="topic-options"
                        clusterId="{{clusterId}}"
                        replicationFactorMax="{{formData.replicationFactor}}"
                    />
                </s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static computed = {
        replicationFactorMin(): number {
            const deployType = this.data.get('deployType');
            const zoneNum = this.data.get('zoneNum');
            const unlimitedReplicaNumWhiteList = this.data.get('unlimitedReplicaNumWhiteList');
            return unlimitedReplicaNumWhiteList ? 1 : deployType === 'HP' ? 2 : zoneNum;
        }
    };

    static components = {
        's-dialog': Dialog,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        'topic-options': TopicOptions,
        'tip-cmpt': Tip
    };

    initData() {
        return {
            rules: {
                topicName: [
                    VAILDITE_ITEMS.requiredInput,
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (!RULES.topicName.test(value)) {
                                return callback('输入格式错误');
                            }
                            callback();
                        }
                    }
                ],
            },
            open: true,
            inputWidth: DIALOG_INPUT_WIDTH,
            formData: {
                partitionNum: 1,
                replicationFactor: 3
            },
            replicationFactorMax: 3
        };
    }

    async attached() {
        const featureTypes = ['UnlimitedReplicaNumWhiteList'];
        const {isExist: unlimitedReplicaNumWhiteList} = await api.getUserAcls({featureTypes});
        this.data.set('unlimitedReplicaNumWhiteList', unlimitedReplicaNumWhiteList);
    }

    // 副本数改变
    onReplicationFactorChange(target: {value: number}) {
        const topicOptionsNode = (this.ref('topic-options') as TopicOptions);
        topicOptionsNode.resetByReplicationFactor(target);
    }

    // 确认
    async onConfirm() {
        try {
            const topicOptionsNode = (this.ref('topic-options') as TopicOptions);
            const formNode = (this.ref('form') as unknown as Form);
            await Promise.all([formNode.validateFields(), topicOptionsNode.validate()]);
            this.data.set('confirming', true);
            const {clusterId, formData: {topicName, partitionNum, replicationFactor}} = this.data.get();
            const otherConfigs = topicOptionsNode.getData();
            await api.createClusterTopic(clusterId, {
                topicName,
                partitionNum,
                replicationFactor,
                otherConfigs
            });
            Notification.success('新建主题成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
