/**
 * @file index.less
 * <AUTHOR>
 */
@TopicOptions: topic-option;
@titleColor: #151b26;

.@{TopicOptions} {
    .s-row-flex {
        flex-direction: row;
        flex-wrap: nowrap;
    }

    &__config {
        margin-bottom: 12px;

        .s-form {
            display: inline-flex;
            padding: 0;
            align-items: center;
            width: 100%;
            margin-bottom: 24px;

            &:last-child {
                margin-bottom: 0;
            }

            .s-select,
            .s-input {
                background: var(--whiteColor);
                min-width: 116px;

                .s-input-suffix {
                    flex: 1;
                    text-align: right;
                }
            }

            &-item {
                margin-bottom: 0;
            }

            .s-form-item-label {
                flex: none;
                >label {
                    padding: 0;
                }
            }

            .s-form-item-control-wrapper {
                width: 200px;

                .s-input {
                    width: 200px;

                    .s-input-area {
                        width: 100px;
                    }
                    .s-input-addon-before {
                        padding: 0;
                    }
                    .s-input-addon-after {
                        padding: 0;
                        .s-select {
                            width: 100px!important;
                            min-width: 100px;
                            background-color: #f7f7f9;

                            .s-input {
                                width: 100px!important;
                                min-width: 100px;
                                background-color: #f7f7f9;
                            }
                        }
                    }
                }
            }
        }

        &_text {
            display: block;
            margin-top: 5px;
            width: 110px;
            overflow: hidden;

            .s-trigger-container {
                width: 100%;
            }
        }

        &_close {
            flex: 1;
            text-align: right;
            margin-top: -3px;
        }
    }

    &__add {
        display: block;

        .s-button {
            color: var(--defaultColor);
            border: none;
            padding: 0;

            .create-icon {
                fill: var(--defaultColor);
            }
        }
    }

    &__del {
        color: var(--defaultColor);
        border: none;
        padding: 0;
    }
}

.@{TopicOptions}-dialog {
    .s-dialog-content {
        width: 700px;

        .@{TopicOptions}__config {
            margin-bottom: 0;
        }
    }
}
