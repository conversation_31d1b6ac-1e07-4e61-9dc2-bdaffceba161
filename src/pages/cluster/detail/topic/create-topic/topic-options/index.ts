/**
 * 创建主题-高级配置
 *
 * @file TopicOptions.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Input, Form, Select, InputNumber, Loading, Notification, Tooltip, Button} from '@baidu/sui';
import {OutlinedClose} from '@baidu/sui-icon';
import CreateBtn from '@/components/create-btn';
import api from '@/common/client';
import {tranferArrayToObject} from '@/common/util';
import TipSelect from '@/components/tip-select';
import {TIMERANGE_LIST} from '@/common/enums/constant';
import {UnitMap} from '../../conf';

import './index.less';

const klass = 'topic-option';
const SelectWidth = 152;

type TopicConfigOption = {
    name: string;
    type: string;
    unit: keyof typeof UnitMap;
    scope: number[];
    value: string;
    description: string;
    valueSet: string[];
};

const MinInsyncReplicas = 'min.insync.replicas';

export class TopicOptions extends Component {
    static template = html`
    <div class="${klass}">
        <template s-if="{{!loadingConf}}">
            <div class="${klass}__config" s-if="{{list && list.length}}">
                <s-form
                    s-for="item,index in list"
                    s-ref="topic-options-{{index}}"
                    rules="{{rules}}"
                    required="{{true}}"
                    >
                    <s-form-item label="配置项：" prop="option">
                        <tip-select
                            value="{= item.option =}"
                            width="${SelectWidth}"
                            getPopupContainer="{{getPopupContainer}}"
                            datasource="{{item.selectList}}"
                            on-change="onOptionChange($event, index)"
                            s-if="{{!item.isShowOptionText}}"
                        >
                            <div slot="c-value">
                                <div>{{item.value}}</div>
                                <div>{{item.desc}}</div>
                            </div>
                        </tip-select>
                        <span s-else class="${klass}__config_text">
                            <s-tooltip>
                                <span class="${klass}__config_text_more ellipsis-single">
                                    {{item.option}}
                                </span>
                                <div slot="content">
                                    <div>{{item.value}}</div>
                                    <div>{{item.desc}}</div>
                                </div>
                            </s-tooltip>
                        </span>
                    </s-form-item>
                    <s-form-item label="内容：" class="ml30" s-if="{{item.option}}">
                        <tip-select
                            s-if="{{item.type === 'select'}}"
                            value="{= item.content =}"
                            datasource="{{item.contentList}}"
                            width="${SelectWidth}"
                        />
                        <template s-elif="{{item.name === 'min.insync.replicas'}}">
                            <s-input-number
                                value="{= item.content =}"
                                min="{{item.scope.min}}"
                                max="{{item.option === '${MinInsyncReplicas}'
                                    ? replicationFactorMax
                                    : item.scope.max
                                }}"
                                step="{{1}}"
                                stepStrictly
                                width="${SelectWidth - 40}"
                            />
                            <span>{{item.cell}}</span>
                        </template>
                        <template s-elif="{{item.type === 'number'}}">
                            <s-input
                                value="{=item.content=}"
                                on-change="onContentInput($event, index)"
                            >
                                <s-select
                                    slot="addonAfter"
                                    width="100"
                                    value="{=item.cell=}"
                                    datasource="{{item.option === 'max.message.bytes' || item.option === 'retention.bytes' ? byteList : timeList}}"
                                    on-change="handleChangeCell(index, $event)"
                                >
                                </s-select>
                            </s-input>
                        </template>
                    </s-form-item>
                    <s-button
                        class="${klass}__del ml8"
                        s-if="{{item.hasClose}}"
                        on-click="onClose(index)"
                        disabled="{{disabled}}">
                        删除
                    </s-button>
                </s-form>
                <div class="err" s-if="{{err}}">{{err}}</div>
            </div>
            <create-btn
                s-if="{{hasCreate}}"
                class="${klass}__add"
                text="添加配置项"
                on-click="onAddOption"
                skin=""
            />
        </template>
        <s-loading loading s-else />
    </div>`;

    initData() {
        return {
            hasTitle: true,
            hasCreate: true,
            rules: {},
            list: [],
            getPopupContainer: () => document.body,
            byteList: [
                {
                    text: 'bytes',
                    value: 'bytes'
                },
                {
                    text: 'KB',
                    value: 'KB'
                },
                {
                    text: 'MB',
                    value: 'MB'
                }
            ],
            timeList: [
                {
                    text: '毫秒',
                    value: 'ms'
                },
                {
                    text: '秒',
                    value: 's'
                },
                {
                    text: '分钟',
                    value: 'minute'
                },
                {
                    text: '小时',
                    value: 'hour'
                },
                {
                    text: '天',
                    value: 'day'
                }
            ]
        };
    }

    static components = {
        's-dialog': Dialog,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        's-loading': Loading,
        's-tooltip': Tooltip,
        'create-btn': CreateBtn,
        'outlined-close': OutlinedClose,
        'tip-select': TipSelect,
        's-button': Button
    };

    attached() {
        this.getProp();
    }

    // 由于副本数变化，重置内容
    resetByReplicationFactor(target: {value: number}) {
        const list = this.data.get('list');
        _.each(list, (item: {option: string, content: number | string}, index) => {
            if (item.option === MinInsyncReplicas && item.content > target.value) {
                this.data.set(`list[${index}].content`, target.value);
            }
        });
    }

    handleChangeCell(index: number, target: {value: string}) {
        this.data.set(`list[${index}].content`, '');
        this.data.set(`list[${index}].cell`, target.value);
    }

    onContentInput(target: {value: string}, index: number) {
        const temp = parseInt(target.value, 10);
        const {cell, scope} = this.data.get(`list[${index}]`);
        let max = Math.floor(this.transCell(cell, scope.max, '/'));
        let min = Math.ceil(this.transCell(cell, scope.min, '/'));
        let content = temp;
        if (isNaN(temp)) {
            content = 1;
        }
        else if (temp < min || temp > max) {
            content = temp < min ? min : max;
        }
        this.nextTick(() => this.data.set(`list[${index}].content`, content));
    }

    transCell(cell: string, value: number, type: string) {
        let result = value;
        const isMultiplication = type === '*';
        switch (cell) {
            case 'KB':
                result = isMultiplication ? value * 1024 : value / 1024;
                break;
            case 'MB':
                result = isMultiplication ? value * (1024 * 1024) : value / (1024 * 1024);
                break;
            case 's':
                result = isMultiplication ? value * 1000 : value / 1000;
                break;
            case 'minute':
                result = isMultiplication ? value * (1000 * 60) : value / (1000 * 60);
                break;
            case 'hour':
                result = isMultiplication ? value * TIMERANGE_LIST['1h'] : value / TIMERANGE_LIST['1h'];
                break;
            case 'day':
                result = isMultiplication ? value * TIMERANGE_LIST['1d'] : value / TIMERANGE_LIST['1d'];
                break;
        }
        return result;
    }

    // 获取高级配置属性
    getProp() {
        this.data.set('loadingConf', true);
        api.getClusterTopicConfigOptions({}).then((data: Array<TopicConfigOption>) => {
            const dropList = data.map(item => ({
                text: item.name,
                value: item.name,
                desc: item.description
            }));
            this.data.set('dropList', dropList);
            this.data.set('configList', data);
            this.fire('end-request', {dropList, configList: data});
        }).finally(() => this.data.set('loadingConf', false));
    }

    // 配置项转变
    onOptionChange(target: {value: string}, itemIndex: number) {
        this.data.set(`list[${itemIndex}].option`, target.value);
        this.data.merge(`list[${itemIndex}]`, this.getAutoOption(target.value));
        this.validate();
    }

    // 添加配置项
    onAddOption() {
        const dropList = this.data.get('dropList');
        const configList: Array<TopicConfigOption> = this.data.get('configList');
        if (configList.length) {
            const option = configList[0].name;
            const item = this.getAutoOption(option);
            this.data.push('list', {
                ...item,
                selectList: dropList,
                hasClose: true,
                isShowOptionText: false
            });
        }
    }

    // 处理配置信息自动处理相关
    getAutoOption(value: string) {
        const index = _.findIndex(this.data.get('configList'), (i: TopicConfigOption) => i.name === value);
        if (index === -1) {
            return {};
        }
        const configItem: TopicConfigOption = this.data.get(`configList[${index}]`);
        const isSelect = ['STRING', 'BOOLEAN'].includes(configItem.type);
        let scope: {min: number, max: number} = {min: 0, max: 0};
        if (!isSelect) {
            const [from, to] = configItem.scope;
            scope.min = +from;
            scope.max = +to;
        }
        return {
            // STRING类型为下拉框信息
            contentList: isSelect ? _.map(configItem.valueSet, i => ({text: i, value: i})) : [],
            type: isSelect ? 'select' : 'number',
            cell: configItem.unit,
            content: configItem.value,
            name: configItem.name,
            scope
        };
    }

    onClose(index: number) {
        this.data.removeAt('list', index);
        this.validate();
    }

    getData() {
        const list = _.cloneDeep(this.data.get('list'));
        list.forEach((item, index) => {
            if (item.type === 'number') {
                const content = parseInt(item.content, 10) || 1;
                list[index].content = this.transCell(item.cell, content, '*');
            }
        });
        return tranferArrayToObject(list, 'option', 'content');
    }

    validate() {
        const list: Array<{option: string, content: string}> = this.data.get('list');
        if (list.length > 0) {
            if (_.some(list, item => !item.option)) {
                this.data.set('err', '请选择配置项');
                return Promise.reject();
            }
            const newMap = new Map();
            let hasRepeat = false;
            let temp;
            for (const item of list) {
                if (newMap.has(item.option)) {
                    hasRepeat = true;
                    temp = item.option;
                    break;
                }
                else {
                    newMap.set(item.option, 1);
                }
            }
            if (hasRepeat) {
                this.data.set('err', `${temp}配置项重复，请重新配置`);
                return Promise.reject();
            }
        }
        this.data.set('err', '');
        return Promise.resolve();
    }
}

const kDialogClass = klass + '-dialog';

// 编辑高级配置使用
export class TopicOptionsDialog extends Component {
    static template = html`
    <div>
        <s-dialog
            class="${kDialogClass}"
            title="高级配置"
            width="{{700}}"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <topic-options
                s-ref="topic-options"
                hasTitle="{{false}}"
                on-end-request="onEndRequest"
                clusterId="{{clusterId}}"
                replicationFactorMax="{{replicationFactorMax}}"
                hasCreate="{{true}}"
            />
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        'topic-options': TopicOptions
    };

    initData() {
        return {
            open: true,
            list: []
        };
    }

    // 请求完数据处理
    onEndRequest(target: {
        dropList: Array<{value: string, text: string, desc: string}>,
        configList: Array<TopicConfigOption>}) {
        const list: Array<{name: string, value: string, unit: keyof typeof UnitMap}> = this.data.get('list');
        const ref = this.ref('topic-options') as TopicOptions;
        ref.data.set('list', []);
        _.each(list, item => {
            const selectItem = _.find(target.dropList, drop => drop.value === item.name);
            const temItem = ref.getAutoOption(item.name);
            ref.data.push('list', {
                ...temItem,
                option: item.name,
                selectList: [{value: item.name, text: item.name}],
                value: item.name,
                desc: selectItem?.desc || '',
                content: item.value,
                isShowOptionText: true,
                hasClose: false
            });
        });
    }

    // 确认
    async onConfirm() {
        try {
            const node = (this.ref('topic-options') as TopicOptions);
            await node.validate();
            this.data.set('confirming', true);
            const {clusterId, topic} = this.data.get('');
            const options = node.getData();
            await api.updateClusterTopic(clusterId, topic, {otherConfigs: options});
            Notification.success('编辑配置成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
