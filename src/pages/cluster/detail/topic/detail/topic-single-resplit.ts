/* eslint-disable max-len */
/**
 * 主题单个重分区
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {debounce} from '@/common/decorator';
import {Component} from 'san';
import m from 'moment';
import {html} from '@baiducloud/runtime';
import {
    Drawer,
    Form,
    Input,
    Notification,
    Select,
    DatePicker,
    Radio,
    InputNumber,
    Table,
    Button,
    Transfer,
    Tooltip
} from '@baidu/sui';
import {DIALOG_INPUT_WIDTH} from '@/common/config';
import api from '@/common/client';
import {TOPIC_RESPLIT_VALIDATE} from '@/common/rules';

const klass = 'topic-single-resplit';

type assignmentsItem = {
    topic: string;
    partition: number;
    replicas: number[];
};
export default class extends Component {
    static template = html` <div>
        <drawer-common
            title="{{'重新分区'}}"
            open="{{open}}"
            maskClose="{{true}}"
            otherClose="{{true}}"
            on-close="onClose"
            getContainer="{{getContainer}}"
            size="700"
            class="formate-drawer"
        >
            <s-form class="form-format ${klass}" s-ref="form" rules="{{rules}}" data="{= formData =}">
                <s-form-item label="分配方案：" required>
                    <s-radio-group
                        on-change="handleRadioGroupChange"
                        value="{= resplitMode =}"
                        radioType="button"
                        datasource="{{radioGroup.modeSource}}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item label="主题名称：" class="form-item-center">
                    <span>{{topic}}</span>
                </s-form-item>
                <s-form-item s-if="resplitMode === 'auto'" label="副本数： " prop="replicationFactor">
                    <s-input-number
                        width="80"
                        value="{= formData.replicationFactor =}"
                        min="{{replicationFactorMin}}"
                        max="{{3}}"
                    />
                </s-form-item>
                <s-form-item s-if="resplitMode === 'auto'" label="目标节点：" prop="brokerIds">
                    <s-transfer
                        titles="{{brokerTitles}}"
                        height="260"
                        datasource="{{brokerList}}"
                        filter="{{true}}"
                        on-change="onTransferChange"
                    />
                </s-form-item>
                <s-form-item s-if="resplitMode === 'selfConfig'" label="选择分区：" prop="partition">
                    <s-select
                        multiple
                        required
                        width="300"
                        clearable
                        filterable
                        labelFilter="{{labelFilter}}"
                        value="{=formData.partition=}"
                        datasource="{{newList}}"
                        on-change="onPartitionChange"
                        on-query-change="onPartitionQuery"
                    ></s-select>
                </s-form-item>
                <s-form-item s-if="resplitMode === 'selfConfig'" label="分配分区：" required>
                    <s-table
                        datasource="{= patitionTable.datasource =}"
                        columns="{{partitionColumns}}"
                        class="partition-table"
                        error="{{patitionTable.error}}"
                    >
                        <div slot="c-partitionId">{{row.partition}}</div>
                        <div slot="c-replicationFactor1">
                            <s-tooltip
                                content="{{row.error.name[0] ? '副本冲突' : null}}"
                                visible="{{row.error.name[0]}}"
                                class="s-error-tooltip-content"
                            >
                                <s-select
                                    s-ref="{{rowIndex + '0'}}"
                                    datasource="{{brokerList}}"
                                    value="{{row.replicas[0]}}"
                                    class="{{row.error.name[0] ? 'error-status' : ''}}"
                                    width="80"
                                    on-change="onBrokerChange($event, rowIndex, 0, row.partition)"
                                />
                            </s-tooltip>
                        </div>
                        <div slot="c-replicationFactor2">
                            <s-tooltip
                                content="{{row.error.name[1] ? '副本冲突' : null}}"
                                visible="{{row.error.name[0]}}"
                                class="s-error-tooltip-content"
                            >
                                <s-select
                                    s-ref="{{rowIndex + '1'}}"
                                    datasource="{{brokerList}}"
                                    value="{{row.replicas[1]}}"
                                    class="{{row.error.name[1] ? 'error-status' : null}}"
                                    width="80"
                                    on-change="onBrokerChange($event, rowIndex, 1, row.partition)"
                                />
                            </s-tooltip>
                        </div>
                        <div slot="c-replicationFactor3" s-if="replicationFactor === 3">
                            <s-tooltip
                                content="{{row.error.name[2] ? '副本冲突' : null}}"
                                visible="{{row.error.name[0]}}"
                                class="s-error-tooltip-content"
                            >
                                <s-select
                                    s-ref="{{rowIndex + '2'}}"
                                    datasource="{{brokerList}}"
                                    value="{{row.replicas[2]}}"
                                    class="{{row.error.name[2] ? 'error-status' : ''}}"
                                    width="80"
                                    on-change="onBrokerChange($event, rowIndex, 2, row.partition)"
                                />
                            </s-tooltip>
                        </div>
                        <span slot="c-operation">
                            <s-button
                                on-click="onDeleteBroker(row.partition, $event)"
                                skin="stringfy"
                                class="table-btn-slim"
                            >
                                删除
                            </s-button>
                        </span>
                    </s-table>
                    <span slot="extra" class="partition-error" s-if="{{showPartitionError && errorList.length && errorMsg !== ''}}"
                        >同一分区不同副本所在节点不能相同，存在问题的分区为：{{errorMsg}}</span
                    >
                </s-form-item>
                <s-form-item label="分批执行：" required class="form-item-center">
                    <s-radio-group datasource="{{radioGroup.batchSource}}" value="{= isBatch =}"> </s-radio-group>
                </s-form-item>
                <s-form-item
                    s-if="isBatch"
                    label="分区个数："
                    class="inline-form-item"
                    prop="batchSize"
                >
                    <s-input-number
                        disabled="{{loading}}"
                        min="{{1}}"
                        max="{{500}}"
                        width="160"
                        value="{= formData.batchSize =}"
                    />个
                    <p slot="help">
                        任务分批执行时，每个批次执行的分区个数，当前涉及分区总数为
                        <span class="yellow-text">{{totalPartitionNum}}</span>
                        个
                    </p>
                </s-form-item>
                <s-form-item label="流量限制：" required class="form-item-center">
                    <s-radio-group datasource="{{radioGroup.throttleSource}}" value="{= isThrottle =}"> </s-radio-group>
                </s-form-item>
                <s-form-item
                    s-if="isThrottle"
                    label="流量限额："
                    class="inline-form-item"
                    prop="interBrokerThrottle"
                >
                    <s-input-number
                        min="{{interBrikerThrottleMin}}"
                        max="{{2147483647}}"
                        width="160"
                        stepStrictly
                        disabled="{{loading}}"
                        on-change="handleIntBrokerThrottleChange"
                        value="{{formData.interBrokerThrottle}}"
                    />MB/s
                    <p slot="help">节点数据迁移流量限制，最小限制为
                        <span class="yellow-text">{{interBrikerThrottleMin}}</span>
                        MB/s
                    </p>
                </s-form-item>
                <s-form-item
                    prop="duration"
                    s-if="isThrottle"
                    label="预计耗时："
                    class="form-item-center"
                >
                    <span class="yellow-text">{{formData.duration | formateDuration}}</span>
                </s-form-item>
                <s-form-item label="执行时间：" required class="form-item-center">
                    <s-radio-group width="160" datasource="{{radioGroup.timeSource}}" value="{= isNow =}">
                    </s-radio-group>
                </s-form-item>
                <s-form-item s-if="!isNow" label="执行时间：" class="inline-form-item" prop="startTime">
                    <s-date-picker value="{= formData.startTime =}" range="{{range}}" mode="second" />
                    <p slot="help">可选择未来 24 小时之内的时间</p>
                </s-form-item>
            </s-form>
            <div class="${klass}__bottom">
                <s-button on-click="onClose">取消</s-button>
                <s-button skin="primary" class="ml12" on-click="onConfirm"> 确定 </s-button>
            </div>
        </drawer-common>
    </div>`;

    static components = {
        'drawer-common': Drawer,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-select': Select,
        's-option': Select.Option,
        's-date-picker': DatePicker,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-input-number': InputNumber,
        's-table': Table,
        's-button': Button,
        's-transfer': Transfer,
        's-tooltip': Tooltip
    };

    initData() {
        return {
            open: true,
            formData: {
                startTime: null,
                brokerIds: [],
                topics: [],
                interBrokerThrottle: 1,
                batchSize: 1,
                replicationFactor: 3,
                assignments: [],
                totalPartitionNum: 0,
                minThrottle: 0
            },
            replicationFactor: 0,
            inputWidth: DIALOG_INPUT_WIDTH,
            isNow: true,
            isBatch: true,
            isThrottle: true,
            radioGroup: {
                batchSource: [
                    {label: '指定批次大小', value: true},
                    {label: '否', value: false}
                ],
                throttleSource: [
                    {label: '指定限制流量', value: true},
                    {label: '否', value: false}
                ],
                timeSource: [
                    {label: '立即执行', value: true},
                    {label: '自定义时间', value: false},
                ],
                modeSource: [
                    {label: '自动生成方案', value: 'auto'},
                    {label: '手动配置方案', value: 'selfConfig'}
                ]
            },
            resplitMode: 'auto',
            partitionList: [],
            labelFilter(labels: string[]) {
                if (labels.length >= 8) {
                    return labels.slice(0, 7).join(',') + ' ...';
                } else {
                    return labels.join(',');
                }
            },
            partitionColumns: [
                {name: 'partitionId', label: '分区ID', width: 70},
                {name: 'replicationFactor1', label: '副本1所在节点', width: 140}
            ],
            getContainer: document.getElementById('main'),
            brokerTitles: ['待选节点', '已选节点'],
            rules: {
                ...TOPIC_RESPLIT_VALIDATE,
                replicationFactor: [{required: true, message: '请输入'}],
                brokerIds: [
                    {required: true, message: '请选择节点'},
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            const {formData, resplitMode} = this.data.get('');
                            if (value.length < formData.replicationFactor && resplitMode === 'auto') {
                                return callback('节点数需大于等于副本数');
                            }
                            callback();
                        }
                    }
                ],
                partition: [{required: true, message: '请选择分区'}]
            },
            originalPartitions: [],
            errorList: []
        };
    }

    static computed: SanComputedProps = {
        replicationFactorMin(): number {
            const deployType = this.data.get('deployType');
            const zoneNum = this.data.get('zoneNum');
            const unlimitedReplicaNumWhiteList = this.data.get('unlimitedReplicaNumWhiteList');
            if (unlimitedReplicaNumWhiteList) {
                return 1;
            }
            if (deployType === 'HP' || zoneNum === 2) {
                return 2;
            }
            else {
                return 3;
            }
        },
        interBrikerThrottleMin() {
            const minThrottle = this.data.get('formData.minThrottle');
            return Math.ceil(minThrottle) > 1 ? Math.ceil(minThrottle) : 1;
        },
        totalPartitionNum() {
            const partition = this.data.get('formData.partition') || [];
            const totalPartitionNum = this.data.get('formData.totalPartitionNum');
            const resplitMode = this.data.get('resplitMode');
            return resplitMode === 'auto' ? totalPartitionNum : partition?.length;
        }
    };

    static filters = {
        formateDuration(value: number) {
            const duration = m.duration(value, 'seconds');
            return `${duration.days()} 天 ${duration.hours()} 时 ${duration.minutes()} 分 ${duration.seconds()} 秒`;
        }
    };

    inited() {
        const current = new Date().getTime() + 3 * 60 * 1000;
        const nextDay = current + 24 * 60 * 60 * 1000;
        const range = {
            begin: new Date(current),
            end: new Date(nextDay)
        };
        this.data.set('range', range);
    }

    async attached() {
        const featureTypes = ['UnlimitedReplicaNumWhiteList'];
        const {isExist: unlimitedReplicaNumWhiteList} = await api.getUserAcls({featureTypes});
        this.data.set('unlimitedReplicaNumWhiteList', unlimitedReplicaNumWhiteList);
        this.getTopicInfo();
        await this.getPartitionList();
        this.data.set('newList', this.data.get('partitionList'));
        this.watch('partitionList', v => this.data.set('newList', this.data.get('partitionList')));
        this.watch('formData.batchSize', this.getParameters);
        this.watch('formData.partition', this.getParameters);
        this.watch('isBatch', this.getParameters);
        this.watch('formData.replicationFactor', this.getReSplitPlan);
    }

    handleIntBrokerThrottleChange(target: {value: number}) {
        this.data.set('formData.interBrokerThrottle', target.value);
        this.getParameters();
    }

    async getTopicInfo() {
        const {clusterId, topic, replicationFactorMin, unlimitedReplicaNumWhiteList} = this.data.get('');
        api.getClusterTopicDetail(clusterId, topic, {}).then((result: {replicationFactor: number}) => {
            const replicationFactor = unlimitedReplicaNumWhiteList ? result.replicationFactor : Math.max(result.replicationFactor, replicationFactorMin)
            this.data.set('formData.replicationFactor', replicationFactor);
            this.data.set('replicationFactor', replicationFactor);
            if (result.replicationFactor >= 2) {
                this.data.push('partitionColumns', {name: 'replicationFactor2', label: '副本2所在节点', width: 140});
            }
            if (result.replicationFactor === 3) {
                this.data.push('partitionColumns', {name: 'replicationFactor3', label: '副本3所在节点', width: 140});
            }
            this.data.push('partitionColumns', {name: 'operation', label: '操作', width: 60});
        });
    }

    async getParameters() {
        const {formData, clusterId, newList, topic, isBatch, resplitMode} = this.data.get('');
        const {
            batchSize,
            interBrokerThrottle,
            currentAssignments,
            proposedAssignments,
            assignments
        } = formData;
        if (!proposedAssignments || !currentAssignments) {
            return;
        }
        let originalAssignments: Array<assignmentsItem> = [];
        this.data.get('originalPartitions').forEach((item: number) => {
            const index = _.findIndex(newList, i => i.value === item);
            index !== -1 && originalAssignments.push({topic, partition: item, replicas: newList[index].replicas});
        });
        let params;
        if (resplitMode === 'auto') {
            params = {
                currentAssignments,
                proposedAssignments,
                interBrokerThrottle
            };
        }
        else {
            params = {
                currentAssignments: originalAssignments,
                proposedAssignments: assignments,
                interBrokerThrottle
            };
        }
        if (isBatch) {
            params = {
                ...params,
                batchSize
            };
        }
        await api.getParameters(clusterId, params).then((res) => {
            this.data.merge('formData', {
                minThrottle: res.minThrottle.toFixed(4),
                interBrokerThrottle: interBrokerThrottle > Math.ceil(res.minThrottle) ? interBrokerThrottle : Math.ceil(res.minThrottle),
                duration: res.duration,
            });
        });
    }

    @debounce(500)
    async getReSplitPlan() {
        const {formData, clusterId, resplitMode, topic} = this.data.get('');
        const {brokerIds, replicationFactor} = formData;
        if (resplitMode === 'auto') {
            if (brokerIds.length < replicationFactor) {
                return;
            }
            this.data.set('loading', true);
            await api.getReSplitPlan(clusterId, {
                topics: [topic],
                brokerIds,
                replicationFactor
            }).then((res) => {
                this.data.merge('formData', {
                    currentAssignments: res.currentAssignments,
                    proposedAssignments: res.proposedAssignments,
                    minThrottle: res.minThrottle.toFixed(4),
                    interBrokerThrottle: formData.interBrokerThrottle > Math.ceil(res.minThrottle) ? formData.interBrokerThrottle : Math.ceil(res.minThrottle),
                    duration: res.duration,
                    totalPartitionNum: res.totalPartitionNum,
                });
                this.data.set('loading', false);
                this.getParameters();
            });
        }
    }


    async getPartitionList() {
        const {clusterId, topic} = this.data.get('');
        const {result} = await api.getClusterTopicStatus(clusterId, topic, {pageNo: 1, pageSize: 9999});
        const arr = _.map(result, s => ({text: s.id.toString(), value: s.id, alias: s, replicas: s.replicas}));
        this.data.set('partitionList', arr);
    }

    // 确认
    async onConfirm() {
        try {
            await (this.ref('form') as unknown as Form).validateFields();
            const {formData, isBatch, isNow, isThrottle, topic, resplitMode} = this.data.get('');
            let res: number[] = [];
            formData.assignments.forEach((item: assignmentsItem, index: number) => {
                if (new Set(item.replicas).size !== item.replicas.length) {
                    res.push(index);
                }
            });
            if (res.length !== 0 && resplitMode === 'selfConfig') {
                return;
            }

            const newList = this.data.get('newList');
            let originalAssignments: Array<assignmentsItem> = [];
            this.data.get('originalPartitions').forEach((item: number) => {
                const index = _.findIndex(newList, i => i.value === item);
                index !== -1 && originalAssignments.push({topic, partition: item, replicas: newList[index].replicas});
            });

            let params = {
                topics: [topic],
                batchSize: isBatch ? formData.batchSize : null,
                interBrokerThrottle: isThrottle ? formData.interBrokerThrottle : null
            };
            if (!isNow) {
                params = {
                    ...params,
                    startTime: formData.startTime.getTime()
                };
            }
            if (resplitMode === 'selfConfig') {
                params = {
                    ...params,
                    partition: formData.partition,
                    proposedAssignments: formData.assignments,
                    currentAssignments: originalAssignments
                };
            } else {
                params = {
                    ...params,
                    replicationFactor: formData.replicationFactor,
                    brokerIds: formData.brokerIds,
                    proposedAssignments: formData.proposedAssignments,
                    currentAssignments: formData.currentAssignments
                };
            }
            await api.resplitClusterTopic(this.data.get('clusterId'), params);
            Notification.success('保存成功');
            this.fire('success', {});
            this.onClose();
        } catch (e) {}
    }

    onDeleteBroker(target: number) {
        this.nextTick(() => {
            this.data.remove('originalPartitions', target);
            this.data.merge('formData', {
                assignments: _.filter(this.data.get('formData.assignments'), item => item.partition !== target),
                partition: _.filter(this.data.get('formData.partition'), item => item !== target)
            });
            this.data.set('patitionTable.datasource', this.data.get('formData.assignments'));
            if (_.includes(this.data.get('errorList'), target)) {
                this.data.remove('errorList', target);
                this.data.set('errorMsg', this.data.get('errorList').join(','));
            }
        });
    }

    onBrokerChange(target: {value: string}, rowIndex: number, index: number, partition: number) {
        this.data.set(`formData.assignments[${rowIndex}].replicas[${index}]`, target.value);
        this.data.set(`patitionTable.datasource[${rowIndex}.replicas[${index}]`, target.value);
        const newKey = `patitionTable.datasource[${rowIndex}]`;

        const replicas = this.data.get(`formData.assignments[${rowIndex}].replicas`);

        if (new Set(replicas).size !== replicas.length) {
            const errorMsg = '副本冲突';
            const first = _.findIndex(replicas, i => i === target.value);
            const second = _.findLastIndex(replicas, i => i === target.value);
            this.data.set(`${newKey}.error.name[${first}]`, errorMsg);
            this.data.set(`${newKey}.error.name[${second}]`, errorMsg);
            this.data.set('showPartitionError', true);
            if (!_.includes(this.data.get('errorList'), partition)) {
                this.data.push('errorList', partition);
            }
            this.data.set('errorMsg', this.data.get('errorList').join(','));
        }
        else {
            this.data.remove('errorList', partition);
            if (this.data.get('errorList').length === 0) {
                this.data.set('showPartitionError', false);
            }
            this.data.set('errorMsg', this.data.get('errorList').join(','));
            this.nextTick(() => {
                this.data.set(`${newKey}.error.name`, null);
            });
        }
    }

    onTransferChange(target: {value: Array<{value: string}>}) {
        let brokerIds: number[] = [];
        target.value.forEach((item: {value: string}) => {
            brokerIds.push(parseInt(item.value));
        });
        this.data.set('formData.brokerIds', brokerIds);
        this.getReSplitPlan();
    }

    onPartitionChange(target: {value: number[]}) {
        const partitions = target.value;
        const {newList, topic} = this.data.get('');
        if (partitions.length !== 0) {
            partitions.forEach(item => {
                if (!_.includes(this.data.get('originalPartitions'), item)) {
                    let index = _.findIndex(newList, i => i.value === item);
                    let replicas = index !== -1 ? newList[index].replicas : [];
                    this.data.push('formData.assignments', {topic, partition: item, replicas});
                }
            });
            this.data.get('formData.assignments').forEach((item: {partition: number}, itemIndex: number) => {
                let index = _.findIndex(partitions, i => i === item.partition);
                if (index === -1) {
                    this.data.removeAt('formData.assignments', itemIndex);
                    this.data.remove('errorList', item.partition);
                    this.data.set('errorMsg', this.data.get('errorList').join(','));
                }
            });
        } else {
            this.data.set('formData.assignments', []);
        }

        this.data.set('patitionTable.datasource', this.data.get('formData.assignments'));
        this.data.set('originalPartitions', target.value);
    }

    onPartitionQuery(target: {value: string}) {
        const partitionList = this.data.get('partitionList');
        const newList = _.filter(partitionList, i => i.text.indexOf(target.value) > -1);
        this.data.set('newList', !target.value ? _.cloneDeep(partitionList) : _.cloneDeep(newList));
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
