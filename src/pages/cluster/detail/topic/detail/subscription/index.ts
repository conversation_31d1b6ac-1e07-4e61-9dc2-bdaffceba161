/**
 * 订阅关系
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Pagination, Table, Button} from '@baidu/sui';
import {AppListPage, Empty} from '@baidu/sui-biz';
import CommonTable from '@/components/common-table';
import {TABLE_SUI, PAGER_SUI} from '@/common/config';

import {pickEmpty, formatTime, formatEmpty} from '@/common/util';

import api from '@/common/client';

import './index.less';
import {SanComponent} from 'san/types';

const klass = 'bms-cluster-detail-topic-subscription';

type SubscriptionItem = {
    consumerId: string;
};

export class SubscriptionCell extends CommonTable {
    static template = html`
    <div class="subscription-cell">
        <s-table
            class="mt10"
            columns="{{table.columns}}"
            loading="{{table.loading}}"
            error="{{table.error}}"
            datasource="{{table.datasource}}"
        />
        <s-pagination
            slot="pager"
            s-if="{{!(!table.datasource.length && pager.page === 1)}}"
            class="pagination"
            layout="{{'pageSize, pager, go'}}"
            total="{{pager.count}}"
            pageSize="{{pager.pageSize}}"
            page="{{pager.page}}"
            pageSizes="{{pager.pageSizes}}"
            max-item="{{7}}"
            on-pagerChange="onPageChange"
            on-pagerSizeChange="onPageSizeChange"
        />
    </div>`;

    static components = {
        's-table': Table,
        's-pagination': Pagination
    };

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {name: 'partitionId', label: '分区ID'},
                    {
                        name: 'consumerId',
                        label: '消费者',
                        render: (item: {consumerId?: string}) => formatEmpty(item.consumerId)
                    },
                    {name: 'maxOffset', label: '最大位点'},
                    {name: 'committedOffset', label: '消费位点'},
                    {name: 'lag', label: '堆积量'},
                    {
                        name: 'clientId',
                        label: '客户端',
                        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands, max-len
                        render: (item: {clientId?: string, host?: string}) => formatEmpty(item.host) + '/' + formatEmpty(item.clientId)
                    },
                    {
                        name: 'lastConsumeTime',
                        label: '最后消费时间',
                        render: (item: {lastConsumeTime: string}) => formatTime(item.lastConsumeTime)
                    }
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    attached() {
        this.getComList();
    }

    async getTableList() {
        const {pager, clusterId, topic, consumerId} = this.data.get();
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize
        });
        const {totalCount, result} = await api.getClusterTopicConsumerGroupSubscribeStatus(clusterId, topic, consumerId, params);
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result);
    }
}

export default class Subscription extends CommonTable {
    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">订阅关系</h2>
            </div>
            <div class="mb15">
                <span class="topic__text_distance">
                    <span class="mr5">订阅消费组数量：</span>
                    <span>{{consumerGroupNum}}</span>
                </span>
                <span>
                    <span class="mr5">最后消费时间：</span>
                    <span>{{lastUpdateTime}}</span>
                </span>
            </div>
            <s-table
                class="btn-format-table"
                s-ref="table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
            >
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onDetail(row, rowIndex, $event)">
                            查看详情
                        </s-button>
                    </span>
                </div>
                <div slot="sub-detail">
                    <subscription-cell
                        clusterId="{{clusterId}}"
                        topic="{{topic}}"
                        consumerId="{{row.consumerId}}"
                        mode="topic"
                    />
                </div>
                <div slot="empty">
                    <s-empty vertical actionText="" />
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        's-empty': Empty,
        'subscription-cell': SubscriptionCell
    };

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'num',
                        label: '编号',
                        render: (item: SubscriptionItem, key: string, obj: Object, rowIndex: number) => {
                            const {pageSize, page} = this.data.get('pager')
                            return pageSize * (page - 1) + rowIndex + 1;
                        }
                    },
                    {name: 'consumerId', label: '消费组名称'},
                    {name: 'operation', label: '操作', width: 90}
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    attached() {
        this.getComList();
        this.getInfo();
    }

    async getTableList() {
        this.resetTableDetail();
        const {pager, clusterId, topic} = this.data.get();
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize
        });
        const {totalCount, result} = await api.listClusterTopicSubscribeStatus(clusterId, topic, params);
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', _.map(result, i => ({
            consumerId: i,
            subSlot: 'sub-detail'
        })));
    }

    async getInfo() {
        const {clusterId, topic} = this.data.get();
        api.getClusterTopicSubscribeStatusSummary(clusterId, topic, {})
            .then((target: {lastUpdateTime: string, groupNum: string}) => {
                this.data.set('consumerGroupNum', target.groupNum);
                this.data.set('lastUpdateTime', formatTime(target.lastUpdateTime));
            });
    }

    // 面板切换前对详情点击重置
    resetTableDetail() {
        const node = (this.ref('table') as unknown as SanComponent<{}>);
        const expandedIndex = node.data.get('expandedIndex');
        if (_.isArray(expandedIndex) && expandedIndex.length > 0) {
            node.data.set('expandedIndex', []);
        }
    }

    /**
     * 详情点击
     */
    onDetail(row: Object, rowIndex: number, e: Event) {
        const node = (this.ref('table') as unknown as SanComponent<{}>);
        const expandedIndex = node.data.get('expandedIndex');
        expandedIndex.indexOf(rowIndex) === -1 && this.resetTableDetail();
        this.toggleRow(rowIndex, e);
    }

    /**
     * 下拉表格toggle
     */
    toggleRow(rowIndex: number, e: Event | null) {
        // @ts-ignore
        (this.ref('table') as unknown as Table).toggleExpandRow(e, rowIndex);
    }
}
