/**
 * 主题详情
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {AppDetailPage, AppTabPage} from '@baidu/sui-biz';
import {Button, Select, Loading} from '@baidu/sui';
import DetailNav from '@/components/nav/detail';
import {ROUTE_PATH} from '@/common/config';
import api from '@/common/client';

import Info from './info';
import Quote from './quote';
import Authority from './authority';
import Subscription from './subscription';
import Message from '../../message';
import {ClusterDetail} from '../..';
import Storage from '../../storage-analyse';

const klass = 'bms-cluster-topic-detail';

enum Refs {
    INFO = 'info',
    QUOTE = 'quote',
    AUTHORITY = 'authority',
    SUBSCRIPTION = 'subscription',
    MESSAGE = 'message'
}

const Pages = [
    ROUTE_PATH.clusterDetailTopicDetailInfo,
    ROUTE_PATH.clusterDetailTopicDetailQuote,
    ROUTE_PATH.clusterDetailTopicDetailAuthority,
    ROUTE_PATH.clusterDetailTopicDetailSubscription,
    ROUTE_PATH.clusterDetailTopicDetailMessage,
    ROUTE_PATH.clusterDetailTopicDetailStorage
];

@decorators.asPage(...Pages)
export default class extends AppDetailPage {

    pageTitle = '主题详情';

    static template = html`
    <div class="${klass}">
        <s-detail-page class="bms-detail-page">
            <nav
                slot="pageTitle"
                title="{{readOnly ? topic + '(只读主题)' : topic}}"
                back="#${ROUTE_PATH.clusterDetailTopic}?name={{name}}&clusterId={{clusterId}}"
            >
            </nav>
            <div class="bms-detail-page__content">
                <app-tab-page
                    class="bms-tab-page"
                    skin="accordion"
                    position="left"
                    direction="vertical"
                    active="{= active =}"
                    on-change="onTabChange">
                    <app-tab-page-panel
                        style="{{active | show(1)}}"
                        label="主题详情"
                        url="#${ROUTE_PATH.clusterDetailTopicDetailInfo}?{{nextSuffix}}">
                        <info
                            s-if="{{active === 1}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            topic="{{topic}}"
                            readOnly="{{readOnly}}"
                            clusterInfo="{{clusterInfo}}"
                            s-ref="${Refs.INFO}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(2)}}"
                        label="分区信息"
                        url="#${ROUTE_PATH.clusterDetailTopicDetailQuote}?{{nextSuffix}}">
                        <quote
                            s-if="{{active === 2}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            topic="{{topic}}"
                            readOnly="{{readOnly}}"
                            deployType="{{route.query.deployType}}"
                            clusterInfo="{{clusterInfo}}"
                            s-ref="${Refs.QUOTE}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(3)}}"
                        s-if="{{aclEnabled}}"
                        label="权限设置"
                        url="#${ROUTE_PATH.clusterDetailTopicDetailAuthority}?{{nextSuffix}}">
                        <authority
                            s-if="{{active === 3}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            topic="{{topic}}"
                            readOnly="{{readOnly}}"
                            clusterInfo="{{clusterInfo}}"
                            s-ref="${Refs.AUTHORITY}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(4)}}"
                        label="订阅关系"
                        url="#${ROUTE_PATH.clusterDetailTopicDetailSubscription}?{{nextSuffix}}">
                        <subscription
                            s-if="{{active === 4}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            topic="{{topic}}"
                            readOnly="{{readOnly}}"
                            clusterInfo="{{clusterInfo}}"
                            s-ref="${Refs.SUBSCRIPTION}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(5)}}"
                        label="消息查询"
                        url="#${ROUTE_PATH.clusterDetailTopicDetailMessage}?{{nextSuffix}}">
                        <message
                            s-if="{{active === 5}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            topic="{{topic}}"
                            readOnly="{{readOnly}}"
                            topicDisabled="{{true}}"
                            clusterInfo="{{clusterInfo}}"
                            s-ref="${Refs.MESSAGE}"
                        />
                    </app-tab-page-panel>
                    <!--<app-tab-page-panel
                        style="{{active | show(6)}}"
                        label="存储分析"
                        url="#${ROUTE_PATH.clusterDetailTopicDetailStorage}?{{nextSuffix}}">
                        <storage
                            s-if="{{active === 6}}"
                            detail="{{clusterInfo}}"
                            topic="{{topic}}"
                            page="topic"
                            clusterId="{{clusterId}}"
                        />
                    </app-tab-page-panel>-->
                </app-tab-page>
            </div>
        </s-detail-page>
    </div>`;

    static components = {
        's-detail-page': AppDetailPage,
        'nav': DetailNav,
        's-button': Button,
        's-select': Select,
        's-option': Select.Option,
        's-loading': Loading,
        'quote': Quote,
        'info': Info,
        'authority': Authority,
        'message': Message,
        'subscription': Subscription,
        'app-tab-page': AppTabPage,
        'app-tab-page-panel': AppTabPage.TabPanel,
        'storage': Storage
    };

    initData() {
        return {
            active: 1,
            isLoading: false,
            aclEnabled: true
        };
    }

    static computed: SanComputedProps = {
        name(): string {
            return this.data.get('route.query.name');
        },
        clusterId(): string {
            return this.data.get('route.query.clusterId');
        },
        topic(): string {
            return this.data.get('route.query.topic');
        },
        readOnly(): boolean {
            return this.data.get('route.query.readOnly') === 'true';
        },
        nextSuffix() {
            const clusterId = this.data.get('clusterId');
            const topic = this.data.get('topic');
            const name = this.data.get('name');
            const aclEnabled = this.data.get('route.query.aclEnabled');
            const readOnly = this.data.get('route.query.readOnly');
            return `name=${name}&topic=${topic}&clusterId=${clusterId}&aclEnabled=${aclEnabled}&readOnly=${readOnly}`;
        }
    };

    static filters: SanFilterProps = {
        show(active: string, key: string) {
            return active === key ? '' : 'display:none';
        }
    };

    attached() {
        const aclEnabled: string = this.data.get('route.query.aclEnabled');
        this.data.set('aclEnabled', Number(aclEnabled) === 1);
        this.getDetail();
    }

    // 获取详情信息
    getDetail(): Promise<void> {
        return api.getClusterDetail(this.data.get('clusterId'), {})
            .then((clusterInfo: ClusterDetail) => {
                this.data.set('clusterInfo', clusterInfo);
            });
    }

    onRegionChange() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }

}
