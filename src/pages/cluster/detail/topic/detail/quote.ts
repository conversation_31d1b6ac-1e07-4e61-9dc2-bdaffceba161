/**
 * 分区状态
 *
 * @file quote.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Pagination, Table, Button, Notification} from '@baidu/sui';
import {AppListPage} from '@baidu/sui-biz';
import CommonTable from '@/components/common-table';
import {TABLE_SUI} from '@/common/config';

import {pickEmpty, formatTime, formatEmpty} from '@/common/util';
import TopicResplit from './topic-single-resplit';

import api from '@/common/client';

const klass = 'bms-cluster-detail-topic-quote';

type QuoteItem = {
    id: number;
    leaderId: number;
    minOffset: number;
    maxOffset: number;
    lastUpdateTime: string;
};

export default class Quote extends CommonTable {
    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">分区信息</h2>
            </div>
            <div class="mb15">
                <span class="topic__text_distance">
                    <span class="mr5">当前消息总量：</span>
                    <span>{{totalMessageNum}}</span>
                </span>
                <span>
                    <span class="mr5">最后写入时间：</span>
                    <span>{{lastUpdateTime}}</span>
                </span>
            </div>
            <s-button
                class="mt24 mb16"
                skin="primary"
                disabled="{{resplitDisabled}}"
                on-click="onReSplit">
                重新分区
            </s-button>
            <s-button
                class="ml8 mt24 mb16"
                disabled="{{clusterInfo.status !== 'ACTIVE'}}"
                loading="{{loading}}"
                on-click="onLeader">
                最优Leader选举
            </s-button>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
            />
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button
    };

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: false,
                columns: [
                    {
                        name: 'id',
                        label: '分区ID',
                        width: 80
                    },
                    {
                        name: 'leaderId',
                        label: 'Leader',
                        render: (item: QuoteItem) => formatEmpty(item.leaderId),
                        width: 80
                    },
                    {
                        name: 'replicas',
                        label: 'Replicas',
                        width: 80
                    },
                    {
                        name: 'inSyncReplicas',
                        label: 'In Sync Replicas',
                        width: 80
                    },
                    {
                        name: 'preferredLeader',
                        label: 'preferredLeader',
                        width: 80
                    },
                    {
                        name: 'underReplica',
                        label: 'underReplica',
                        width: 80
                    },
                    {
                        name: 'minOffset',
                        label: '最小位点',
                        width: 80
                    },
                    {
                        name: 'maxOffset',
                        label: '最大位点',
                        width: 80
                    },
                    {
                        name: 'lastUpdateTime',
                        label: '最后写入时间',
                        render: (item: QuoteItem) => formatTime(item.lastUpdateTime),
                        width: 80
                    }
                ]
            },
            pager: {
                pageSize: 10,
                page: 1,
                pageSizes: [10, 20]
            },
            brokerLoading: true
        };
    }

    static computed: SanComputedProps = {
        resplitDisabled() {
            const disableRessign = this.data.get('disableRessign');
            const status = this.data.get('clusterInfo.status');
            const notActive = status !== 'ACTIVE';
            const brokerLoading = this.data.get('brokerLoading');
            return disableRessign || notActive || brokerLoading;
        }
    };

    attached() {
        this.getComList();
        this.getInfo();
        this.getBrokerList();
    }

    async getTableList() {
        const {pager, clusterId, topic} = this.data.get('');
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize
        });
        this.data.set('table.loading', true);
        const {totalCount, result} = await api.getClusterTopicStatus(clusterId, topic, params);
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', result);
        this.data.set('table.loading', false);
    }

    getInfo() {
        const {clusterId, topic} = this.data.get('');
        api.getClusterTopicStatusSummary(clusterId, topic, {})
            .then((target: {lastUpdateTime: string, totalMessageNum: string}) => {
                this.data.set('totalMessageNum', target.totalMessageNum);
                this.data.set('lastUpdateTime', formatTime(target.lastUpdateTime));
            });
    }

    async getBrokerList() {
        await api.listClusterBrokers(this.data.get('clusterId'), {})
            .then((target: Array<{brokerId: string, instanceId: string}>) => {
                const arr = _.map(target, s => ({
                    label: s.brokerId.toString(),
                    value: parseInt(s.brokerId, 10),
                    show: true
                }));
                this.data.set('brokerList', arr);
                this.data.set('brokerLoading', false);
            });
    }

    async onReSplit(event: Event) {
        event && event?.stopPropagation();
        const {clusterId, topic, brokerList, deployType, clusterInfo} = this.data.get('');
        const dialog = new TopicResplit({
            data: {
                clusterId,
                topic,
                brokerList,
                deployType,
                zoneNum: clusterInfo.logicalZones.length
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.data.set('disableRessign', true);
        });
    }

    async onLeader() {
        this.data.set('table.loading', true);
        this.data.set('loading', true);
        const {clusterId, topic} = this.data.get('');
        try {
            await api.selectLeader(clusterId, topic);
            Notification.success('选举leader成功', {
                duration: 5
            });
        }
        finally {
            setTimeout(() => {
                this.data.set('loading', false);
            }, 10000);
            this.getTableList();
        }
    }
}
