/**
 * 权限设置
 *
 * @file list.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Input, Form, Select, Pagination, Table, Notification, Button, Checkbox} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import CreateBtn from '@/components/create-btn';
import PaginationTable from '@/components/common-table/pagination-table';

import EllipsisTip from '@/components/ellipsis-tip';
import {TABLE_SUI, PAGER_SUI, INPUT_WIDTH, DIALOG_INPUT_WIDTH} from '@/common/config';
import {pickEmpty} from '@/common/util';
import {VAILDITE_ITEMS} from '@/common/rules';
import {ClusterAuthorityPatternType, ClusterAuthorityOptionType} from '@/common/enums';
import {OperationType} from '@/common/enums/constant';
import {ClusterAuthorityItem} from '@/pages/cluster/detail/authority/list';
import Permission from '@/common/permission';

import api from '@/common/client';

import './index.less';

const klass = 'bms-cluster-detail-topic-authority';
const klassCreate = 'create-user-authority';

class CreateAuthority extends Component {
    static template = html`
    <div>
        <s-dialog
            title="添加用户权限"
            class="${klassCreate} ue-dialog"
            okText="提交"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <s-form
                s-ref="form"
                rules="{{rules}}"
                class="form-format"
                required="{{true}}"
                data="{= formData =}">
                <s-form-item label="用户名：" prop="username">
                    <s-select
                        width="${DIALOG_INPUT_WIDTH}"
                        value="{= formData.username =}"
                        datasource="{{userList}}"
                        getPopupContainer="{{getPopupContainer}}"
                        filterable
                    />
                </s-form-item>
                <s-form-item class="${klassCreate}__authority" label="权限：" prop="operation">
                    <s-checkbox-group value="{= formData.operation =}" on-change="onCheckboxChange">
                        <s-checkbox value="${OperationType.CONSUME}">订阅</s-checkbox>
                        <s-checkbox value="${OperationType.PRODUCE}">发布</s-checkbox>
                    </s-checkbox-group>
                </s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-input': Input,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup
    };

    initData() {
        return {
            rules: {
                username: [VAILDITE_ITEMS.requiredInput],
                operation: [VAILDITE_ITEMS.requiredSelect],
            },
            open: true,
            inputWidth: INPUT_WIDTH,
            getPopupContainer: () => document.body
        }
    }

    attached() {
        this.getUserList();
    }

    getUserList() {
        api.getClusterUserList(this.data.get('clusterId'), {})
            .then((target: Array<{username: string, createTime: string}>) => {
                this.data.set('userList',
                    _.map(target, i => ({text: i.username, value: i.username})));
            });
    }

    // checkbox改变
    onCheckboxChange() {
        this.nextTick(() => (this.ref('form') as unknown as Form).validateFields(['permission']));
    }

    // 确认
    async onConfirm() {
        try {
            await (this.ref('form') as unknown as Form).validateFields();
            this.data.set('confirming', true);
            const {clusterId, topic, formData} = this.data.get();
            await api.createClusterAuthority(clusterId, {
                ...formData,
                resourceType: 'TOPIC',
                patternType: 'LITERAL',
                resourceName: topic,
            });
            Notification.success('添加用户权限成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}

export default class Topic extends PaginationTable {

    searchKey = '';

    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">权限设置</h2>
            </div>
            <div slot="bulk">
                <create-btn
                    text="添加用户权限"
                    disabled="{{clusterInfo.status | disabledAuthorityCreate}}"
                    on-click="onCreate"
                />
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}">
                <div slot="c-operate">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{clusterInfo.status | disabledAuthorityDelete}}"
                            on-click="onDelete(row)">
                            删除
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty
                        vertical
                    >
                        <span slot="action">
                            <s-button
                                skin="stringfy"
                                on-click="onCreate"
                                disabled="{{clusterInfo.status | disabledAuthorityCreate}}">
                                马上添加>
                            </s-button>
                        </span>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-empty': Empty,
        's-pagination': Pagination,
        'ellipsis-tip': EllipsisTip,
        'create-btn': CreateBtn
    }

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'username',
                        label: '用户名称'
                    },
                    {
                        name: 'patternType',
                        label: '匹配模式',
                        render: (item: ClusterAuthorityItem) => ClusterAuthorityPatternType.getTextFromValue(item.patternType)
                    },
                    {
                        name: 'resourceName',
                        label: '匹配内容'
                    },
                    {
                        name: 'operation',
                        label: '权限',
                        render: (item: ClusterAuthorityItem) => ClusterAuthorityOptionType.getTextFromValue(item.operation)
                    },
                    {
                        name: 'operate',
                        label: '操作',
                        width: 60
                    }
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        disabledAuthorityCreate(status: string) {
            const readOnly = this.data.get('readOnly');
            return Permission.CLUSTER.TOPIC.disabledAuthorityCreate(status) || readOnly;
        },
        disabledAuthorityDelete(status: string) {
            const readOnly = this.data.get('readOnly');
            return Permission.CLUSTER.TOPIC.disabledAuthorityDelete(status) || readOnly;
        },
    };

    attached() {
        this.getComList();
    }

    async getNewTableList() {
        const {clusterId, topic} = this.data.get();
        const params = pickEmpty({
            resourceName: topic,
            resourceType: 'TOPIC',
            patternType: 'MATCH'
        });
        return await api.getClusterAuthorityList(clusterId, params);
    }

    // 添加用户权限
    onCreate() {
        const {clusterId, topic} = this.data.get();
        const dialog = new CreateAuthority({
            data: {
                mode: 'create',
                clusterId,
                topic
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.resetTable());
    }

    // 删除
    onDelete(row: ClusterAuthorityItem) {
        Dialog.warning({
            content: '您确定删除吗？',
            okText: '确定',
            onOk: async () => {
                const {clusterId} = this.data.get();
                await api.deleteClusterAuthority(clusterId, row, {});
                Notification.success('删除成功');
                this.resetTable();
            }
        });
    }
}
