/**
 * 主题集群详情
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Table, Button, Notification, Tooltip} from '@baidu/sui';
import m from 'moment';
import TileInfo from '@/components/tile-info';
import InstantEditor from '@/components/instant-editor';
import {TABLE_SUI, PAGER_SUI} from '@/common/config';
import api from '@/common/client';
import EllipsisTip from '@/components/ellipsis-tip';
import Permission from '@/common/permission';
import Tip from '@/components/tip';
import {UnitMap} from '../../conf';
import {TopicOptionsDialog} from '../../create-topic/topic-options';

import './index.less';

const klass = 'bms-cluster-topic-detail-info';
const klassConfig = 'config-title';

const TEXT_MAP = {
    topicName: '主题名称：',
    partitionNum: '分区数：',
    replicationFactor: '副本数：',
    brokersSkewed: 'Brokers Skewed %：',
    brokersLeaderSkewed: 'Brokers Leader Skewed %：',
    brokersSpread: 'Brokers Spread %：',
    preferredReplicas: 'Preferred Replicas %：',
    underReplicated: 'Under-replicated %：'
};

type TopicClusterInfo = {
    clusterId: string;
    topicName: string;
    partitionNum: string;
    brokersSkewed: string;
    brokersLeaderSkewed: string;
    brokersSpread: string;
    preferredReplicas: string;
    underReplicated: string;
    otherConfigs: Array<{key: string, value: string, unit: keyof typeof UnitMap}>;
};

const renderItem = (
    label: string | number,
    key: string,
    text: string | number = '',
    isShow: boolean = true) => ({label, key, text, isShow}
);

export default class extends Component {
    static template = html`
    <div class="${klass} bms-tab-info">
        <tile-info
            isLast="{{false}}"
            type="{{info.type}}"
            title="{{info.title}}"
            list="{{info.list}}"
        >
            <div slot="h-baseinfo-title" class="${klassConfig}">
                <s-append label="基本信息" class="${klassConfig}__label" noHighlight />
                <tip-cmpt slot="label" class="ml4 mt2" placement="right">
                    <p>属性说明：</p>
                    <p>* Brokers Skewed %：broker 倾斜率。该 topic 所占有的 broker 中，拥有超过该 topic 平均 partition 数的 broker 所占的百分比</p>
                    <p>* Brokers Leader Skewed %：  broker leader 倾斜率。该 topic leader 所占有的 broker 中，拥有超过该 topic 平均 leader partition 数的 broker 所占的百分比</p>
                    <p>* Brokers Spread %： 该主题的 partition对KAFKA 集群中 broker 的使用率</p>
                    <p>* Preferred Replicas %： 该主题的优先副本占所有副本的百分比</p>
                    <p>* Under-replicated %： 该主题中不同步的副本的百分比</p>
                </tip-cmpt>
            </div>
            <span slot="c-topicName-item" class="${klass}__topicName">
                <label>${TEXT_MAP.topicName}</label>
                <span>
                    <ellipsis-tip text="{{text}}" placement="top" />
                </span>
            </span>
            <span slot="c-partitionNum-text">
                {{text}}
                <instant-editor
                    s-if="{{text}}"
                    class="ml10"
                    type="number"
                    min="{{text}}"
                    max="{{500}}"
                    step="{{1}}"
                    stepStrictly
                    value="{{text}}"
                    request="{{request}}"
                    disabled="{{clusterInfo.status | disabledEditQuote}}"
                >
                    <s-button
                        disabled="{{clusterInfo.status | disabledEditQuote}}"
                        slot="edit"
                        class="editor-text"
                        skin="stringfy">
                        修改
                    </s-button>
                </instant-editor>
            </span>
        </tile-info>
        <tile-info type="config" isLast="{{true}}">
            <div slot="h-config-title" class="${klassConfig}">
                <s-append label="高级配置" class="${klassConfig}__label" noHighlight />
                <s-button
                    class="ml15"
                    width="{{46}}"
                    disabled="{{unEditAble | disabledEditHigherConf(clusterInfo.status)}}"
                    class="${klassConfig}__edit"
                    on-click="onEdit">
                    编辑
                </s-button>
            </div>
            <s-table
                slot="config-footer"
                loading="{{table.loading}}"
                columns="{{table.columns}}"
                datasource="{{table.datasource}}"
            >
                <div slot="c-value">
                    <div s-if="{{row.unit === 'ms'}}">
                        <s-tooltip>
                            <div slot="content">{{row.alias | tipText}}</div>
                            <span>{{row.value}}</span>
                        </s-tooltip>
                    </div>
                    <div s-elif="{{row.unit === 'bytes'}}">
                        <s-tooltip>
                            <div slot="content">{{row.alias | byteText}}</div>
                            <span>{{row.value}}</span>
                        </s-tooltip>
                    </div>
                    <span s-else>{{row.value}}</span>
                </div>
            </s-table>
        </tile-info>
    </div>`;

    static components = {
        's-append': AppLegend,
        'tile-info': TileInfo,
        'instant-editor': InstantEditor,
        'ellipsis-tip': EllipsisTip,
        's-table': Table,
        's-button': Button,
        's-tooltip': Tooltip,
        'tip-cmpt': Tip
    };

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                datasource: [],
                columns: [
                    {label: '配置项', name: 'key'},
                    {label: '内容', name: 'value'}
                ]
            },
            pager: {...PAGER_SUI},
            detail: {},
            infoLoading: true,
            unEditAble: true,
            request: this.request.bind(this)
        };
    }

    static computed: SanComputedProps = {
        info() {
            const detail = this.data.get('detail');
            const {
                topicName,
                partitionNum,
                replicationFactor,
                brokersSkewed,
                brokersLeaderSkewed,
                brokersSpread,
                preferredReplicas,
                underReplicated
            } = detail;

            const BaseInfo = {
                title: '基本信息',
                type: 'baseinfo',
                list: [
                    renderItem(TEXT_MAP.topicName, 'topicName', topicName),
                    renderItem(TEXT_MAP.partitionNum, 'partitionNum', partitionNum),
                    renderItem(TEXT_MAP.replicationFactor, 'replicationFactor', replicationFactor),
                    renderItem(TEXT_MAP.brokersSkewed, 'brokersSkewed', brokersSkewed),
                    renderItem(TEXT_MAP.brokersLeaderSkewed, 'brokersLeaderSkewed', brokersLeaderSkewed),
                    renderItem(TEXT_MAP.brokersSpread, 'brokersSpread', brokersSpread),
                    renderItem(TEXT_MAP.preferredReplicas, 'preferredReplicas', preferredReplicas),
                    renderItem(TEXT_MAP.underReplicated, 'underReplicated', underReplicated),
                ]
            };

            return BaseInfo;
        }
    };

    static filters = {
        disabledEditQuote(status: string) {
            return Permission.CLUSTER.TOPIC.disabledEditQuote(status) || this.data.get('readOnly');
        },
        disabledEditHigherConf(unEditAble: boolean, status: string) {
            return unEditAble
                || Permission.CLUSTER.TOPIC.disabledEditHigherConf(status);
        },
        tipText(value: number) {
            const tempTime = m.duration(value);
            const timeObj = {
                days: tempTime.days(),
                hrs: tempTime.hours(),
                mins: tempTime.minutes(),
                secs: tempTime.seconds(),
                ms: tempTime.milliseconds()
            };

            const timeArr = [];

            for (let k in timeObj) {
                if (Number(timeObj[k]) > 0) {
                    timeArr.push(`${timeObj[k]} ${k}`);
                }
            }

            return timeArr.join(' ');
        },
        byteText(value: number) {
            let res = [];
            if (value < 1024) {
                res.push(value + ' bytes');
            }
            else if (value >= 1024 && value < 1024 * 1024) {
                res.push(Math.floor(value / 1024) + ' KB');
                const bytes = value % 1024;
                bytes && res.push(bytes + ' bytes');
            }
            else {
                res.push(Math.floor(value / (1024 * 1024)) + ' MB');
                const KBS = Math.floor(value % (1024 * 1024) / 1024);
                const bytes = Math.floor(value % (1024 * 1024) % 1024);
                KBS && res.push(KBS + ' KB');
                bytes && res.push(bytes + ' bytes');
            }
            return res.join(' ');
        }
    };

    attached() {
        this.getInfo();
    }

    getInfo() {
        const {topic, clusterId} = this.data.get('');
        this.data.set('table.loading', true);
        this.data.set('unEditAble', true);
        api.getClusterTopicDetail(clusterId, topic, {})
            .then((detail: TopicClusterInfo) => {
                this.data.set('detail', detail);
                this.data.set('table.datasource',
                    _.map(detail.otherConfigs, i => ({
                        ...i,
                        value: i.unit ? `${i.value} ${i.unit}` : i.value,
                        alias: i.value
                    })));
            }).finally(() => {
                this.data.set('table.loading', false);
                this.data.set('unEditAble', false);
            });
    }

    // 编辑
    onEdit() {
        const {clusterId, topic, detail} = this.data.get();
        const {otherConfigs, replicationFactor} = detail;
        const dialog = new TopicOptionsDialog({
            data: {
                list: _.map(otherConfigs, i => ({
                    value: i.value,
                    unit: i.unit,
                    name: i.key
                })),
                replicationFactorMax: replicationFactor,
                clusterId,
                topic
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.getInfo());
    }

    // 修改分区数
    request(partitionNum: string) {
        const {clusterId, topic} = this.data.get('');
        return api.updateClusterTopic(clusterId, topic, {partitionNum})
            .then(async () => {
                Notification.success('修改分区数成功');
                this.getInfo();
            });
    }
}
