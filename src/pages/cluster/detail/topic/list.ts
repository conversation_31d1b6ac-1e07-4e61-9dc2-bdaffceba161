/**
 * 主题
 *
 * @file list.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html, redirect} from '@baiducloud/runtime';
import {Dialog, Pagination, Table, Notification, Button} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import CreateBtn from '@/components/create-btn';
import {OutlinedRefresh} from '@baidu/sui-icon';
import PaginationTable from '@/components/common-table/pagination-table';
import TopicResplit from './topic-resplit';
import EllipsisTip from '@/components/ellipsis-tip';
import {TABLE_SUI, PAGER_SUI, ROUTE_PATH} from '@/common/config';
import {formatTime} from '@/common/util';
import Permission from '@/common/permission';
import MessageSend from './message-dialog';
import api from '@/common/client';

import CreateTopic from './create-topic';

import './common.less';

const klass = 'bms-cluster-detail-topic';

type ClusterTopicItem = {
    topicName: string;
    partitionNum: number;
    replicaNum: number;
    brokerNum: number;
    retentionHour: number;
    createTime: string;
    readOnly: boolean;
};

export default class Topic extends PaginationTable implements DetailRefresh {

    searchKey = 'topicName';

    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">主题管理</h2>
            </div>
            <div slot="bulk">
                <create-btn
                    text="创建主题"
                    on-click="onCreate"
                    disabled="{{detail.status | createDisabled}}"
                />
                <s-button
                    class="ml8"
                    disabled="{{resplitDisabled}}"
                    on-click="onReSplit">
                    重新分区
                </s-button>
            </div>
            <div slot="filter">
                <s-searchbox
                    class="searchbox"
                    placeholder="请输入主题名称进行搜索"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="{{170}}"
                >
                    <div slot="prefix"></div>
                </s-searchbox>
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}">
                <div slot="c-topicName">
                    <a href="#${ROUTE_PATH.clusterDetailTopicDetailInfo}?name={{name}}&deployType={{detail.deployType}}&clusterId={{clusterId}}&readOnly={{row.readOnly}}&topic={{row.topicName}}&aclEnabled={{detail.aclEnabled ? 1 : 0}}"
                        class="a-btn">
                        {{row.readOnly ? row.topicName + '（只读主题）' : row.topicName }}
                    </a>
                </div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onGetMonitor(row)">
                            查看监控
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{detail.status | disabledDelete(row)}}"
                            on-click="onMessageSend(row)">
                            消息发送
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{detail.status | disabledDelete(row)}}"
                            on-click="onDelete(row)">
                            删除
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty
                        vertical
                    >
                        <span slot="action">
                            <s-button
                                skin="stringfy"
                                on-click="onCreate"
                                disabled="{{detail.status | createDisabled}}">
                                马上创建>
                            </s-button>
                        </span>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'ellipsis-tip': EllipsisTip,
        'create-btn': CreateBtn,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty
    };

    initData() {
        return {
            searchbox: {
                keyword: ''
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'topicName',
                        label: '主题名称',
                    },
                    {
                        name: 'partitionNum',
                        label: '分区数',
                        width: 150
                    },
                    {
                        name: 'replicaNum',
                        label: '副本数',
                        width: 150
                    },
                    {
                        name: 'createTime',
                        label: '创建时间',
                        render: (item: ClusterTopicItem) => formatTime(item.createTime),
                    },
                    {
                        name: 'operation',
                        label: '操作',
                        width: 280
                    }
                ]
            },
            pager: {...PAGER_SUI},
            detail: {}
        };
    }

    static filters = {
        createDisabled(state: string) {
            return !Permission.CLUSTER.TOPIC.canCreate(state);
        },
        disabledDelete(status: string, row: ClusterTopicItem) {
            return Permission.CLUSTER.TOPIC.disabledDelete(status) || row.readOnly;
        }
    };

    static computed: SanComputedProps = {
        resplitDisabled() {
            const detail = this.data.get('detail');
            const isCreateProcess = this.data.get('isOnCreate');
            const hasNoData = this.data.get('table.datasource').length === 0;
            const notActive = detail.status !== 'ACTIVE';
            return isCreateProcess || hasNoData || notActive;
        }
    };

    attached() {
        this.getComList();
        this.getBrokerList();
    }

    async getNewTableList() {
        return await api.listClusterTopic(this.data.get('clusterId'), {});
    }

    // 新建主题
    onCreate() {
        const {clusterId, detail} = this.data.get('');
        const dialog = new CreateTopic({data: {
            clusterId,
            deployType: detail.deployType,
            zoneNum: detail.logicalZones.length,
            numberOfBrokerNodes: detail.numberOfBrokerNodes
        }});
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.resetTable();
            this.getBrokerList();
        });
    }

    // 删除
    onDelete(row: ClusterTopicItem) {
        Dialog.warning({
            content: '您确定删除此主题吗？',
            okText: '确定',
            onOk: async () => {
                await api.deleteClusterTopic(this.data.get('clusterId'), row.topicName, {});
                Notification.success('删除成功');
                this.resetTable();
            }
        });
    }

    // 查看监控信息
    onGetMonitor(row: ClusterTopicItem) {
        const clusterId = this.data.get('clusterId');
        const name = this.data.get('name');
        redirect(`#${ROUTE_PATH.clusterDetailMonitor}?name=${name}&clusterId=${clusterId}&topic=${row.topicName}`);
    }

    onMessageSend(row: ClusterTopicItem) {
        const {clusterId, name} = this.data.get('');
        const dialog = new MessageSend({data: {
            clusterId,
            topicName: row.topicName,
            clusterName: name,
        }});
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    async getBrokerList() {
        this.data.set('isOnCreate', true);
        await api.listClusterBrokers(this.data.get('clusterId'), {})
            .then((target: Array<{brokerId: string, instanceId: string}>) => {
                const arr = _.map(target, s => ({label: s.brokerId, value: parseInt(s.brokerId), show: true}));
                this.data.set('brokerList', arr);
                this.data.set('isOnCreate', false);
            });
    }

    onReSplit(event: Event) {
        event.stopPropagation();
        const {clusterId, brokerList} = this.data.get('');
        const dialog = new TopicResplit({data: {clusterId, brokerList}});
        dialog.attach(document.body);
        dialog.on('success', () => {
            this.refreshInfo();
        });
    }

    // refresh更新
    refreshInfo() {
        return this.getComList();
    }
}
