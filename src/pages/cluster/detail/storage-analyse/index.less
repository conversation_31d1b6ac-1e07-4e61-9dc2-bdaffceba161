@klass: bms-cluster-storage-analyse;

.@{klass} {
    width: 100%;
    height: 100%;
    padding: 16px;
    overflow-x: hidden;

    .s-list-content {
        background: #FFFFFF;
    }

    h2 {
        border-bottom: 0;
        margin-left: -16px;
    }

    .table-full-wrap {
        margin: 0;
        padding: 0;
    }

    &_overview_list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    &_broker_list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .health  {
        color: #30BF13 !important;
    }
    .normal {
        color: #2468F2 !important;
    }
    .warn {
        color: #FF9326 !important;
    }
    .danger {
        color: #F33E3E !important;
    }
}
