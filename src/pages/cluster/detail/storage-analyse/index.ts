/**
 * 存储分析
 *
 * @file storage-analyse.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Tooltip, Loading} from '@baidu/sui';
import {AppListPage} from '@baidu/sui-biz';
import {Component} from 'san';
import {OutlinedQuestionCircle} from '@baidu/sui-icon';
import Overview from './components/overview/index';
import BrokerView from './components/broker-view';
import TopicBrokerView from './components/topic-broker-view';
import api from '@/common/client';
import {formatBytes, byte2GB} from '@/common/util';
import './index.less';

const klass = 'bms-cluster-storage-analyse';

interface clusterDetailUsage {
    clusterId: string;
    totalBytes: number;
    usedBytes: number;
    freeBytes: number;
    usedPercent: number;
    disks: object[];
    nodeServices: object[];
}

export default class Storage extends Component {


    static template = html`
    <div class="${klass}">
        <s-loading
            loading="{{loading}}"
            style="width: 100%;height: 100%"
        >
            <app-list-page class="${klass}_content">
                <div slot="pageTitle" class="page-title">
                    <h2 class="title">
                        存储分析
                        <s-tooltip placement="right" style="margin-left: -16px">
                            <s-question color="#84868C" width="16px"/>
                            <div slot="content">
                                <p>存储分析能够帮助用户快速了解磁盘存储情况，快速定位异常</p>
                                <p class="status normal">磁盘使用率 = [0%, 30%)</p>
                                <p class="status process">磁盘使用率 = [30%, 60%)</p>
                                <p class="status warning">磁盘使用率 = [60%, 80%)</p>
                                <p class="status error">磁盘使用率 = [80%, 100%)</p>
                            </div>
                        </s-tooltip>
                    </h2>
                </div>
                <div class="${klass}_overview_list">
                    <overview
                        s-for="item in overviewList"
                        page="{{page}}"
                        detail="{{item}}"
                    />
                </div>
                <div class="${klass}_broker_list" s-if="page === 'cluster'">
                    <broker-view
                        s-for="item in brokerList"
                        broker="{{item}}"
                        diskList="{{diskList}}"
                        clusterId="{{detail.clusterId}}"
                        isController="{{controller === item.brokerId}}"
                        numberOfDisk="{{detail.storageMeta.numberOfDisk}}"
                    />
                </div>
                <div class="${klass}_broker_list" s-else>
                    <topic-broker-view
                        s-for="item in brokerList"
                        broker="{{item}}"
                        topic="{{topic}}"
                        isController="{{controller === item.brokerId}}"
                        clusterId="{{detail.clusterId}}"
                        numberOfDisk="{{detail.storageMeta.numberOfDisk}}"
                    />
                </div>
            </app-list-page>
        </s-loading>
    </div>`;


    static components = {
        'app-list-page': AppListPage,
        's-question': OutlinedQuestionCircle,
        's-tooltip': Tooltip,
        'overview': Overview,
        'broker-view': BrokerView,
        'topic-broker-view': TopicBrokerView,
        's-loading': Loading
    };

    initData() {
        return {
            overviewList: [],
            brokerList: [],
            diskList: [],
            controller: '',
            loading: true,
            page: 'cluster'
        };
    }

    async attached() {
        const {page} = this.data.get('');
        try {
            await this.getClusterDiskUsage();
            page === 'cluster' && await this.getDiskList();
            await this.getController();
            await this.getBrokerList();
            this.data.set('loading', false);
        }
        catch (e) {
            this.data.set('loading', false);
        }
    }

    async getClusterDiskUsage() {
        const {page, clusterId, topic} = this.data.get('');
        const target = page === 'cluster'
            ? await api.getClusterDiskUsage(clusterId, {})
            : await api.getTopicDiskUsage(clusterId, topic, {});
        const fullDiskNum = _.filter(target.disks, disk => disk.usedPercent === 100).length;
        const danger = _.filter(target.nodeServices, node => node.grade === 'DANGER').length || 0;
        const warn = _.filter(target.nodeServices, node => node.grade === 'WARNING').length || 0;
        let diagnosis = [];
        target.nodeServices.forEach(node => {
            if (_.includes(['DANGER', 'WARNING'], node.grade)) {
                diagnosis.push('节点' + node.brokerId + ' : ' + node.diagnosis);
            }
        });
        const list = [
            {
                title: page === 'cluster' ? '数据磁盘使用率' : '磁盘使用占比',
                percent: page === 'cluster' ? target.usedPercent : target.topicUsedPercent.toFixed(2),
                unit: '%',
                usedBytes: formatBytes(page === 'cluster' ? target.usedBytes : target.topicUsedBytes, 2),
            },
            {
                title: '节点服务信息',
                redcontent: danger > 0 ? danger + '个异常' : warn > 0 ? warn + '个告警' : 0 + '个异常',
                content: '总节点数 ' + target.nodeServices.length + '个',
                diagnosis: '异常情况说明：<br/>' + diagnosis.join('<br/>'),
                danger,
                warn
            },
            {
                title: '数据磁盘状态',
                redcontent: fullDiskNum + '块已满',
                content: '总磁盘数 ' + target.disks.length + '个',
                danger: fullDiskNum
            },
            {
                title: '数据磁盘剩余量',
                percent: byte2GB(page === 'cluster' ? target.freeBytes : target.topicFreeBytes),
                unit: 'GB',
                content: '总容量 ' + byte2GB(page === 'cluster' ? target.totalBytes : target.topicTotalBytes) + 'GB'
            }
        ];
        this.data.set('overviewList', list);
    }

    async getBrokerList() {
        const {page, clusterId, topic} = this.data.get('');
        const target = page === 'cluster'
            ? await api.getBrokerDetail(clusterId, {})
            : await api.getTopicBrokerDiskUsage(clusterId, topic, {});
        this.data.set('brokerList', target.map(item => ({
            ...item,
            totalBytes: byte2GB(item.totalBytes),
            freeBytes: byte2GB(item.freeBytes),
        })));
    }

    // 获取控制节点
    async getController() {
        await api.getControllerBroker(this.data.get('clusterId'), {})
            .then((result: {timestamp: string, brokerId: number}) => {
                this.data.set('controller', result.brokerId);
            });
    }

    async getDiskList() {
        const {clusterId} = this.data.get('');
        const diskList = await api.listDataDisks(clusterId, {});
        this.data.set('diskList', _.map(diskList, (disk: {diskName: string, diskId: string}) => ({
            text: disk.diskName,
            value: disk.diskId
        })));
    }

    refreshInfo() {}
}
