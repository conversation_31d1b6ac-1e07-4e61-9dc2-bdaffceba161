/**
 * 存储分析-概览组件
 *
 * @file overview.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Tooltip, Progress} from '@baidu/sui';
import {Component} from 'san';
import {OutlinedHome} from '@baidu/sui-icon';
import {percents, getSkin} from '../../util';
import './index.less';

const klass = 'bms-cluster-storage-overview';

export default class Overview extends Component {


    static template = html`
    <div class="${klass}">
        <template s-if="{{page === 'cluster' || detail.title !== '磁盘使用占比' }}">
            <div class="title">
            <s-home color="#84868C" width="16px" style="margin-top: -1px"/>
                {{detail.title}}
            </div>
            <div class="content">
                <span s-if="detail.percent" class="percent">{{detail.percent}}</span>
                <span>{{detail.unit}}</span>
                <s-tooltip s-if="detail.title === '节点服务信息' && (detail.danger > 0 || detail.warn > 0)" placement="right">
                    <span class="red-content {{colorComputed}}">{{detail.redcontent}}</span>
                    <div slot="content">
                        {{detail.diagnosis | raw}}
                    </div>
                </s-tooltip>
                <span s-elif="detail.redcontent" class="red-content {{colorComputed}}">{{detail.redcontent}}</span>
                <span style="margin-left: 16px">{{detail.content}}</span>
            </div>
            <div s-if="{{detail.title === '数据磁盘使用率'}}" class="progress">
                <s-progress
                    width="52"
                    height="52"
                    percent="{{detail.percent}}"
                    type="circle"
                    skin="{{detail.percent | getSkin}}"
                >
                    <div slot="info"></div>
                </s-progress>
            </div>
        </template>
        <div s-else style="display:flex; justify-content: space-around">
            <div>
                <span class="title">主题使用量</span>
                <p class="mt16">
                    <span class="percent">{{detail.usedBytes}}</span>
                </p>
            </div>
            <div>
                <span class="title">{{detail.title}}</span>
                <p class="mt16">
                    <span class="percent">{{detail.percent}}</span>
                    <span>{{detail.unit}}</span>
                </p>
            </div>
        </div>
    </div>`;

    static components = {
        's-tooltip': Tooltip,
        's-home': OutlinedHome,
        's-progress': Progress
    };

    initData() {
        return {
            percents
        };
    }

    static filters = {
        getSkin
    };

    static computed = {
        colorComputed() {
            const danger = this.data.get('detail.danger');
            const warn = this.data.get('detail.warn');
            if (danger > 0) {
                return 'danger';
            }
            else if (warn > 0) {
                return 'warn';
            }
            else {
                return 'health';
            }
        }
    };
}
