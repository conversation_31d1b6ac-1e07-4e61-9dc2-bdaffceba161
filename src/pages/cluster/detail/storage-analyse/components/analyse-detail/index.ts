/**
 * 节点分析详情弹窗
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Form, Select, Radio, Button, InputNumber, Table, Loading} from '@baidu/sui';
import * as echarts from 'echarts/core';
import {PieChart} from 'echarts/charts';
import {CanvasRenderer} from 'echarts/renderers';
import {
    VisualMapComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
} from 'echarts/components';
import {option} from './util';
import {formatBytes} from '@/common/util';
import api from '@/common/client';
import './index.less';

const klass = 'analyse-detail';

echarts.use([
    PieChart,
    VisualMapComponent,
    CanvasRenderer,
    LegendComponent,
    GridComponent,
    TooltipComponent,
]);
export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="节点分析详情"
            open="{{true}}"
            maskClose="{{true}}"
            otherClose="{{true}}"
            on-close="onClose"
            getContainer="{{getContainer}}"
            size="700"
        >
            <s-form label-align="left" class="form-item-center ${klass}">
                <s-form-item prop="brokerId" label="节点ID:">
                    {{brokerId}}
                </s-form-item>
                <s-form-item s-if="numberOfDisk > 1" prop="disk" label="磁盘名称:">
                    <s-select
                        value="{=disk=}"
                        datasource="{{diskList}}"
                        on-change="handleDiskChange"
                    >
                    </s-select>
                </s-form-item>
                <s-form-item class="inline-item" prop="method" label="统计方式:">
                    <s-radio-group
                        on-change="handleMethodChange"
                        value="{=method=}"
                        radioType="button"
                        name="method"
                        datasource="{{methodList}}"
                    >
                    </s-radio-group>
                </s-form-item>
                <s-form-item class="inline-item" prop="value" label="{{methodLabel}}">
                    <s-input-number
                        on-change="handleValueChange"
                        value="{=value=}"
                        step="1"
                        stepStrictly
                        min="{{0}}"
                        max="{{100}}"
                        name="value"
                    >
                    </s-input-number>
                    <span>{{cell}}</span>
                </s-form-item>
            </s-form>
            <!--<s-button skin="primary" on-click="analyse">分析</s-button>-->
            <s-loading loading="{{loading}}">
                <div
                    id="chart-content"
                    style="width: 600px; height: 500px;border: 1px solid #E8E9EB; border-radius: 6px;"
                ></div>
                <s-table
                    columns="{{columns}}"
                    datasource="{{datasource}}"
                    width="{{600}}"
                    class="table-content"
                >
                    <div slot="c-usedPercent">{{row.usedPercent + '%'}}</div>
                </s-table>
            </s-loading>
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-select': Select,
        's-option': Select.Option,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-button': Button,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        's-table': Table,
        's-loading': Loading
    };

    initData() {
        return {
            open: true,
            radioGroup: {
                batchSource: [
                    {label: '指定批次大小', value: true},
                    {label: '否', value: false}
                ],
                throttleSource: [
                    {label: '指定限制流量', value: true},
                    {label: '否', value: false}
                ],
                timeSource: [
                    {label: '自定义时间', value: false},
                    {label: '现在执行', value: true}
                ],
            },
            method: 'byTop',
            value: 10,
            methodList: [
                {label: '使用Top', value: 'byTop'},
                {label: '使用大小', value: 'bySize'},
                {label: '使用占比', value: 'byPercent'}
            ],
            diskList: [],
            columns: [
                {name: 'topicName', label: '主题', width: 150},
                {name: 'partitionId', label: '分区', width: 150},
                {name: 'usedBytes', label: '使用量', width: 150},
                {name: 'usedPercent', label: '磁盘容量占比', width: 150},
            ],
            getContainer: document.getElementById('main'),
            option: option,
            loading: true
        };
    }

    static computed: SanComputedProps = {
        methodLabel() {
            const method = this.data.get('method');
            switch (method) {
                case 'byTop':
                    return '使用量排名前';
                case 'bySize':
                    return '使用量大小达';
                case 'byPercent':
                    return '使用量占比达';
            }
        },
        cell() {
            const method = this.data.get('method');
            switch (method) {
                case 'byTop':
                    return '个';
                case 'bySize':
                    return 'GB';
                case 'byPercent':
                    return '%';
            }
        },
    };

    async attached() {
        await this.getDiskList();
        await this.analyse();
    }

    async getDiskList() {
        const {clusterId, page, topic, brokerId} = this.data.get('');
        const diskList = page === 'cluster'
            ? await api.listDataDisks(clusterId, {})
            : await api.listTopicDisks(clusterId, topic, brokerId, {});
        this.data.set('diskList', _.map(diskList, (disk: {diskName: string, diskId: string}) => ({
            text: disk.diskName,
            value: disk.diskId
        })));
        this.data.set('disk', diskList[0].diskId);
    }

    async handleDiskChange(target: {value: string}) {
        this.data.set('disk', target.value);
        await this.analyse();
    }

    async handleMethodChange(target: {value: string}) {
        let value = 0;
        switch (target.value) {
            case 'byTop':
                value = 10;
                break;
            case 'bySize':
                value = 5;
                break;
            case 'byPercent':
                value = 20;
                break;
        }
        this.data.set('method', target.value);
        this.data.set('value', value);
        await this.analyse();
    }

    async handleValueChange(target: {value: string}) {
        this.data.set('value', target.value);
        await this.analyse();
    }

    async analyse() {
        const {clusterId, brokerId, disk, method, value, topic, page} = this.data.get('');
        const param = {};
        switch (method) {
            case 'byTop':
                param.top = value;
                break;
            case 'bySize':
                param.minSize = value;
                break;
            case 'byPercent':
                param.percent = value;
                break;
        }
        this.data.set('loading', true);
        const result = page === 'cluster'
            ? await api.getDiskStorageDetail(clusterId, brokerId, disk, param)
            : await api.getTopicBrokerDiskUsageDetail(clusterId, topic, brokerId, disk, param);
        const {topics, analysisUsedBytes, otherUsedBytes, freeBytes, totalBytes, partitions} = result;
        let data = [];
        if (page === 'cluster') {
            this.data.set('datasource', topics.map((topic) => ({
                ...topic,
                usedBytes: formatBytes(topic.usedBytes, 2)
            })));
            data = topics.map(item => ({
                value: item.usedPercent,
                name: item.topicName + '_' + item.partitionId
            }));
        }
        else {
            this.data.set('datasource', partitions.map((partition) => ({
                ...partition,
                usedBytes: formatBytes(partition.usedBytes, 2)
            })));
            data = partitions.map(item => ({
                value: item.usedPercent,
                name: item.topicName + '_' + item.partitionId
            }));
        }
        let color = [];
        const percent = analysisUsedBytes / totalBytes;
        if (percent < 0.3) {
            color = [
                '#30BF13',
                '#A5E693',
                '#E8E9EB'
            ];
        }
        else if (percent < 0.6) {
            color = [
                '#2468F2',
                '#A8CAFF',
                '#E8E9EB'
            ];
        }
        else if (percent < 0.8) {
            color = [
                '#FF9326',
                '#FFD8A8',
                '#E8E9EB'
            ];
        }
        else {
            color = [
                '#F33E3E',
                '#FFB6B3',
                '#E8E9EB'
            ];
        }
        this.data.set('option.series[0].data[0].value', analysisUsedBytes);
        this.data.set('option.series[0].data[1].value', otherUsedBytes);
        this.data.set('option.series[0].data[2].value', freeBytes);
        this.data.set('option.series[1].data', data);
        this.data.merge('option.series[1].itemStyle', {color: color[0]});
        this.data.merge('option', {color});
        const option = this.data.get('option');
        const myChart = echarts.init(document.getElementById('chart-content'));
        option && myChart.setOption(option);
        this.data.set('loading', false);
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
