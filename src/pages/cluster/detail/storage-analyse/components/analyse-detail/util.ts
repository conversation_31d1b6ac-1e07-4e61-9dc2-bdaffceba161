import {formatBytes} from '@/common/util';
export const option = {
    color: [
        '#30BF13',
        '#A5E693',
        '#E8E9EB'
    ],
    tooltip: {},
    legend: {
        data: [
            '分析已用',
            '其他已用',
            '剩余可用'
        ],
        icon: 'circle',
        y: 'bottom',
        itemGap: 36
    },
    series: [
        {
            name: 'Analyse From',
            type: 'pie',
            selectedMode: 'single',
            radius: [0, '30%'],
            label: {
                position: 'inner',
                fontSize: 0
            },
            labelLine: {
                show: false
            },
            data: [
                {value: 0, name: '分析已用', selected: true},
                {value: 0, name: '其他已用'},
                {value: 0, name: '剩余可用'}
            ],
            tooltip: {
                trigger: 'item',
                formatter: (params) => {
                    let value = formatBytes(params.value);
                    let htmls = `${params.name}量: ${value}
                    <br/> ${params.name}率: ${params.percent}%`;
                    return htmls;
                }
            }
        },
        {
            name: 'Analyse From',
            type: 'pie',
            radius: ['45%', '60%'],
            labelLine: {
                length: 30
            },
            label: {
                formatter: '{b|{b}}',
                rich: {
                    b: {
                        fontSize: '9.6px',
                        color: '#1A2530',
                        fontWeight: 400,
                        lineHeight: 22,
                        align: 'center'
                    }
                }
            },
            data: [],
            tooltip: {
                trigger: 'item',
                formatter: '{b}'
            }
        }
    ]
};
