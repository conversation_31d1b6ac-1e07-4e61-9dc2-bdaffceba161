@klass: bms-cluster-storage-broker-view;

.@{klass} {
    width: calc(~"50% - 8px");
    height: 392px;
    padding: 24px;
    background: #FFFFFF;
    border: 1px solid #E8E9EB;
    border-radius: 6px;
    margin-left: 16px;
    margin-top: 16px;

    &:nth-child(odd) {
        margin-left: 0;
    }

    .title {
        font-size: 16px;
        color: #151B26;
        line-height: 24px;
        font-weight: 500;
    }

    .controller {
        width: 70px;
        height: 20px;
        border: 1px solid #30BF13;
        border-radius: 2px;
        font-size: 12px;
        color: #30BF13;
        line-height: 20px;
        font-weight: 400;
        margin-left: 12px;
        padding: 0 8.5px;
        cursor: pointer;
    }

    .analyse {
        font-size: 12px;
        color: #2468F2;
        font-weight: 400;
        float: right;
        display: flex;
        align-items: center;
        padding: 0;
    }

    .broker-detail {
        display: flex;
        margin-top: 25px;
        justify-content: space-between;
        align-items: center;

        .detail-item {
            width: 20%;

            .bigger {
                font-size: 16px;
                color: #151B26;
                line-height: 34px;
                font-weight: 500;
            }

            .percent {
                font-size: 16px;
                line-height: 34px;
                font-weight: 500;
            }
        }

        .detail-item-long {
            width: 22%;
        }

        .split {
            display: inline-block;
            border-left: 1px solid #E8E9EB;
            height: 40px;
            margin-left: -20px;
        }
    }

    .chart {
        border: none;
        padding: 10px 0;
        width: auto;
    }
}
