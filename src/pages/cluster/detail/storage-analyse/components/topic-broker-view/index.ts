/**
 * 主题存储分析-节点组件
 *
 * @file broker.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import m from 'moment';
import _ from 'lodash';
import {Tooltip, Progress, Button} from '@baidu/sui';
import {Component} from 'san';
import AnalaseDetail from '../analyse-detail/index';
import {BcmChartPanel} from '@baiducloud/bcm-sdk-san';
import {formatTime} from '@/common/util';
import {OutlinedRight} from '@baidu/sui-icon';
import {Alarm} from '@baidu/xicon-san';
import {percents, getSkin} from '../../util';
import {byte2GB} from '@/common/util';
import * as echarts from 'echarts/core';
import {LineChart} from 'echarts/charts';
import {CanvasRenderer} from 'echarts/renderers';
import {
    VisualMapComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
} from 'echarts/components';
import './index.less';

const klass = 'bms-cluster-storage-broker-view';

echarts.use([
    LineChart,
    VisualMapComponent,
    CanvasRenderer,
    LegendComponent,
    GridComponent,
    TooltipComponent,
]);

export const Color = [
    '#2468F2', '#77D160', '#E9B21A', '#5F69EE', '#0BA69B',
    '#A247CC', '#63A60B', '#BF870D', '#9781FA', '#5ED1CF'
];
export default class TopicBrokerView extends Component {

    static template = html`
    <div class="${klass}">
        <span class="title">节点ID: {{broker.brokerId}}</span>
        <s-tooltip content="当前控制节点" placement="right">
            <span s-if="isController" class="controller">Contoller</span>
        </s-tooltip>
        <s-button
            disabled="{{broker.topicUsedPercent === 'NaN'}}"
            skin="stringfy"
            class="analyse"
            on-click="viewDetail"
        >
            分析
            <s-outlined-right color="#2468F2" width="16" height="16" style="margin-left: 2px; margin-top: -1px"/>
        </s-button>
        <div class="broker-detail">
            <div class="detail-item">
                <p>磁盘总量</p>
                <p><span class="bigger">{{broker.totalBytes}}</span> GB</p>
            </div>
            <div class="detail-item">
                <p>剩余可用</p>
                <p><span class="bigger">{{broker.freeBytes}}</span> GB</p>
            </div>
            <template s-if="numberOfDisk === 1">
                <div class="detail-item">
                    <p>主题占用率</p>
                    <p class="percent {{colorComputed}}">{{broker.topicUsedPercent !== 'NaN' ? broker.topicUsedPercent : '--'}} <span style="font-size: 12px">%</span></p>
                </div>
                <div class="detail-item">
                    <s-progress
                        width="68"
                        height="68"
                        percent="{{broker.topicUsedPercent || 0}}"
                        type="circle"
                        skin="{{broker.topicUsedPercent | getSkin}}"
                    >
                        <div slot="info"></div>
                    </s-progress>
                </div>
            </template>
           <template s-elif="numberOfDisk > 1">
                <span class="split"></span>
                <div class="detail-item detail-item-long">
                    <s-tooltip placement="top" s-if="broker.maxDisk.topicUsedPercent > 80">
                        <p>
                            <s-alarm
                                s-if="broker.maxDisk.topicUsedPercent > 80"
                                theme="line"
                                color="#F33E3E"
                                size="{{16}}"
                                strokeLinejoin="round"
                            />
                            单磁盘最大主题占用率
                        </p>
                        <p class="percent {{colorComputed}}">
                            {{broker.maxDisk.topicUsedPercent}} <span style="font-size: 12px">%</span>
                        </p>
                        <div slot="content">
                            {{broker.maxDisk.diskId | getDiskName}}的使用率已超过80%，建议您立即处理。
                        </div>
                    </s-tooltip>
                    <template s-else>
                        <p>
                            单磁盘最大主题占用率
                        </p>
                        <p class="percent {{colorComputed}}">
                            {{broker.maxDisk.topicUsedPercent}} <span style="font-size: 12px">%</span>
                        </p>
                    </template>
                </div>
                <div class="detail-item">
                    <s-progress
                        width="68"
                        height="68"
                        percent="{{broker.maxDisk.topicUsedPercent || 0}}"
                        type="circle"
                        skin="{{broker.maxDisk.topicUsedPercent | getSkin}}"
                    >
                        <div slot="info">{{broker.maxDisk.diskId | getDiskName}}</div>
                    </s-progress>
                </div>
           </template>
        </div>
        <div
            class="chart"
            id="{{'chart-content-' + broker.brokerId}}"
            style="height: 230px;"
        ></div>
    </div>`;

    static components = {
        's-tooltip': Tooltip,
        's-progress': Progress,
        's-button': Button,
        'bcm-chart': BcmChartPanel,
        's-outlined-right': OutlinedRight,
        's-alarm': Alarm
    };

    static computed = {
        colorComputed() {
            const numberOfDisk = this.data.get('numberOfDisk');
            const usedPercent = numberOfDisk === 1
                ? this.data.get('broker.topicUsedPercent')
                : this.data.get('broker.maxDisk.topicUsedPercent');
            if (usedPercent < 30) {
                return 'health';
            }
            else if (usedPercent < 60) {
                return 'normal';
            }
            else if (usedPercent < 80) {
                return 'warn';
            }
            else {
                return 'danger';
            }
        }
    };

    static filters = {
        getDiskName(diskId: string): string {
            const diskList = this.data.get('broker.disks');
            return _.find(diskList, disk => disk.diskId === diskId)?.label;
        },
        getSkin
    };

    initData() {
        return {
            percents,
            usedPercentArr: [],
            option: {
                color: Color,
                title: {
                    left: 'left',
                    text: '',
                    textStyle: {
                        fontSize: 12,
                        width: 300,
                        overflow: 'breakAll',
                        color: '#151B26'
                    }
                },
                legend: {
                    left: 'right',
                    itemHeight: 2,
                    itemWidth: 12,
                    icon: 'rect',
                    scrollDataIndex: 0,
                    orient: 'horizontal',
                    type: 'scroll',
                    padding: [5, 30, 5, 200],
                    pageIconSize: 8,
                },
                tooltip: {
                    trigger: 'axis',
                    confine: true,
                    formatter: (params) => {
                        let template = `时间：<span style="font-weight: bold">${params[0]?.name}</span> <br />
                        主题：<span style="font-weight: bold">${this.data.get('topic')}</span><br />`;
                        params.forEach((item, index) => {
                            template += `
                                所在数据盘：<span style="font-weight: bold">${item.seriesName}</span> <br/>
                                使用量:
                                <span style="font-weight: bold">${item.value !== 'NaN' ? item.value : '--'} GB</span>
                                使用率:  <span style="font-weight: bold">${this.tip(item.seriesName, item.dataIndex)}%</span> <br/>
                                `;
                        });
                        return template;
                    }
                },
                xAxis: {
                    type: 'category',
                    data: []
                },
                yAxis: [
                    {
                        name: '[GB]',
                        nameTextStyle: {
                            align: 'right',
                            width: 23,
                            color: '#84868C'
                        },
                        type: 'value',
                        axisLabel: {
                            width: 25,
                            color: '#84868C'
                        },
                        min: 0,
                        position: 'left',
                        axisLine: {
                            onZero: false,
                        },
                        offset: 12,
                        splitLine: {
                            lineStyle: {
                                color: '#E8E9EB'
                            }
                        },
                    },
                ],
                dataZoom: [{
                    type: 'slider',
                    handleIcon: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAICAIAAABPmPnhAAAABmJLR0QA/wD/AP+gvaeTAAAAIUlEQVQImWNUyfjEgBsw4ZFDkb49nReNQYpucqQZKXI5AM9uBgn5QGRyAAAAAElFTkSuQmCC',
                    handleSize: '120%',
                    height: '8px',
                    backgroundColor: '#f4f4f2',
                    fillerColor: '#E6F0FF',
                    borderColor: 'transparent',
                    moveHandleStyle: {
                        opacity: 0,
                    },
                    dataBackground: {
                        lineStyle: {
                            opacity: 0,
                        },
                        areaStyle: {
                            opacity: 0,
                        },
                    },
                    selectedDataBackground: {
                        lineStyle: {
                            opacity: 0,
                        },
                        areaStyle: {
                            opacity: 0,
                        },
                    },
                }],
                series: []
            }
        };
    }

    attached() {
        const {broker} = this.data.get('');
        const option = this.data.get('option');
        option.series = broker.diskMetrics.map(item =>
            ({
                name: item.label,
                type: 'line',
                showSymbol: false,
                data: item.dataPoints.map(item => byte2GB(item.value)),
            })
        );
        option.legend.data = broker.diskMetrics.map(item => item.label);
        option.xAxis.data = broker.diskMetrics[0].dataPoints.map(item => formatTime(item.timestamp));
        const myChart = echarts.init(document.getElementById(`chart-content-${broker.brokerId}`));
        option && myChart.setOption(option);
        this.data.set('loading', false);
        window.addEventListener('resize', function () {
            myChart.resize();
        });
    }

    viewDetail(event: Event) {
        event.stopPropagation();
        const {broker, numberOfDisk, clusterId, topic} = this.data.get('');
        const dialog = new AnalaseDetail({
            data: {
                brokerId: broker.brokerId,
                topic,
                page: 'topic',
                numberOfDisk,
                clusterId,
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    tip(seriesName: string, index: number): string[] {
        const {broker} = this.data.get('');
        const diskIndex = broker.diskMetrics.findIndex(item => item.label === seriesName);
        return broker.diskMetrics[diskIndex].dataPoints[index]?.percent?.toFixed(2) || '--';
    }
}
