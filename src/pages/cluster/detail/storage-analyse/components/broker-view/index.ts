/**
 * 集群存储分析-节点组件
 *
 * @file broker.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import m from 'moment';
import _ from 'lodash';
import {Tooltip, Progress, Button} from '@baidu/sui';
import {Component} from 'san';
import AnalaseDetail from '../analyse-detail/index';
import {BcmChartPanel} from '@baiducloud/bcm-sdk-san';
import {BcmSDK} from '@baiducloud/bcm-sdk';
import {SCOPE} from '@/common/config';
import HttpClient from '@baiducloud/httpclient';
import {formatUtcTime} from '@/common/util';
import {OutlinedRight} from '@baidu/sui-icon';
import {Alarm} from '@baidu/xicon-san';
import api from '@/common/client';
import {percents, getSkin} from '../../util';
import {byte2GB} from '@/common/util';
import './index.less';

const klass = 'bms-cluster-storage-broker-view';
export default class BrokerView extends Component {


    static template = html`
    <div class="${klass}">
        <span class="title">节点ID: {{broker.brokerId}}</span>
        <s-tooltip content="当前控制节点" placement="right">
            <span s-if="isController" class="controller">Contoller</span>
        </s-tooltip>
        <!--<span style="margin-left: 12px">该节点共有{{numberOfDisk}}块磁盘</span>-->
        <s-button
            disabled="{{broker.usedPercent === 'NaN'}}"
            skin="stringfy"
            class="analyse"
            on-click="viewDetail"
        >
            分析
            <s-outlined-right color="#2468F2" width="16" height="16" style="margin-left: 2px; margin-top: -1px"/>
        </s-button>
        <div class="broker-detail">
            <div class="detail-item">
                <p>磁盘总量</p>
                <p><span class="bigger">{{broker.totalBytes}}</span> GB</p>
            </div>
            <div class="detail-item">
                <p>剩余可用</p>
                <p><span class="bigger">{{broker.freeBytes}}</span> GB</p>
            </div>
            <template s-if="numberOfDisk === 1">
                <div class="detail-item">
                    <p>使用率</p>
                    <p class="percent {{colorComputed}}">{{broker.usedPercent !== 'NaN' ? broker.usedPercent : '--'}} <span style="font-size: 12px">%</span></p>
                </div>
                <div class="detail-item">
                    <s-progress
                        width="68"
                        height="68"
                        percent="{{broker.usedPercent || 0}}"
                        type="circle"
                        skin="{{broker.usedPercent | getSkin}}"
                    >
                        <div slot="info"></div>
                    </s-progress>
                </div>
            </template>
           <template s-elif="numberOfDisk > 1">
                <span class="split"></span>
                <div class="detail-item">
                    <s-tooltip placement="top" s-if="broker.maxDisk.usedPercent > 80">
                        <p>
                            <s-alarm
                                s-if="broker.maxDisk.usedPercent > 80"
                                theme="line"
                                color="#F33E3E"
                                size="{{16}}"
                                strokeLinejoin="round"
                            />
                            单磁盘最大使用率
                        </p>
                        <p class="percent {{colorComputed}}">
                            {{broker.maxDisk.usedPercent}} <span style="font-size: 12px">%</span>
                        </p>
                        <div slot="content">
                            {{broker.maxDisk.diskId | getDiskName}}的使用率已超过80%，建议您立即处理。
                        </div>
                    </s-tooltip>
                    <template s-else>
                        <p>
                            单磁盘最大使用率
                        </p>
                        <p class="percent {{colorComputed}}">
                            {{broker.maxDisk.usedPercent}} <span style="font-size: 12px">%</span>
                        </p>
                    </template>
                </div>
                <div class="detail-item">
                    <s-progress
                        width="68"
                        height="68"
                        percent="{{broker.maxDisk.usedPercent || 0}}"
                        type="circle"
                        skin="{{broker.maxDisk.usedPercent | getSkin}}"
                    >
                        <div slot="info">{{broker.maxDisk.diskId | getDiskName}}</div>
                    </s-progress>
                </div>
           </template>
        </div>
        <div class="chart">
            <bcm-chart
                s-ref="chart"
                scope="{{scope}}"
                legend="{{legend}}"
                tooltip="{{tooltip}}"
                height="{{200}}"
                dimensions="{{dimensions}}"
                metrics="{{metrics}}"
                statistics="{{statistics}}"
                time="{= time =}"
                period="{{periodTime}}"
                startTime="{{startTime}}"
                endTime="{{endTime}}"
                unit="{{unit}}"
                sdk="{{bcmSdk}}"
                showbigable="{{false}}"
                connect-nulls="{{true}}"
                queryProccessor="{{queryProccessor}}"
                requester="{{requester}}"
                proccessor="{{proccessor}}"
                seriesOption="{{seriesOption}}"
                options="{{options}}"
            >
        </div>
    </div>`;

    static components = {
        's-tooltip': Tooltip,
        's-progress': Progress,
        's-button': Button,
        'bcm-chart': BcmChartPanel,
        's-outlined-right': OutlinedRight,
        's-alarm': Alarm
    };

    static computed = {
        dimensions(): object[] {
            const broker = this.data.get('broker');
            const clusterId = this.data.get('clusterId');
            const diskList = this.data.get('diskList');
            let res = [];
            diskList.forEach(disk => {
                res.push([{
                    name: 'ClusterId',
                    value: clusterId
                },
                {
                    name: 'NodeId',
                    value: broker.nodeId
                },
                {
                    name: 'DiskId',
                    value: disk.value
                }]);
            });
            return res;
        },
        colorComputed() {
            const numberOfDisk = this.data.get('numberOfDisk');
            const usedPercent = numberOfDisk === 1
                ? this.data.get('broker.usedPercent')
                : this.data.get('broker.maxDisk.usedPercent');
            if (usedPercent < 30) {
                return 'health';
            }
            else if (usedPercent < 60) {
                return 'normal';
            }
            else if (usedPercent < 80) {
                return 'warn';
            }
            else {
                return 'danger';
            }
        },
        legend(): NormalObject {
            const hide = this.data.get('numberOfDisk') === 1;
            return {
                show: !hide,
                left: 'right',
                itemHeight: 2,
                itemWidth: 12,
                scrollDataIndex: 0,
                orient: 'horizontal',
                align: 'left'
            };
        }
    };

    static filters = {
        getDiskName(diskId: string): string {
            const diskList = this.data.get('diskList');
            return _.find(diskList, disk => disk.value === diskId).text;
        },
        getSkin
    };

    initData() {
        return {
            percents,
            bcmSdk: new BcmSDK({
                client: new HttpClient({}, {
                    getCsrfToken() {
                        return this.$cookie.get('bce-user-info');
                    },
                    getCurrentRegion() {
                        return this.$context.getCurrentRegion();
                    }
                })
            }, this.$context),
            seriesOption: {
                type: 'line',
                chart: {
                    symbol: 'none'
                }
            },
            queryProccessor: this.queryProccessor.bind(this),
            scope: SCOPE,
            unit: 'GB',
            metrics: 'DiskUsedBytes,DiskUsedPercent',
            statistics: 'average',
            usedPercentArr: [],
            options: {
                dataZoom: {
                    type: 'slider',
                    show: false
                },
                tooltip: {
                    trigger: 'axis',
                    confine: true,
                    formatter: (params) => {
                        let template = '';
                        params.forEach((item, index) => {
                            template += `
                                <span style="font-weight: bold">${item.name} ${item.seriesName}</span> <br/>
                                磁盘使用量:
                                <span style="font-weight: bold">${item.value !== 'NaN' ? item.value : '--'} GB</span>
                                磁盘使用率:  <span style="font-weight: bold">${this.tip(item.seriesName, item.dataIndex, index)}%</span> <br/>
                                `;
                        });
                        return template;
                    }
                }
            },
        };
    }

    inited() {
        this.handleReq();
    }

    viewDetail(event: Event) {
        event.stopPropagation();
        const {broker, numberOfDisk, clusterId} = this.data.get('');
        const dialog = new AnalaseDetail({
            data: {
                brokerId: broker.brokerId,
                numberOfDisk,
                page: 'cluster',
                clusterId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    tip(name: string, index: number, diskIndex: number): string[] {
        const usedPercentArr = this.data.get('usedPercentArr');
        const diskList = this.data.get('diskList');
        const diskId = _.filter(diskList, disk => disk.text === name)[0]?.value;
        const item = _.filter(usedPercentArr, i => i.dimensions[1]?.value === diskId)[0];
        return item?.dataPoints[index]?.average?.toFixed(2) || '--';
    }

    // 处理请求参数
    queryProccessor(data: {startTime: string, endTime: string, [x: string]: string | number}) {
        return {
            ...data,
            cycle: 120,
            type: 'Node',
            scope: 'BCE_MQ_KAFKA',
            statistics: [this.data.get('statistics')],
            userId: this.$context.getUserId(),
            region: this.$context.getCurrentRegion().id,
            endTime: formatUtcTime(m(data.endTime)),
            startTime: formatUtcTime(m(data.startTime))
        };
    }

    handleReq() {
        this.data.set('requester', api.bcmMetricDataMetricData.bind(api));
        // 设置请求返回参数
        this.data.set('proccessor', this.proccessor.bind(this));
    }

    // 处理返回数据
    proccessor(
        data: {
            metrics: Array<{
                dataPoints: Array<{average: number, minimum: number, maximum: number, sum: number, timestamp: string}>;
                dimensions: NormalObject;
                metricName: string;
                resourceId: string;
            }>;}
    ) {
        let newData = {series: [], category: []};
        const usedBytesArr = _.filter(data.metrics, metric => metric.metricName === 'DiskUsedBytes');
        const usedPercentArr = _.filter(data.metrics, metric => metric.metricName === 'DiskUsedPercent');
        this.data.set('usedPercentArr', usedPercentArr);
        newData.series = usedBytesArr.map((item, index) => {
            const seriesData = item.dataPoints.map(i => ({
                average: byte2GB(i.average),
                minimum: i.minimum,
                maximum: i.maximum,
                sum: i.sum
            }));
            return {
                data: seriesData,
                name: this.getDiskName(_.find(item.dimensions, dimension => dimension.name === 'DiskId').value),
            };
        });
        data.metrics[0]?.dataPoints.forEach(i => {
            newData.category.push(i.timestamp);
        });

        return newData;
    }

    getDiskName(diskId: string) {
        const diskList = this.data.get('diskList');
        return _.find(diskList, disk => disk.value === diskId)?.text;

    }
}
