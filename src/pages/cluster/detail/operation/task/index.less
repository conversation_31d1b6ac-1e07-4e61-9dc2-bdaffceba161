@klass: bms-cluster-task-detail;
@backgrounColor: #F7F7F9;
@whiteColor: #fff;
@titleColor: #5C5F66;
@tableHeadColor: #F2F2F4;

.@{klass} {
    width: 100%;
    height: 100%;
    background: @backgrounColor;

    .nav_content {
        height: 48px;
        background: @whiteColor;
        padding: 0px 16px;
    }

    &_content_wrap {
        padding: 24px;
        width: 100%;
        background: @whiteColor;
        margin: 16px 16px 0;
        border-radius: 6px;

        .progress_area {
            display: flex;
            align-items: flex-start;

            span {
                margin-top: -5px;
            }
        }

        .diff-area {
            display: flex;
            justify-content: flex-start;
        }
    }

    .task-pagination {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;

        .s-pagination-total {
            margin-right: 16px;
        }
    }

    .s-table-subrow-wrapper {
        background: @backgrounColor;
        padding: 24px 60px;
        .sub-table {
            .sub-table-title {
                font-size: 12px;
                color: @titleColor;
                line-height: 20px;
                margin-bottom: 20px;
            }

            .s-table-thead {
                background: @tableHeadColor;
            }

            .s-pagination-wrapper {
                .s-pagination-pager li {
                    background-color: @backgrounColor;
                }
                .s-pagination-pager .disable {
                    background-color: @backgrounColor!important;
                }
                .s-pagination-active-page, .s-pagination-pagersize-wrapper, .s-pagination-page-go-wrapper, .s-pagination-page-go {
                    background-color: @whiteColor!important;
                }
            }
        }
    }
}
