/**
 * 任务详情
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {AppDetailCell, AppLegend, Empty, AppDetailPage} from '@baidu/sui-biz';
import {Button, Loading, Progress, Table, Notification, Pagination} from '@baidu/sui';
import DetailNav from '@/components/nav/detail';
import OperationContent from '@/components/operation-content';
import {ROUTE_PATH, PAGER_SUI, TABLE_SUI} from '@/common/config';
import {ClusterTaskStatus} from '@/common/enums';
import {ClusterTaskStatusType, ClusterTaskType, GroupNameType, DiagnosisType} from '@/common/enums/constant';
import {renderStatus} from '@/common/html';
import {UtilHandler} from '../../../util/util';
import {formatEmpty} from '@/common/util';
import {throttle} from '@/common/decorator';
import api from '@/common/client';
import './index.less';

const klass = 'bms-cluster-task-detail';

const TEXT_MAP = {
    operationId: '操作ID：',
    jobId: '任务ID：',
    name: '所属任务：',
    type: '操作类型：',
    startTime: '启动时间：',
    endTime: '结束时间：'
};

const renderItem = (
    label: string | number,
    key: string,
    value: string | string[] | number | Object = '',
    isShow: boolean = true) => ({label, key, value, isShow});

type groupType = {
    groupName: string;
    aliaName: string;
    state: string;
    analysis: boolean;
};

@decorators.asPage(ROUTE_PATH.clusterTaskDetail)
export default class extends AppDetailPage {
    pageTitle = '操作详情';
    static template = html`
    <div class="${klass}">
        <nav
            slot="pageTitle"
            title="{{route.query.type | getTitle}}"
            options="{{options}}"
            class="nav_content"
            back="#${ROUTE_PATH.clusterOperationList}?clusterId={{route.query.clusterId}}&name={{route.query.name}}"
        >
            <div slot="right">
                <template s-if="showExtra">
                    <s-button
                        s-if="{{showStart}}"
                        on-click="onOperation('excute')">
                        启动
                    </s-button>
                    <s-button
                        s-if="{{showStart}}"
                        on-click="onOperation('cancel')">
                        取消
                    </s-button>
                    <s-button
                        s-if="detail.state === '${ClusterTaskStatusType.RUNNING}' && detail.started"
                        disabled="{{detail.schedule === 'SUSPEND'}}"
                        on-click="onOperation('suspend')">
                        暂停
                    </s-button>
                    <s-button
                        s-if="detail.state === '${ClusterTaskStatusType.SUSPENDED}'"
                        disabled="{{detail.schedule === 'EXECUTE'}}"
                        on-click="onOperation('resume')">
                        恢复
                    </s-button>
                </template>
                <s-button
                    width="46"
                    loading="{{isLoading}}"
                    on-click="onRefresh">
                    刷新
                </s-button>
            </div>
        </nav>
        <div class="${klass}_content_wrap">
            <s-legend label="{{baseInfo.title}}" noHighlight/>
            <s-detail-cell datasource="{{baseInfo.list}}" divide="3" labelWidth="60px"/>
        </div>
        <div class="${klass}_content_wrap">
            <s-legend label="执行进度" noHighlight/>
            <div class="progress_area"><span>完成度： </span><s-progress class="ml16" percent="{{percent}}" skin="normal" /></div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                has-Expand-Row="{{true}}"
                expandedIndex="{{expandedIndex}}"
                on-exprow-collapse="onView"
                on-exprow-expand="onView"
                datasource="{{table.datasource}}">
                <div slot="sub-table" class="sub-table">
                    <span s-if="row.diagnosis" class="sub-table-title">诊断信息：{{row.diagnosis | formatEmpty}}</span>
                    <div s-if="detail.type === '${ClusterTaskType.REASSIGN_PARTITION}'" class="sub-table-title">
                        <span>流量限制： {{row.interBrokerThrottle | formatEmpty}}</span>
                        <span style="margin-left: 400px">执行批次： {{row.batchSize | formatEmpty}}</span>
                    </div>
                    <s-table
                        columns="{{columns}}"
                        datasource="{{row.subTable}}"
                    >
                        <div slot="c-state">{{row.state | filterStatus | raw}}</div>
                    </s-table>
                    <s-pagination
                        layout="{{'total, pageSize, pager, go'}}"
                        total="{{row.pager.count || 0}}"
                        pageSize="{{row.pager.pageSize}}"
                        class="task-pagination"
                        page="{{row.pager.page}}"
                        pageSizes="{{row.pager.pageSizes}}"
                        max-item="{{7}}"
                        on-pagerChange="onPageChange($event, rowIndex)"
                        on-pagerSizeChange="onPageSizeChange($event, rowIndex)"
                    />
                </div>
                <div slot="c-groupName">{{row.groupName | formateGroupName}}</div>
                <div slot="c-state">{{row.state | filterStatus | raw}}</div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            s-if="{{row.analysis}}"
                            on-click="onView({value: {rowIndex}})">
                            查看
                        </s-button>
                        <span s-else>--</span>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty vertical actionText="" />
                </div>
            </s-table>
        </div>
        <div class="${klass}_content_wrap">
            <s-legend label="执行内容" noHighlight/>
            <div class="diff-area">
                <operation-content
                    width="{{520}}"
                    height="{{360}}"
                    data="{{detail.sourceContext}}"
                    title="修改前"
                ></operation-content>
                <operation-content
                    width="{{520}}"
                    height="{{360}}"
                    class="ml16"
                    data="{{detail.targetContext}}"
                    title="修改后"
                ></operation-content>
            </div>
        </div>
    </div>`;

    static components = {
        's-detail-page': AppDetailPage,
        's-detail-cell': AppDetailCell,
        'nav': DetailNav,
        's-button': Button,
        's-loading': Loading,
        's-legend': AppLegend,
        's-progress': Progress,
        's-table': Table,
        'operation-content': OperationContent,
        's-pagination': Pagination,
        's-empty': Empty
    };

    initData() {
        return {
            isLoading: false,
            datasource: [],
            percent: 0,
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'groupName',
                        label: '阶段名称',
                        width: 320
                    },
                    {
                        name: 'state',
                        label: '阶段状态',
                        width: 320,
                    },
                    {
                        name: 'operation',
                        label: '操作',
                        width: 560
                    }
                ]
            },
            columns: [
                {
                    name: 'name',
                    label: '子阶段名称',
                    width: 400
                },
                {
                    name: 'state',
                    label: '子阶段状态',
                    width: 400,
                }
            ],
            baseInfo: {
                title: '操作详情',
                list: []
            },
            expandedIndex: [],
            detail: {},
            pager: {...PAGER_SUI},
        };
    }

    static filters: SanFilterProps = {
        filterStatus(state: ClusterTaskStatusType) {
            return renderStatus(ClusterTaskStatus.fromValue(state));
        },
        formateGroupName(groupName: string) {
            return GroupNameType[`${groupName}`];
        },
        getTitle(type: string) {
            return UtilHandler.operationName(type);
        },
        showStart() {
            const state = this.data.get('detail.state');
            const started = this.data.get('detail.started');
            const name = this.data.get('detail.name');
            return state === ClusterTaskStatusType.NEW && !started && name === ClusterTaskType.REASSIGN_PARTITION;
        },
        formatEmpty
    };

    static computed: SanComputedProps = {
        showExtra(): boolean {
            const detail = this.data.get('detail');
            const isNotRessign = detail.type !== ClusterTaskType.REASSIGN_PARTITION;
            const isNotShowState = _.includes(['FINISHED', 'FAILED', 'CANCELED', 'PENDING'], detail.state);
            return !(isNotRessign || isNotShowState);
        }
    };

    attached() {
        this.getOperationDetail();
    }

    setDetailInfo() {
        const {
            type,
            operationId,
            name,
            startTime,
            endTime
        } = this.data.get('detail');
        const baseInfo = {
            title: '操作详情',
            list: [
                renderItem(TEXT_MAP.type, 'type', UtilHandler.operationName(type)),
                renderItem(TEXT_MAP.name, 'name', UtilHandler.taskName(name)),
                renderItem(TEXT_MAP.operationId, 'operationId', UtilHandler.operationId(operationId)),
                renderItem(TEXT_MAP.startTime, 'startTime', UtilHandler.createTime(startTime)),
                renderItem(TEXT_MAP.endTime, 'endTime', UtilHandler.createTime(endTime))
            ]
        };
        this.data.set('baseInfo', baseInfo);
    }

    async getOperationDetail() {
        const {clusterId, operationId} = this.data.get('route.query');
        const result = await api.getOperationDetail(clusterId, operationId, {});
        this.data.set('detail', result);
        this.data.set('options', ClusterTaskStatus.fromValue(result.state));
        this.setDetailInfo();
        this.data.set('percent', result.process);
        const sourceContext = JSON.parse(result.sourceContext || '{}');
        const targetContext = JSON.parse(result.targetContext || '{}');
        this.data.set('detail.sourceContext', JSON.stringify(sourceContext, null, '\t'));
        this.data.set('detail.targetContext', JSON.stringify(targetContext, null, '\t'));
        this.data.set('table.datasource', result.groups.map((item: groupType) => ({
            groupName: item.groupName,
            aliaName: item.aliaName,
            state: item.state,
            analysis: item.analysis,
            subSlot: item.analysis ? 'sub-table' : '',
            pager: item.analysis ? this.data.get('pager') : null
        })));
        if (result.type === ClusterTaskType.REASSIGN_PARTITION) {
            this.data.set('columns', [
                {
                    name: 'topic',
                    label: '主题名称',
                    width: 100
                },
                {
                    name: 'partition',
                    label: '分区ID',
                    width: 100,
                },
                {
                    name: 'targetReplicas',
                    label: '副本',
                    width: 100
                },
                {
                    name: 'state',
                    label: '分区状态',
                    width: 100,
                }
            ]);
        }
        this.data.set('table.loading', false);
    }

    async getGroupDetail(rowIndex: number) {
        const {clusterId, operationId} = this.data.get('route.query');
        const {page, pageSize} = this.data.get(`table.datasource[${rowIndex}].pager`);
        const groupName = this.data.get(`table.datasource[${rowIndex}].groupName`);
        const result = await api.getGroupDetaill(clusterId, operationId, groupName, {pageNo: page, pageSize});
        this.data.set(`table.datasource[${rowIndex}].subTable`, result.kinds);
        this.data.set(`table.datasource[${rowIndex}].diagnosis`, DiagnosisType[`${result.diagnosis}`]);
        this.data.set(`table.datasource[${rowIndex}].pager.count`, result.totalCount);
    }

    async getResignTaskProcess(index: number) {
        const {clusterId, operationId} = this.data.get('route.query');
        const {page, pageSize} = this.data.get(`table.datasource[${index}].pager`);
        const result = await api.getResignTaskProcess(clusterId, operationId, {pageNo:page, pageSize});
        this.data.set(`table.datasource[${index}].subTable`, result.assignments);
        this.data.set(`table.datasource[${index}].batchSize`, result.batchSize);
        this.data.set(`table.datasource[${index}].diagnosis`, DiagnosisType[`${result.diagnosis}`]);
        this.data.set(`table.datasource[${index}].interBrokerThrottle`, result.interBrokerThrottle);
        this.data.set(`table.datasource[${index}].pager.count`, result.totalCount);
    }

    async onView(target: {value: {rowIndex: number}}) {
        await this.getOperationDetail();
        let expandedIndex = this.data.get('expandedIndex');
        const index = _.findIndex(expandedIndex, (i: number) => target.value.rowIndex === i);
        const type = this.data.get('detail.type');
        if (index === -1) {
            this.data.push('expandedIndex', target.value.rowIndex);
        } else {
            this.data.removeAt('expandedIndex', index);
        }
        const method = type !== ClusterTaskType.REASSIGN_PARTITION ? 'getGroupDetail' : 'getResignTaskProcess';
        expandedIndex = this.data.get('expandedIndex');
        expandedIndex.forEach((item: number) => {
            this[method](item);
        });
    }

    @throttle(1000)
    async onOperation(operationType: string) {
        const {clusterId, operationId} = this.data.get('route.query');
        switch (operationType) {
            case 'excute':
                await api.excuteTask(clusterId, operationId, {});
                Notification.success('任务启动成功');
                break;
            case 'cancel':
                await api.cancelTask(clusterId, operationId, {});
                Notification.success('任务取消成功');
                break;
            case 'suspend':
                await api.suspendTask(clusterId, operationId, {});
                Notification.success('任务暂停成功');
                break;
            case 'resume':
                await api.resumeTask(clusterId, operationId, {});
                Notification.success('任务恢复成功');
                break;
        }
        this.onRefresh();
    }

    // 分页 page 设置
    async onPageChange(args: {value: {page: number, pageSize: number}}, rowIndex: number) {
        this.data.set(`table.datasource[${rowIndex}].pager.pageSize`, args.value.pageSize);
        this.data.set(`table.datasource[${rowIndex}].pager.page`, args.value.page);
        const type = this.data.get('detail.type');
        type !== ClusterTaskType.REASSIGN_PARTITION
            ? await this.getGroupDetail(rowIndex)
            : await this.getResignTaskProcess(rowIndex);
    }

    // 分页 pageSize 设置
    async onPageSizeChange(args: {value: {page: number, pageSize: number}}, rowIndex: number) {
        this.data.set(`table.datasource[${rowIndex}].pager.pageSize`, args.value.pageSize);
        this.data.set(`table.datasource[${rowIndex}].pager.page`, 1);
        const type = this.data.get('detail.type');
        type !== ClusterTaskType.REASSIGN_PARTITION
            ? await this.getGroupDetail(rowIndex)
            : await this.getResignTaskProcess(rowIndex);
    }

    async onRefresh() {
        this.data.set('isLoading', true);
        this.data.set('expandedIndex', []);
        await this.getOperationDetail();
        Notification.success('刷新成功');
        this.data.set('isLoading', false);
    }

    onRegionChange() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }
}
