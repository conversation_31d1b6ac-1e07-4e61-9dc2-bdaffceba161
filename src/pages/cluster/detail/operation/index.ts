/**
 * 任务管理列表
 *
 * @file operationList.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html, redirect} from '@baiducloud/runtime';
import {Pagination, Table, Notification, Button, Select} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {throttle} from '@/common/decorator';
import CommanTable from '@/components/common-table/index';
import EllipsisTip from '@/components/ellipsis-tip';
import {renderStatus} from '@/common/html';
import {TABLE_SUI, PAGER_SUI, ROUTE_PATH} from '@/common/config';
import {formatTime, pickEmpty, formatEmpty} from '@/common/util';
import {ClusterTaskStatus, AllEnum, ClusterTask} from '@/common/enums';
import {ClusterTaskStatusType, taskType, operationType, ClusterTaskType} from '@/common/enums/constant';

import api from '@/common/client';
import './index.less';

const klass = 'bms-cluster-operation-list';

const allEnum = AllEnum.toArray();
let timeCount: number;

type OperationItem = {
    operationId: string;
    type: string;
    state: string;
    process: number;
    createTime: string;
    startTime: string;
    endTime: string;
    started: boolean;
    schedule: string;
    name: string;
    showExtra: boolean;
};

const clusterTaskArray = ClusterTask.toArray();

export default class Operation extends CommanTable implements DetailRefresh {

    searchKey = 'operationName';

    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">任务管理</h2>
            </div>
            <div slot="bulk">
                任务类型：
                <s-select
                    datasource="{{clusterTaskList}}"
                    value="{{type}}"
                    filterable
                    on-change="onTypeChange"
                />
            </div>
            <div slot="filter">
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                has-Expand-Row="{{true}}"
                on-exprow-collapse="onRowExpand"
                on-exprow-expand="onRowExpand"
                on-sort="onSort"
                on-filter="onFilter"
                expandedIndex="{{expandedIndex}}"
                datasource="{{table.datasource}}">
                <div slot="expanded-row">
                    <s-table
                        columns="{{subColumns}}"
                        datasource="{{row.subDatasource}}"
                    >
                        <div slot="c-type">{{row.type | formateOperationName}}</div>
                        <div slot="c-state">{{row.state | filterState(row.process) | raw}}</div>
                        <div slot="c-operation">
                            <span class="operation">
                                <s-button
                                    skin="stringfy"
                                    class="table-btn-slim"
                                    on-click="onView(row)">
                                    查看
                                </s-button>
                                <template s-if="row.showExtra">
                                    <s-button
                                        skin="stringfy"
                                        class="table-btn-slim"
                                        s-if="{{row.showStart}}"
                                        on-click="onOperation(row, 'excute')">
                                        启动
                                    </s-button>
                                    <s-button
                                        skin="stringfy"
                                        class="table-btn-slim"
                                        s-if="{{row.showStart}}"
                                        on-click="onOperation(row, 'cancel')">
                                        取消
                                    </s-button>
                                    <s-button
                                        skin="stringfy"
                                        class="table-btn-slim"
                                        s-if="row.state === '${ClusterTaskStatusType.RUNNING}'"
                                        disabled="{{row.schedule === 'SUSPEND'}}"
                                        on-click="onOperation(row, 'suspend')">
                                        暂停
                                    </s-button>
                                    <s-button
                                        skin="stringfy"
                                        class="table-btn-slim"
                                        s-if="row.state === '${ClusterTaskStatusType.SUSPENDED}'"
                                        disabled="{{row.schedule === 'EXECUTE'}}"
                                        on-click="onOperation(row, 'resume')">
                                        恢复
                                    </s-button>
                                </template>
                            </span>
                        </div>
                    </s-table>
                    <s-pagination
                        class="sub-pagination"
                        s-if="{{row.datasource.length > 5}}"
                        layout="{{'total, pager'}}"
                        total="{{row.datasource.length}}"
                        pageSize="{{5}}"
                        page="{{pager.page}}"
                        on-pagerChange="onSubPageChange($event, row, rowIndex)"
                    />
                </div>
                <div slot="c-name">{{row.name | formateTaskName}}</div>
                <div slot="c-status">{{row.status | filterStatus | raw}}</div>
                <div slot="empty">
                    <s-empty vertical actionText="" />
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'ellipsis-tip': EllipsisTip,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty,
        's-select': Select
    };

    initData() {
        return {
            searchbox: {
                keyword: ''
            },
            clusterTaskList: clusterTaskArray,
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'name',
                        label: '任务类型',
                        width: 100
                    },
                    {
                        name: 'actionId',
                        label: '任务ID',
                        width: 220
                    },
                    {
                        name: 'status',
                        label: '任务状态',
                        width: 150
                    }
                ]
            },
            subColumns: [
                {
                    name: 'type',
                    label: '操作类型',
                    width: 100
                },
                {
                    name: 'operationId',
                    label: '操作ID',
                    width: 220
                },
                {
                    name: 'state',
                    label: '操作状态',
                    width: 150
                },
                {
                    name: 'startTime',
                    label: '启动时间',
                    width: 120,
                    render: (item: {startTime: string}) => formatTime(item.startTime)
                },
                {
                    name: 'endTime',
                    label: '结束时间',
                    width: 120,
                    render: (item: {endTime: string}) => formatTime(item.endTime)
                },
                {
                    name: 'operation',
                    label: '操作',
                    width: 100
                }
            ],
            selection: {
                mode: 'single'
            },
            pager: {...PAGER_SUI},
            expandedIndex: []
        };
    }

    static filters: SanFilterProps = {
        filterState(state: ClusterTaskStatusType, process: number) {
            let temp = renderStatus(ClusterTaskStatus.fromValue(state));
            if (_.includes([ClusterTaskStatusType.RUNNING], state)) {
                temp += html`<div class="desc">进度：${process}%</div>`;
            }
            return temp;
        },
        filterStatus(status: ClusterTaskStatusType) {
            let option = ClusterTaskStatus.fromValue(status);
            let temp =  `<span class="jobStatus-${option.taskKlass} taskStatus">${formatEmpty(option.text)}</span>`;
            return temp;
        },
        formateTaskName(type: string) {
            return taskType[`${type}`];
        },
        formateOperationName(type: string) {
            return operationType[`${type}`];
        },
    };

    attached() {
        this.getComList();
    }

    showExtra(row: OperationItem) {
        const isNotRessign = row.type !== ClusterTaskType.REASSIGN_PARTITION;
        const isNotShowState = _.includes(['FINISHED', 'FAILED', 'CANCELED', 'PENDING'], row.state);
        return !(isNotRessign || isNotShowState);
    }

    /**
     * 获取表格，重写是为了支持自动刷新不展示loading
     */
    async getComList(loadStatus = true) {
        this.data.set('table.loading', loadStatus);
        this.data.set('table.error', false);
        // 清除多选
        this.data.set('selection.selectedIndex', []);
        try {
            await this.getTableList();
            this.data.set('table.loading', false);
        }
        catch (error) {
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    /**
     * 分页，重写是为了支持换页后自动刷新定时器重置
     * @param {Number} page page
     * @param {Number} pageSize pageSize
     */
    onPageChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', args.value.page);
        this.refreshInfo();
    }

    onSubPageChange(args: {value: {page: number, pageSize: number}}, row, index) {
        const datasource = row.datasource;
        const start = (args.value.page - 1) * 5;
        this.data.set(`table.datasource[${index}].subDatasource`, datasource.slice(start, start + 5));
    }

    /**
     * 分页 pageSize 设置，重写是为了支持改变页面大小后自动刷新定时器重置
     */
    onPageSizeChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('selection.selectedIndex', []);
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', 1);
        this.refreshInfo();
    }

    async onTypeChange(target: {value: string}) {
        this.data.set('type', target.value);
        this.data.set('table.loading', true);
        await this.getTableList();
        this.data.set('table.loading', false);
    }

    async getTableList() {
        const {pager, type} = this.data.get('');
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            name: type,
        });
        this.data.set('expandedIndex', []);
        const {totalCount, result} =  await api.listJobs(this.data.get('clusterId'), params);
        this.data.set('pager.count', totalCount);
        this.data.set('allList', result);
        let datasource = result.map(job => ({
            ...job,
            subSlot: job.operations.length ? 'sub-name' : '',
            datasource: job.operations.map((item: OperationItem) => ({
                ...item,
                showExtra: this.showExtra(item),
                // eslint-disable-next-line max-len
                showStart: item.state === ClusterTaskStatusType.NEW && !item.started && item.name === ClusterTaskType.REASSIGN_PARTITION
            })),
        }));
        datasource = datasource.map(item => ({
            ...item,
            subDatasource: item.datasource.slice(0, 5)
        }));
        const firstIndex = _.findIndex(datasource, item => item.subSlot === 'sub-name');
        this.data.set('expandedIndex', [firstIndex]);
        this.data.set('table.datasource', datasource);
        this.data.set('pager.count', totalCount);
    }

    onView(row: OperationItem) {
        const {clusterId, name} = this.data.get('');
        redirect(`#${ROUTE_PATH.clusterTaskDetail}?name=${name}&operationId=${row.operationId}&clusterId=${clusterId}&status=${row.state}&type=${row.type}`);
    }

    @throttle(1000)
    async onOperation(row: OperationItem, operationType: string) {
        const {clusterId} = this.data.get('');
        switch (operationType) {
            case 'excute':
                await api.excuteTask(clusterId, row.operationId, {});
                Notification.success('任务启动成功');
                break;
            case 'cancel':
                await api.cancelTask(clusterId, row.operationId, {});
                Notification.success('任务取消成功');
                break;
            case 'suspend':
                await api.suspendTask(clusterId, row.operationId, {});
                Notification.success('任务暂停成功');
                break;
            case 'resume':
                await api.resumeTask(clusterId, row.operationId, {});
                Notification.success('任务恢复成功');
                break;
        }
        this.refreshInfo();
    }

    // refresh更新
    refreshInfo(loadstatus = true) {
        return this.getComList(loadstatus);
    }
}
