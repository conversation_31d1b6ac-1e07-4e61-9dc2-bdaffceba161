@klass: bms-cluster-operation-list;

.@{klass} {
    .s-pagination-total {
        margin-right: 16px!important;
    }
    .s-table-cell-text {
        padding-left: 12px!important;
    }
    .s-table-expanded-row {
        background-color: #F7F7F9;

        .s-table-thead {
            background-color: #e8e9eb;
        }
    }

    .sub-pagination {
        float: right;
        margin-top: 10px;

        .s-pagination-pager-wrapper {
            background-color: #F7F7F9;

            .s-pagination-pager>li {
                background-color: #F7F7F9!important;
            }
        }
    }

    .s-selectdropdown>div:first-child {
        max-height: 300px;
    }

    .taskStatus {
        border: 1px solid;
        border-radius: 2px;
        padding: 2px 8px;
    }

    .jobStatus {
        &-error {
            color: var(--errorColor);
            border-color: var(--errorColor);
        }
        &-success {
            color: var(--successColor);
            border-color: var(--successColor);
        }
        &-warning {
            color: var(--warningColor);
            border-color: var(--warningColor);
        }
        &-normal {
            color: var(--defaultColor);
            border-color: var(--defaultColor);
        }
        &-fail {
            color: var(--failColor);
            border-color: var(--failColor);
        }
        &-prepare {
            color: #fad000;
            border-color: #fad000;
        }
    }
}
