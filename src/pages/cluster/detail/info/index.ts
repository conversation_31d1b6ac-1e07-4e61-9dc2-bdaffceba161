/* eslint-disable max-len */
/**
 * 集群详情
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import moment from 'moment';
import {Component, defineComponent} from 'san';
import {html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {MultiToneSuccess, MultiToneError, OutlinedEye} from '@baidu/sui-icon';
import {Table, Button, Dialog, Notification, Pagination, Tooltip} from '@baidu/sui';
import {formatEmpty, formatTime, pickEmpty, formatUtcTime, getPayLoop, isOneCloudId, isInvalid} from '@/common/util';
import TileInfo from '@/components/tile-info';
import {ClusterNodeClientStatus, AllEnum, Payments} from '@/common/enums';
import {renderStatus, renderSwitch} from '@/common/html';
import {TABLE_SUI, PAGER_SUI, SERVICE_TYPE, ROUTE_PATH} from '@/common/config';
import EllipsisTip from '@/components/ellipsis-tip';
import {FORBID_HANDLER, LIST_OPERATION_SETTING} from '@/common/rules';
import {throttle} from '@/common/decorator';
import CommonTable from '@/components/common-table';
import api from '@/common/client';
import InstantEditor from '@/components/instant-editor';
import ControllerDialog from './controller-dialog';
import ParamDialog from './config-param';
import MyTooltip from '@/components/tip';
import {UtilHandler} from '../../util/util';
import {UpgrageType} from '../../util/conf';
import ZookeeperPw from './zookeeper-pw';
import {suffexV3Cluster} from '@/common/client';
import './index.less';

const klass = 'bms-cluster-detail-info';

const TEXT_MAP = {
    clusterId: '集群ID：',
    name: '集群名称：',
    createTime: '创建时间：',
    version: '集群版本：',
    runningTime: '运行时间：',
    region: '所在地区：',
    payment: '付费方式：',
    vipTime: '到期时间：',
    autoRenew: '自动续费：',
    autoRenewTime: '自动续费周期：',
    clusterSid: '账单信息：',
    config: '集群配置：',
    configName: '配置名称：',
    configVersion: '配置版本：',
    vpc: '所在网络：',
    subnets: '所在子网：',
    securityGroup: '安全组：',
    publicIpBandwidth: '公网带宽：',
    ssl_certificate: 'SSL 证书：',
    zookeeperPw: 'Zookeeper 信息：',
    KafkaJMX: 'Kafka JMX 地址：',
    intranetIpEnabled: '产品间转储：',
    storageType: '磁盘类型：',
    storageSize: '节点磁盘容量：',
    totalSize: '总磁盘容量：',
    nodeSpec: '节点类型：',
    numberOfBrokerNodes: '节点总数：',
    numberOfBrokerNodesPerZone: '单可用区节点数：',
    controllerOfBrokerNodes: 'Controller 节点ID：',
    aclEnabled: '权限控制：',
    authenticationModes: '认证方式：',
    deploySetEnabled: '部署集：',
    deployType: '部署方式：',
    logicalZones: '可用区：',
    storagePolicy: '容量阈值策略：',
    storagePolicyEnabled: '磁盘水位：',
    remoteStorageEnabled: '远程存储：',
};

const protocolColumns = [
    {name: 'securityProtocol', label: '协议类型', width: 150},
    {name: 'mode', label: '认证方式', width: 100},
    {name: 'aclEnabled', label: '权限控制', width: 100},
    {name: 'encryptionInTransit', label: '传输加密', width: 100},
    {name: 'usage', label: '使用场景', width: 100},
    {name: 'endpoint', label: '访问地址', width: 220}
];

const renderItem = (
    label: string | number,
    key: string,
    text: string | string[] | number | Object = '',
    isShow: boolean = true) => ({label, key, text, isShow});

type NodeClientStatus = {
    host: string;
    nodeId: string;
    status: string;
    publicIp: string;
    internalIp: string;
};

const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;

class RestartNode extends Component {
    static template = html`
    <div>重启节点最长需要10分钟时间。
        在此期间，Kafka集群可用于生成和使用数据，但在重启完成之前，您将无法执行其他集群操作。
    </div>`;
}

export default class Info extends CommonTable implements DetailRefresh {
    static template = html`
    <div class="${klass} bms-tab-info">
        <div class="detail-operation-btn">
            <s-button
                width="46"
                disabled="{{canUpgrade}}"
                on-click="onUpgrade">
                变更
            </s-button>
            <s-button
                width="46"
                class="ml16"
                disabled="{{canUpgrade}}"
                on-click="onRestart">
                重启
            </s-button>
            <s-button
                width="46"
                s-if="{{!showStart}}"
                class="ml16"
                disabled="{{!detail.canStop}}"
                on-click="onStop">
                停止
            </s-button>
            <s-button
                width="46"
                class="ml16"
                s-if="{{showStart}}"
                disabled="{{!detail.canStart}}"
                on-click="onStart">
                启动
            </s-button>
        </div>
        <tile-info
            s-for="item,index in infoList"
            isLast="{{index === infoList.length - 1}}"
            type="{{item.type}}"
            title="{{item.title}}"
            list="{{item.list}}"
        >
            <span slot="c-name-item" class="${klass}__clusterName">
                <label>${TEXT_MAP.name}</label>
                <span>
                    {{text}}
                    <instant-editor
                        s-if="{{text}}"
                        value="{{text}}"
                        request="{{editName}}"
                        check="{{check}}"
                        placeholder="请输入集群名称"
                        desc="不超过65个字符，仅支持数字/字母/特殊符号(_/-.)"
                    />
                </span>
            </span>
            <span slot="c-autoRenew-text">
                {{text}}
                <a s-if="!${isXushang}" href="{{renewLink}}" target="_blank" class="a-btn">续费管理</a>
            </span>
            <span slot="c-clusterSid-text" s-if="!${isXushang}">
                <a href="{{text.billingUrl}}" target="_blank" class="a-btn">{{text.clusterSid}}</a>
            </span>
            <span slot="c-ssl_certificate-text">
                <sui-tooltip content="下载默认证书">
                    <a
                        on-click="downloadSSL"
                        class="a-btn"
                        href="javascript:void(0);"
                    >
                        下载
                    </a>
                </sui-tooltip>
            </span>
            <span slot="c-zookeeperPw-text">
                <a href="javascript:void(0);" on-click="onShowZookeeperPw" class="a-btn">查看</a>
            </span>
            <span slot="c-KafkaJMX-text">
                <ellipsis-tip
                    style="display:inline-block;max-width:200px;"
                    text="{{text}}"
                    placement="top"
                    copy="{{true}}"
                    alwaysTip="{{true}}"
                />
            </span>
            <span slot="c-subnets-item" class="subnets-item">
                <span>{{label}}</span>
                <span class="subnets-item__text">
                    <div s-for="item,index in text">{{item}}{{index < text.length - 1 ? '，' : ''}}</div>
                </span>
            </span>
            <span slot="c-securityGroups-text">
                {{text}}
                <a
                    s-for="item in detail.securityGroups"
                    href="{{item | securityGroupHref}}"
                >{{item.name}}；</a>
            </span>
            <span slot="c-controllerOfBrokerNodes-text">
                {{text}}
                <a href="javascript:void(0);" on-click="onShowController" class="a-btn">查看</a>
            </span>
            <span slot="c-configId-text">
                {{text}}
                <a href="javascript:void(0);" on-click="onConfigView" class="a-btn">查看</a>
            </span>
            <span slot="c-configName-text">
                {{text}}
                <s-tooltip s-if="{{detail.config.configDescription}}" type="question" placement="top">
                    <p>{{detail.config.configDescription}}</p>
                </s-tooltip>
            </span>
            <span slot="c-revisionId-text">
                {{text}}
                <s-tooltip s-if="{{detail.config.revisionDescription}}" type="question" placement="top">
                    <p>{{detail.config.revisionDescription}}</p>
                </s-tooltip>
            </span>
            <span slot="c-storagePolicy-text">
                {{text}}
                <sui-tooltip placement="top" getPopupContainer="{{document.body}}">
                    <a href="javascript:void(0);" class="a-btn">策略详情</a>
                    <div slot="content">
                        {{text}}:
                        <span s-if="detail.storagePolicy.type === 'AUTO_DELETE'">
                            当磁盘空间使用率达到
                            <span class="yellow">{{detail.storagePolicy.autoDelete.diskUsedThresholdPercent}}%</span>时，
                            自动删除最早数据直到使用率降到阈值以下，
                            保底时长
                            <span class="yellow">{{detail.storagePolicy.autoDelete.logMinRetentionMs | filterMs}}</span>，
                            保底大小
                            <span class="yellow">{{detail.storagePolicy.autoDelete.logMinRetentionBytes | filterBytes}}</span>
                        </span>
                        <span s-if="detail.storagePolicy.type === 'AUTO_EXPAND'">
                            当磁盘空间使用率达到
                            <span class="yellow">{{detail.storagePolicy.autoExpand.diskUsedThresholdPercent}}%</span>时，
                            磁盘自动扩容<span class="yellow">{{detail.storagePolicy.autoExpand.stepForwardPercent}}%或{{detail.storagePolicy.autoExpand.stepForwardSize}}G</span>，
                            最大容量
                            <span class="yellow">{{detail.storagePolicy.autoExpand.maxStorageSize}}G</span>

                        </span>
                        <span s-if="detail.storagePolicy.type === 'DYNAMIC_RETENTION'">
                            当磁盘空间使用率达到
                            <span class="yellow">{{detail.storagePolicy.dynamicRetention.diskUsedThresholdPercent}}%</span>时，
                            主题自动缩短
                            <span class="yellow">{{detail.storagePolicy.dynamicRetention.stepForwardPercent}}%</span>消息保留时长，
                            保底时长
                            <span class="yellow">{{detail.storagePolicy.dynamicRetention.logMinRetentionMs | filterMs}}</span>
                        </span>
                    </div>
                </sui-tooltip>
            </span>
            <div slot="client-footer" class="client-wrap">
                <s-table
                    loading="{{table.loading}}"
                    datasource="{{table.datasource}}"
                    columns="{{table.columns}}"
                    on-filter="onFilter">
                    <div slot="c-publicIp">
                        {{row.publicIp | formatEmpty}}/{{row.internalIp | formatEmpty}}
                    </div>
                    <div slot="c-operation">
                        <s-button
                            class="table-btn-slim"
                            on-click="onReloadNode(row, rowIndex)"
                            disabled="{{disableRestart}}"
                            skin="stringfy">
                            重启
                        </s-button>
                        <s-button
                            class="table-btn-slim"
                            on-click="viewLog(row, rowIndex)"
                            skin="stringfy">
                            查看日志
                        </s-button>
                    </div>
                </s-table>
                <s-pagination
                    class="client-wrap__pager"
                    slot="pager"
                    s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                    layout="{{'total, pageSize, pager, go'}}"
                    total="{{pager.count}}"
                    pageSize="{{pager.pageSize}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    max-item="{{7}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </div>
            <div slot="accessConfig-footer">
                <s-table
                    class="client-wrap"
                    columns="{{protocolColumns}}"
                    datasource="{{detail.accessControls}}"
                >
                    <div slot="c-aclEnabled">
                        <s-icon-succ s-if="row.aclEnabled"></s-icon-succ>
                        <s-icon-err s-else></s-icon-err>
                    </div>
                    <div slot="c-encryptionInTransit">
                        <s-icon-succ s-if="row.encryptionInTransit"></s-icon-succ>
                        <s-icon-err s-else></s-icon-err>
                    </div>
                    <div slot="c-endpoint">
                        <ellipsis-tip alwaysTip="{{true}}" text="{{row.endpoint}}" placement="top" copy="{{true}}" />
                    </div>
                </s-table>
            </div>
        </tile-info>
    </div>`;

    static components = {
        's-append': AppLegend,
        'tile-info': TileInfo,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'instant-editor': InstantEditor,
        's-icon-succ': MultiToneSuccess,
        's-icon-err': MultiToneError,
        's-tooltip': MyTooltip,
        's-icon-eye': OutlinedEye,
        'sui-tooltip': Tooltip,
        'ellipsis-tip': EllipsisTip,
    };

    initData() {
        return {
            detail: {},
            table: {
                ...TABLE_SUI,
                datasource: [],
                columns: [
                    {
                        name: 'brokerId',
                        label: '节点ID',
                        width: 60
                    },
                    {
                        name: 'status',
                        label: '节点状态',
                        filter: {
                            options: [
                                ...AllEnum.toArray(),
                                ...ClusterNodeClientStatus.toArray()
                            ],
                            value: AllEnum.toArray()[0].value
                        },
                        render(item: NodeClientStatus) {
                            return renderStatus(ClusterNodeClientStatus.fromValue(item.status));
                        },
                        width: 60
                    },
                    {
                        name: 'publicIp',
                        label: '公网IP/VPC IP',
                        width: 60
                    },
                    {name: 'operation', label: '操作', width: 60}
                ]
            },
            protocolSource: [],
            protocolColumns: protocolColumns,
            pager: {...PAGER_SUI},
            editName: this.editName.bind(this),
            check: this.check.bind(this),
            KafkaZookeeperPasswordWhitelist: false
        };
    }

    static computed: SanComputedProps = {
        infoList() {
            const detail = this.data.get('detail');
            const isMultipleDisks = this.data.get('isMultipleDisks');
            const controllerOfBrokerNodes = this.data.get('controller');
            const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
            const {
                clusterId,
                clusterSid,
                config = {
                    configId: '',
                    configName: '',
                    configDescription: '',
                    revisionId: '',
                    revisionDescription: ''
                },
                name,
                createTime,
                region,
                runningTime,
                version,
                payment,
                vpc,
                subnets,
                publicIpEnabled,
                publicIpBandwidth,
                publicIpMode,
                storageMeta = {storageSize: 0, numberOfDisk: 0},
                nodeSpec,
                numberOfBrokerNodes,
                numberOfBrokerNodesPerZone,
                billing,
                aclEnabled,
                autoRenew,
                expirationTime = '',
                deploySetEnabled,
                logicalZones,
                deployType,
                storagePolicyEnabled,
                storagePolicy = {
                    type: 'AUTO_DELETE',
                    autoDelete: {}
                },
                remoteStorageEnabled,
                zookeeperEnabled,
                jmxEndpoint,
                intranetIpEnabled,
                accessControls = []
            } = detail;

            let billingUrl = '';
            if (billing) {
                const {instanceId, shortId, productType, subAccountId} = billing;
                const time = `startTime=${formatUtcTime(moment().subtract(1, 'months'))}&endTime=${formatUtcTime(moment())}&region=${region}`;
                const suffex = `instanceId=${instanceId}&shortId=${shortId}&serviceType=${SERVICE_TYPE}&productType=${productType}&subAccountId=${subAccountId}&${time}`;
                billingUrl = `/billing/#/account/resource/detail~${suffex}`;
            }

            const BaseInfo = {
                title: '集群概要',
                type: 'baseinfo',
                list: [
                    renderItem(TEXT_MAP.clusterId, 'clusterId', UtilHandler.clusterId(clusterId)),
                    renderItem(TEXT_MAP.name, 'name', UtilHandler.name(name)),
                    renderItem(TEXT_MAP.createTime, 'createTime', UtilHandler.createTime(createTime)),
                    renderItem(TEXT_MAP.version, 'version', UtilHandler.version(version)),
                    renderItem(TEXT_MAP.runningTime, 'runningTime', UtilHandler.runningTime(runningTime)),
                    renderItem(TEXT_MAP.region, 'region', UtilHandler.region(region)),
                    renderItem(TEXT_MAP.deployType, 'deployType', UtilHandler.deployType(deployType)),
                    renderItem(TEXT_MAP.deploySetEnabled, 'deploySetEnabled', UtilHandler.deploySetEnabled(deploySetEnabled)),
                    renderItem(TEXT_MAP.logicalZones, 'logicalZones', UtilHandler.logicalZones(logicalZones))
                ]
            };

            const PayInfo = {
                title: '付费信息',
                type: 'payinfo',
                list: [
                    renderItem(TEXT_MAP.payment, 'payment', Payments.getTextFromValue(payment)),
                    renderItem(TEXT_MAP.vipTime, 'expirationTime', formatTime(expirationTime)),
                ]
            };

            if (!isXushang) {
                PayInfo.list.splice(1, 0, renderItem(TEXT_MAP.clusterSid, 'clusterSid', {clusterSid, billingUrl}));
            }

            autoRenew ? PayInfo.list.push(renderItem(TEXT_MAP.autoRenew, 'autoRenew', renderSwitch(autoRenew.renew))) : '';

            if (autoRenew && autoRenew.renew) {
                PayInfo.list = PayInfo.list.concat([
                    renderItem(TEXT_MAP.autoRenewTime, 'autoRenew.renewTime', getPayLoop(autoRenew.renewTime, autoRenew.renewTimeUnit))
                ]);
            }

            const Config = {
                title: '集群配置',
                type: 'config',
                list: [
                    renderItem(TEXT_MAP.config, 'configId', UtilHandler.configId(config?.configId)),
                ]
            };

            if (config?.configId) {
                Config.list = Config.list.concat([
                    renderItem(TEXT_MAP.configName, 'configName', config?.configName),
                    renderItem(TEXT_MAP.configVersion, 'revisionId', config?.revisionId),
                ]);
            }

            const NetWork = {
                title: '网络安全',
                type: 'netWork',
                list: [
                    renderItem(TEXT_MAP.vpc, 'vpc', UtilHandler.vpc(vpc)),
                    renderItem(TEXT_MAP.subnets, 'subnets', UtilHandler.subnets(subnets)),
                    renderItem(TEXT_MAP.securityGroup, 'securityGroups', ''),
                    renderItem(TEXT_MAP.publicIpBandwidth, 'publicIpBandwidth', UtilHandler.publicIpBandwidth(publicIpBandwidth, publicIpMode), publicIpEnabled),
                    renderItem(TEXT_MAP.intranetIpEnabled, 'intranetIpEnabled', UtilHandler.intranetIpEnabled(intranetIpEnabled))
                ]
            };

            const Access = {
                title: '访问配置',
                type: 'accessConfig',
                list: [
                    renderItem(TEXT_MAP.aclEnabled, 'aclEnabled', UtilHandler.aclEnabled(aclEnabled)),
                ]
            };

            if (_.findIndex(accessControls, i => i.encryptionInTransit) !== -1) {
                Access.list.push(renderItem(TEXT_MAP.ssl_certificate, 'ssl_certificate'));
            }

            if (zookeeperEnabled) {
                Access.list.push(renderItem(TEXT_MAP.zookeeperPw, 'zookeeperPw'));
            }
            if (jmxEndpoint) {
                Access.list.push(renderItem(TEXT_MAP.KafkaJMX, 'KafkaJMX', jmxEndpoint));
            }


            const Storage = {
                title: '存储信息',
                type: 'storage',
                list: [
                    renderItem(TEXT_MAP.storageType, 'storageType', UtilHandler.storageType(storageMeta.storageType)),
                    renderItem(TEXT_MAP.storageSize, 'storageSize', UtilHandler.storageSize(storageMeta, isMultipleDisks)),
                    renderItem(TEXT_MAP.totalSize, 'totalSize', UtilHandler.totalSize(storageMeta, numberOfBrokerNodes)),
                    renderItem(TEXT_MAP.storagePolicyEnabled, 'storagePolicyEnabled', UtilHandler.storagePolicyEnabled(storagePolicyEnabled)),
                ]
            };

            if (storagePolicyEnabled) {
                Storage.list = Storage.list.concat([
                    renderItem(TEXT_MAP.storagePolicy, 'storagePolicy', UtilHandler.storagePolicy(storagePolicy)),
                ]);
            }
            if (!isInvalid(remoteStorageEnabled)) {
                Storage.list = Storage.list.concat([
                    renderItem(TEXT_MAP.remoteStorageEnabled, 'remoteStorageEnabled', renderSwitch(remoteStorageEnabled))
                ]);
            }

            const Client = {
                title: '节点详情',
                type: 'client',
                list: [
                    renderItem(TEXT_MAP.nodeSpec, 'nodeSpec', nodeSpec),
                    renderItem(
                        isEdgeRegion ? '单区节点数：' : TEXT_MAP.numberOfBrokerNodesPerZone,
                        'numberOfBrokerNodesPerZone',
                        UtilHandler.numberOfBrokerNodesPerZone(numberOfBrokerNodesPerZone)),
                    renderItem(
                        TEXT_MAP.numberOfBrokerNodes,
                        'numberOfBrokerNodes',
                        UtilHandler.numberOfBrokerNodes(numberOfBrokerNodes)),
                    renderItem(
                        TEXT_MAP.controllerOfBrokerNodes,
                        'controllerOfBrokerNodes',
                        UtilHandler.controllerOfBrokerNodes(controllerOfBrokerNodes)),
                ]
            };

            return [
                BaseInfo,
                PayInfo,
                Config,
                Access,
                NetWork,
                Storage,
                Client
            ];
        },
        disableRestart() {
            const detail = this.data.get('detail');
            return FORBID_HANDLER.disableRestart(detail.status);
        },
        canUpgrade() {
            const detail = this.data.get('detail');
            return !FORBID_HANDLER.checkClusterUpgrage(detail.status);
        },
        showStart() {
            const detail = this.data.get('detail');
            return LIST_OPERATION_SETTING.showStart(detail.status);
        },
        renewLink() {
            const isOneCloud = isOneCloudId();
            const isDev = /qasandbox/.test(location.hostname);
            return !isOneCloud ? '/billing/#/renew/list' : isDev
                ? 'http://console.cloud-sandbox.baidu-int.com/finance/renewal/list?type=BAIDU' : 'https://console.cloud.baidu-int.com/finance/renewal/list?type=BAIDU';
        },
    };

    static filters = {
        formatEmpty,
        filterMs: UtilHandler.formatTimeInfo,
        filterBytes: UtilHandler.formatBytes,
        securityGroupHref(item: object) {
            const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
            const detail = this.data.get('detail');
            return isEdgeRegion
                ? `/bec/#/bec/security-group/detail?id=${item.securityGroupUuid}`
                : `/network/#/vpc/security/detail?vpcId=${detail.vpc.vpcUuid}&id=${item.securityGroupUuid}`;
        },
    };

    async attached() {
        this.getController();
        this.getComList();
    }

    onShowController(event: Event) {
        event.stopPropagation();
        const dialog = new ControllerDialog({
            data: {
                clusterId: this.data.get('clusterId')
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    onShowZookeeperPw(event: Event) {
        event.stopPropagation();
        const {clusterId, KafkaZookeeperPasswordWhitelist, KafkaZookeeperEndpointWhitelist} = this.data.get('');
        const dialog = new ZookeeperPw({
            data: {
                clusterId,
                KafkaZookeeperPasswordWhitelist,
                KafkaZookeeperEndpointWhitelist
            }
        });
        dialog.attach(document.body);
    }

    // 获取控制节点
    getController() {
        api.getControllerBroker(this.data.get('clusterId'), {}).then((result: {timestamp: String, brokerId: Number}) => {
            this.data.set('controller', result.brokerId);
        });
    }

    /**
     * 获取表格 list
     */
    async getComList(loadStatus = true) {
        this.data.set('table.loading', loadStatus);
        this.data.set('table.error', false);
        // 清除多选
        this.data.set('selection.selectedIndex', []);
        try {
            await this.getTableList();
            this.data.set('table.loading', false);
        }
        catch (error) {
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    getTableList() {
        const {pager, status} = this.data.get('');
        return api.getNodeList(this.data.get('clusterId'), pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize,
            status
        })).then((target: {totalCount: string, result: Array<NodeClientStatus>}) => {
            this.data.set('pager.count', target.totalCount);
            this.data.set('table.datasource', target.result);
        });
    }

    // 变更
    onUpgrade() {
        const detail = this.data.get('detail');
        redirect(`#${ROUTE_PATH.clusterUpgrage}?clusterId=${detail.clusterId}`);
    }

    // 重启
    onReloadNode(row: NodeClientStatus, rowIndex: string) {
        Dialog.warning({
            okText: '确定',
            content: RestartNode,
            onOk: () => {
                api.restartClusterNode(this.data.get('clusterId'), row.nodeId, {})
                    .then(() => {
                        Notification.success('重启成功');
                        this.fire('refresh-page', {});
                        setTimeout(() => {this.fire('refresh-page', {})}, 5000);
                    });
            }
        });
    }

    // 跳转节点日志页面
    viewLog(row: NodeClientStatus, rowIndex: string) {
        this.fire('active-log-tab', {nodeId: row.nodeId});
    }

    refreshInfo(loadStatus = true) {
        this.getController();
        return this.getComList(loadStatus);
    }

    // 名称输入校验
    check(name: string, callback: (str: string) => void) {
        if (!name) {
            return callback('请输入');
        }
        if (name.length > 65) {
            return callback('不能超过65个字符');
        }
        if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(name)) {
            return callback('输入字符格式有误');
        }
        callback('');
    }

    // 名称编辑
    async editName(name: string) {
        const clusterId = this.data.get('clusterId');
        await api.updateCluster(clusterId, {
            name,
            type: UpgrageType.UPDATE_CLUSTER_NAME
        });
        Notification.success('修改成功');
        this.data.set('detail.name', name);
        redirect(`#${ROUTE_PATH.clusterDetailInfo}?name=${name}&clusterId=${clusterId}`);
    }

    @throttle(5000)
    async onStart() {
        const detail = this.data.get('detail');
        Dialog.warning({
            content: defineComponent({
                template: html`
                <div>
                    <div>集群：${detail.name}</div>
                    <div>您确定启动此集群吗？</div>
                </div>`
            }),
            okText: '确定',
            onOk: async () => {
                await api.startCluster(this.data.get('clusterId'), {});
                this.fire('refresh-page', {});
            }
        });
    }

    @throttle(5000)
    async onStop() {
        const detail = this.data.get('detail');
        Dialog.warning({
            content: defineComponent({
                template: html`
                <div>
                    <div>集群：${detail.name}</div>
                    <div>您确定停止此集群吗？</div>
                </div>`
            }),
            okText: '确定',
            onOk: async () => {
                await api.stopCluster(this.data.get('clusterId'), {});
                this.fire('refresh-page', {});
            }
        });
    }

    @throttle(5000)
    async onRestart() {
        const detail = this.data.get('detail');
        Dialog.warning({
            content: defineComponent({
                template: html`
                <div>
                    <div>集群：${detail.name}</div>
                    <div>您确定重启此集群吗？</div>
                </div>`
            }),
            okText: '确定',
            onOk: async () => {
                await api.restartCluster(this.data.get('clusterId'), {});
                this.fire('refresh-page', {});
            }
        });
    }

    @throttle(1000)
    onConfigView() {
        event.stopPropagation();
        const {configId, revisionId} = this.data.get('detail.config') || {
            configId: '',
            revisionId: ''
        };
        const dialog = new ParamDialog({
            data: {
                clusterId: this.data.get('clusterId'),
                configId,
                revisionId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    // 下载证书文件
    @throttle(1000)
    async downloadSSL() {
        const clusterId = this.data.get('clusterId');
        const FILENAME = 'kafka-key.zip';
        let xhr = new XMLHttpRequest();
        let saveBlob = this.saveBlob;
        xhr.open('GET', `${suffexV3Cluster}/${clusterId}/cert`);
        xhr.setRequestHeader('Content-Type', 'application/octet-stream');
        xhr.setRequestHeader('X-Region', window.$context.getCurrentRegion().id);
        xhr.setRequestHeader('csrfToken', this.$cookie.get('bce-user-info'));
        xhr.responseType = 'blob';
        xhr.onload = function (e: Event) {
            if (this.status === 200) {
                saveBlob(this.response, FILENAME);
            } else {
                Notification.error(`下载报错：${JSON.stringify(e)}`);
            }
        };
        xhr.onerror = function (e) {
            Notification.error(`下载报错：${JSON.stringify(e)}`);
        };
        xhr.send(JSON.stringify({}));
    }

    // transParam
    saveBlob(blob, fileName: string) {
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(blob);
        a.download = fileName;
        a.dispatchEvent(new MouseEvent('click'));
    }
};
