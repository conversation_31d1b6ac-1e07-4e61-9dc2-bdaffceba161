/**
 * 配置参数列表展示
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Table, Button, Pagination, Search} from '@baidu/sui';
import {formatEmpty, pickEmpty} from '@/common/util';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {PAGER_SUI} from '@/common/config';
import './index.less';
import api from '@/common/client';

const klass = 'controller-dialog';
export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="配置详情"
            class="formate-drawer"
            open="{{open}}"
            maskClose="{{true}}"
            otherClose="{{true}}"
            on-close="onClose"
            getContainer="{{getContainer}}"
            size="{{800}}"
        >
            <span class="title mb24">参数信息</span>
            <s-table
                class="${klass}"
                loading="{{loading}}"
                columns="{{columns}}"
                datasource="{{params}}"
            >
                <div slot="c-description">{{row.description | formatEmpty}}</div>
            </s-table>
            <s-pagination
                class="controller-dialog__pager"
                layout="total, pageSize, pager, go"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{5}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            >
            </s-pagination>
            <div class="${klass}__bottom">
                <s-button
                    skin="primary"
                    class="ml12"
                    width="46"
                    height="30"
                    on-click="onClose">
                    关闭
                </s-button>
            </div>
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        's-search': Search,
        's-refresh': OutlinedRefresh,
    };

    initData() {
        return {
            open: true,
            getContainer: document.getElementById('main'),
            columns: [
                {
                    name: 'name',
                    label: '参数名称',
                },
                {
                    name: 'description',
                    label: '描述',
                },
                {
                    name: 'scope',
                    label: '参数范围',
                },
                {
                    name: 'defaultValue',
                    label: '默认值',
                    width: 86
                },
                {
                    name: 'currentValue',
                    label: '当前值',
                    width: 86
                }
            ],
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        formatEmpty
    };

    async attached() {
        await this.getParamList();
    }

    async getParamList() {
        this.data.set('loading', true);
        const {clusterId, configId, revisionId} = this.data.get('');
        const param = pickEmpty({
            configId,
            revisionId
        });
        await api.getClusterConfigDetail(clusterId, param)
            .then((result: object[]) => {
                this.data.set('paramList', result);
                this.data.set('initialList', result);
                this.data.set('params', result.slice(0, 10));
                this.data.set('pager.count', result.length);
                this.data.set('loading', false);
            });
    }

    onChange(e: {value: string}) {
        const list = this.data.get('initialList');
        if (e.value !== '') {
            this.data.set('paramList', list);
            this.data.set('params', list.slice(0, 10));
            this.data.set('pager.count', list.length);
            this.onPageChange({value: {page: 1, pageSize: 10}});
        }
        else {
            this.refresh();
        }
    }

    refresh() {
        this.getParamList();
    }

    onPageChange(event: {value: {page: number, pageSize: number}}) {
        const {page, pageSize} = event.value;
        const {paramList} = this.data.get('');
        this.data.set('params', paramList.slice((page - 1) * pageSize, page * pageSize));
    }

    onPageSizeChange(event: {value: {page: number, pageSize: number}}) {
        const {pageSize, page} = event.value;
        const {paramList} = this.data.get('');
        const newPage = Math.min(Math.ceil(paramList.length / pageSize), page);
        this.data.set('params', paramList.slice((newPage - 1) * pageSize, newPage * pageSize));
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
