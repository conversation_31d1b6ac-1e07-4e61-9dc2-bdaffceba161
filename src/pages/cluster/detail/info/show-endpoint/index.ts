/**
 * 接入点
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Notification} from '@baidu/sui';
import {ClipBoard} from '@baidu/sui-biz';
import {suffexV3Cluster} from '@/common/client';
import {throttle} from '@/common/decorator';

import './index.less';

const klass = 'show-endpoint';

export default class extends Component {
    static template = html`
    <template>
        <s-dialog
            title="接入点信息"
            okText="确定"
            open="{{open}}"
            width="500"
            class="${klass}"
            on-close="onClose">
            <s-button slot="footer" on-click="onConfirm" skin="primary">确定</s-button>
            <div class="mb20 ${klass}__title">集群名称</div>
            <div class="mb20">
                {{clusterName}}
            </div>
            <div class="mb20 ${klass}__title">Bootstrap 服务器</div>
            <div class="mb20">
                用于建立与Kafka集群初始连接的主机/端口对的列表。在Kafka生产者或消费者配置中用此属性。
            </div>
            <div class="mb20">
                <template s-if="sslEnabled">
                    SSL/SASL_SSL方式请下载证书文件：
                    <a
                        class="${klass}__certification"
                        on-click="downloadSSL"
                        href="javascript:void(0);"
                    >
                        kafka-key.zip
                    </a>
                </template>
            </div>
            <template s-for="item,index in kafkaEndpoints">
                <div class="${klass}__protocol">{{item.securityProtocol}}</div>
                <div class="${klass}__endpoints {{index === kafkaEndpoints.length - 1 ? 'at-last' : ''}}">
                    <s-clip-board class="clipboard-default" text="{{item.endpoints}}" />
                    <span class="${klass}__endpoints_text">{{item.endpoints}}</span>
                </div>
            </template>
            <template s-if="{{zookeepeerEndpoints}}">
                <div class="mt20 mb20 ${klass}__title">Zookeeper 连接</div>
                <div class="${klass}__protocol" s-if="{{zookeepeerEndpoints.securityProtocol}}">
                    {{zookeepeerEndpoints.securityProtocol}}
                </div>
                <div class="${klass}__endpoints at-last" s-if="{{zookeepeerEndpoints.endpoints}}">
                    <s-clip-board class="clipboard-default" text="{{zookeepeerEndpoints.endpoints}}" />
                    <span class="${klass}__endpoints_text">{{zookeepeerEndpoints.endpoints}}</span>
                </div>
            </template>
            <template s-if="{{jmxEndpoints}}">
                <div class="mt20 mb20 ${klass}__title"> Kafka JMX 地址</div>
                <div class="${klass}__protocol" s-if="{{jmxEndpoints.securityProtocol}}">
                    {{jmxEndpoints.securityProtocol}}
                </div>
                <div class="${klass}__endpoints at-last" s-if="{{jmxEndpoints.endpoints}}">
                    <s-clip-board class="clipboard-default" text="{{jmxEndpoints.endpoints}}" />
                    <span class="${klass}__endpoints_text">{{jmxEndpoints.endpoints}}</span>
                </div>
            </template>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-clip-board': ClipBoard
    };

    initData() {
        return {
            open: true,
        };
    }

    // 确认
    async onConfirm() {
        this.onClose();
    }

    // 下载证书文件
    @throttle(1000)
    async downloadSSL() {
        const clusterId = this.data.get('clusterId');
        const FILENAME = 'kafka-key.zip';
        let xhr = new XMLHttpRequest();
        let saveBlob = this.saveBlob;
        xhr.open('GET', `${suffexV3Cluster}/${clusterId}/cert`);
        xhr.setRequestHeader('Content-Type', 'application/octet-stream');
        xhr.setRequestHeader('X-Region', window.$context.getCurrentRegion().id);
        xhr.setRequestHeader('csrfToken', this.$cookie.get('bce-user-info'));
        xhr.responseType = 'blob';
        xhr.onload = function (e: Event) {
            if (this.status === 200) {
                saveBlob(this.response, FILENAME);
            } else {
                Notification.error(`下载报错：${JSON.stringify(e)}`);
            }
        };
        xhr.onerror = function (e) {
            Notification.error(`下载报错：${JSON.stringify(e)}`);
        };
        xhr.send(JSON.stringify({}));
    }

    // transParam
    saveBlob(blob, fileName: string) {
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(blob);
        a.download = fileName;
        a.dispatchEvent(new MouseEvent('click'));
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
