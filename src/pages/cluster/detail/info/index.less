/**
 * @file index.less
 * <AUTHOR>
 */
@klass: bms-cluster-detail-info;
@borderColor: #E8E9EB;
@height: 120px;

.@{klass} {
    position: relative;

    .client-wrap {
        padding-bottom: 20px;

        &__pager {
            margin-top: 10px;
            height: 32px;

            .s-pagination-wrapper {
                float: right;
            }
        }
    }

    .subnets-item {
        display: flex;

        &__text {
            margin-left: 3px;
        }
    }

    &__clusterName {
        display: inline-flex;
        width: 100%;
        overflow: hidden;

        > label {
            flex: 0 0 64px;
        }

        > span {
            flex: 1;
            margin-top: -2px;
            padding-right: 5px;
            overflow: hidden;

            .instance-editor {
                display: inline-block;
            }

            .ellipsis-tip {
                display: inline-block;
                width: auto;
                max-width: calc(~"100% - 30px");
            }
        }
    }

    .detail-operation-btn {
        position: absolute;
        top: 10px;
        right: 30px;
    }

    .box-label {
        color: #5c5f66;
    }
    .box-text {
        color: #151b26;
    }

    .ellipsis-tip__text {
        margin-bottom: -6px;
    }
}

.formate-drawer {
    .s-drawer-content {
        border-bottom: 1px solid @borderColor;
        height: calc(~"100% - @{height}")!important;

        .title {
            font-size: 14px;
            color: #151B26;
            font-weight: 500;
            display: inline-block;
        }

        .operation-area {
            width: 100%;
            display: flex;
            justify-content: right;
            align-items: center;
            padding: 0px 20px;
            border: none;
            margin-bottom: 20px;
        }
    }
    .controller-dialog {
        &__bottom {
            position: absolute;
            right: 40px;
            bottom: 20px;
        }

        &__pager {
            margin-top: 10px;
            height: 32px;

            .s-pagination-wrapper {
                float: right;
            }
        }
    }
}
