/**
 * Zookeeper密码
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {ClipBoard} from '@baidu/sui-biz';
import {Dialog, Form, Notification, Button} from '@baidu/sui';
import api from '@/common/client';
import {copyDashboard} from '@/common/util';

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            title="ZooKeeper信息"
            open="{{open}}"
            on-close="onClose">
            <s-button slot="footer" on-click="onClose" skin="primary">确定</s-button>
            <s-form class="form-format">
                <s-form-item
                    label="用户："
                    class="form-item-center">
                    {{username}}
                    <s-clip-board text="{{username}}" />
                </s-form-item>
                <s-form-item
                    label="密码："
                    class="form-item-center">
                    {{password}}
                    <s-clip-board text="{{password}}" />
                </s-form-item>
                <s-form-item
                    label="接入点："
                    class="form-item-center">
                    <p>{{zookeeperEndpoint.securityProtocol}}</p>
                    {{zookeeperEndpoint.endpoints}}
                    <s-clip-board text="{{zookeeperEndpoint.endpoints}}" />
                </s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-clip-board': ClipBoard
    };

    initData() {
        return {
            open: false,
            username: '',
            password: '',
            zookeeperEndpoint: {}
        };
    }

    attached() {
        api.getZkPasswd(this.data.get('clusterId'), {})
            .then(({username, password, zookeeperEndpoint}) => {
                this.data.set('open', true);
                this.data.set('username', username);
                this.data.set('password', password);
                this.data.set('zookeeperEndpoint', zookeeperEndpoint);
            });
    }

    // 确认
    async onConfirm() {
        copyDashboard(this.data.get('passwd'));
        Notification.success('复制成功');
        this.onClose();
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
