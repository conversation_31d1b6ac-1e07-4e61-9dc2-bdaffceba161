/**
 * 控制节点列表展示
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Table, Button, Pagination, Search} from '@baidu/sui';
import {formatEmpty, formatTime} from '@/common/util';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {PAGER_SUI} from '@/common/config';
import './index.less';
import api from '@/common/client';

const klass = 'controller-dialog';
export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="管控详情"
            class="formate-drawer"
            open="{{open}}"
            maskClose="{{true}}"
            otherClose="{{true}}"
            on-close="onClose"
            getContainer="{{getContainer}}"
            size="600"
        >
            <div class="operation-area">
                <s-search
                    on-change="onChange"
                    placeholder="请输入节点ID"
                />
                <s-button class="ml5 s-icon-button" on-click="refresh">
                    <s-refresh />
                </s-button>
            </div>
            <s-table
                class="${klass}"
                loading="{{loading}}"
                columns="{{columns}}"
                datasource="{{controllers}}">
                <div slot="c-ip">
                        {{row.publicIp | formatEmpty}}/{{row.internalIp | formatEmpty}}
                    </div>
            </s-table>
            <s-pagination
                s-if="pager.count > 0"
                class="controller-dialog__pager"
                layout="total, pageSize, pager, go"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{5}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            >
            </s-pagination>
            <div class="${klass}__bottom">
                <s-button on-click="onClose">取消</s-button>
                <s-button
                    skin="primary"
                    class="ml12"
                    on-click="onClose">
                    确定
                </s-button>
            </div>
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        's-search': Search,
        's-refresh': OutlinedRefresh,
    };

    initData() {
        return {
            open: true,
            getContainer: document.getElementById('main'),
            columns: [
                {
                    name: 'timestamp',
                    label: '变化时间',
                    render: item => formatTime(item.timestamp)
                },
                {
                    name: 'brokerId',
                    label: '节点ID',
                },
                {
                    name: 'ip',
                    label: '公网IP/内网IP',
                },
            ],
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        formatEmpty
    };

    async attached() {
        await this.getControllerList();
    }

    async getControllerList() {
        this.data.set('loading', true);
        await api.getControllerHistroy(this.data.get('clusterId'), {})
            .then((target: Array<NormalObject>) => {
                let arr = _.map(target, s => ({
                    timestamp: s.timestamp,
                    brokerId: s.brokerId,
                    publicIp: s.publicIp,
                    internalIp: s.internalIp
                }));
                this.data.set('controllerList', arr);
                this.data.set('initialList', arr);
                this.data.set('controllers', arr.slice(0, 10));
                this.data.set('pager.count', arr.length);
                this.data.set('loading', false);
            });
    }

    onChange(e: {value: string}) {
        const list = this.data.get('initialList');
        if (e.value !== '') {
            const res = _.filter(list, item => item.brokerId.toString().indexOf(e.value) > -1);
            this.data.set('controllerList', res);
            this.data.set('controllers', res.slice(0, 10));
            this.data.set('pager.count', res.length);
            this.onPageChange({value: {page: 1, pageSize: 10}});
        }
        else {
            this.refresh();
        }
    }

    refresh() {
        this.getControllerList();
    }

    onPageChange(event: {value: {page: number, pageSize: number}}) {
        const {page, pageSize} = event.value;
        const {controllerList} = this.data.get('');
        this.data.set('controllers', controllerList.slice((page - 1) * pageSize, page * pageSize));
    }

    onPageSizeChange(event: {value: {page: number, pageSize: number}}) {
        const {pageSize, page} = event.value;
        const {controllerList} = this.data.get('');
        const newPage = Math.min(Math.ceil(controllerList.length / pageSize), page);
        this.data.set('controllers', controllerList.slice((newPage - 1) * pageSize, newPage * pageSize));
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
