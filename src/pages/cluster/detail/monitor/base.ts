import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Tooltip, Checkbox} from '@baidu/sui';
import TipSelect from '@/components/tip-select';
import api from '@/common/client';
import {SELECT_SEARCH_HEIGHT, SELECT_SEARCH_WIDTH} from '@/common/config';

export enum TabKeyType {
    CLUSTER = 'cluster',
    TOPIC = 'topic',
    CONSUMERGROUP = 'consumerGroup',
    BROKER = 'broker'
}

const initialSelectItem = [{value: '', label: ''}];

export default class baseFilter extends Component {
    static template = html`
        <template>
            <template s-if="{{current === '${TabKeyType.BROKER}'}}">
                节点ID：
                <tip-select
                    class="mr10"
                    multiple="{{true}}"
                    checkAll="{{true}}"
                    filterable="{{true}}"
                    labelFilter="{{labelFilter}}"
                    datasource="{{singlebrokers}}"
                    value="{= brokersNode =}"
                    height="${SELECT_SEARCH_HEIGHT}"
                    width="${SELECT_SEARCH_WIDTH}"
                    on-change="onBrokersSingleChange"
                />
            </template>
            <template s-elif="{{current === '${TabKeyType.TOPIC}'}}">
                <template s-if="{{monitorType === 'broker'}}">
                    主题：
                    <tip-select
                        class="mr10"
                        multiple="{{false}}"
                        filterable="{{true}}"
                        datasource="{{topics}}"
                        value="{= topic =}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        on-change="onTopicChange"
                    />
                </template>
                <template s-if="{{monitorType === 'basic'}}">
                    主题：
                    <tip-select
                        class="mr10"
                        multiple="{{true}}"
                        checkAll="{{true}}"
                        filterable="{{true}}"
                        labelFilter="{{labelFilter}}"
                        datasource="{{topics}}"
                        value="{= topic =}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        width="${SELECT_SEARCH_WIDTH}"
                        on-change="onTopicChange"
                    />
                </template>
                <template s-if="{{monitorType === 'broker'}}">
                    节点ID：
                    <tip-select
                        class="mr10"
                        multiple="{{true}}"
                        checkAll="{{true}}"
                        filterable="{{true}}"
                        labelFilter="{{labelFilter}}"
                        datasource="{{singlebrokers}}"
                        value="{= brokersNode =}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        width="${SELECT_SEARCH_WIDTH}"
                        on-change="onBrokersSingleChange"
                    />
                </template>
                <template s-if="{{monitorType === 'topic'}}">
                    节点ID：
                    <tip-select
                        class="mr10"
                        multiple="{{false}}"
                        filterable="{{true}}"
                        datasource="{{singlebrokers}}"
                        value="{= brokersNode =}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        on-change="onBrokersSingleChange"
                    />
                </template>
                <template s-if="{{monitorType === 'partition'}}">
                    分区ID：
                    <tip-select
                        datasource="{{partitions}}"
                        value="{= partition =}"
                        multiple="{{true}}"
                        checkAll="{{true}}"
                        filterable="{{true}}"
                        labelFilter="{{labelFilter}}"
                        width="${SELECT_SEARCH_WIDTH}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        on-change="onPartitionChange"
                    />
                </template>
                <template s-if="{{monitorType === 'topic'}}">
                    主题：
                    <tip-select
                        datasource="{{topics}}"
                        value="{= topic =}"
                        multiple="{{true}}"
                        checkAll="{{true}}"
                        filterable="{{true}}"
                        labelFilter="{{labelFilter}}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        width="${SELECT_SEARCH_WIDTH}"
                        on-change="onTopicChange"
                    />
                </template>
            </template>
            <template s-elif="{{current === '${TabKeyType.CONSUMERGROUP}'}}">
                <span s-if="{{monitorType !== 'basic'}}">
                    消费组：
                    <tip-select
                        multiple="{{false}}"
                        filterable="{{true}}"
                        datasource="{{consumers}}"
                        value="{= consumer =}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        on-change="onConsumerChange"
                    />
                </span>
                <span s-if="{{monitorType === 'basic'}}">
                    消费组：
                    <tip-select
                        multiple="{{true}}"
                        checkAll="{{true}}"
                        filterable="{{true}}"
                        labelFilter="{{labelFilter}}"
                        datasource="{{consumers}}"
                        value="{= consumer =}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        width="${SELECT_SEARCH_WIDTH}"
                        on-change="onConsumerChange"
                    />
                </span>
                <span class="ml10" s-if="{{monitorType === 'topic'}}">
                    主题：
                    <tip-select
                        s-if="{{consumerTopicFlag}}"
                        datasource="{{consumerTopics}}"
                        value="{= consumerTopic =}"
                        multiple="{{true}}"
                        checkAll="{{true}}"
                        filterable="{{true}}"
                        labelFilter="{{labelFilter}}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        width="${SELECT_SEARCH_WIDTH}"
                        on-change="consumerTopicChange"
                    />
                </span>
                <span class="ml10" s-if="{{monitorType === 'partition'}}">
                    主题：
                    <tip-select
                        s-if="{{consumerTopicFlag}}"
                        filterable="{{true}}"
                        datasource="{{consumerTopics}}"
                        value="{= consumerTopic =}"
                        multiple="{{false}}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        on-change="consumerTopicChange"
                    />
                </span>
                <span class="ml10" s-if="{{monitorType === 'partition'}}">
                    分区：
                    <tip-select
                        s-if="{{quoteFlag}}"
                        datasource="{{quotes}}"
                        value="{= quote =}"
                        height="${SELECT_SEARCH_HEIGHT}"
                        width="${SELECT_SEARCH_WIDTH}"
                        multiple="{{true}}"
                        checkAll="{{true}}"
                        filterable="{{true}}"
                        labelFilter="{{labelFilter}}"
                        on-change="onQuoteChange"
                    />
                </span>
            </template>
            <s-checkbox
                s-if="{{current !== '${TabKeyType.CLUSTER}'}}"
                label="反选"
                class="ml8"
                checked="{=checkboxValue=}"
                on-change="onSelectReverse"
            />
        </template>
    `;

    initData() {
        return {
            consumers: initialSelectItem,
            consumerTopics: initialSelectItem,
            quotes: initialSelectItem,
            topics: initialSelectItem,
            labelFilter(labels: string[]) {
                return labels.join(',');
            },
            checkboxValue: false
        };
    }

    static components = {
        's-button': Button,
        'tip-select': TipSelect,
        's-tooltip': Tooltip,
        's-checkbox': Checkbox
    };

    async attached() {
        const {current, location, broker, topic, consumer, consumerTopic, quote, topicBroker} = this.data.get('');
        switch (current) {
            case TabKeyType.CLUSTER:
                break;
            case TabKeyType.BROKER:
                await this.getBrokers();
                break;
            case TabKeyType.TOPIC:
                await this.getTopics();
                await this.getBrokers();
                break;
            case TabKeyType.CONSUMERGROUP:
                await this.getConsumer();
                break;
        }
        if (location === 'slot') {
            if (current === TabKeyType.BROKER) {
                this.data.set('brokersNode', broker.map(i => i.brokerId));
            }
            else {
                this.data.set('brokersNode', topicBroker);
            }
            this.data.set('topic', topic);
            this.data.set('consumer', consumer);
            consumer && await this.setConsumerTopicsInfo(consumer);
            this.data.set('consumerTopicFlag', true);
            this.data.set('quoteFlag', true);
            this.data.set('consumerTopic', consumerTopic);
            this.data.set('quote', quote);
        }
        else if (topic) {
            this.data.set('topic', [topic]);
            this.onTopicChange({value: [topic]});
        }
        this.watch('broker', v => {
            this.data.set('brokersNode', v.map(i => i.brokerId));
        });
        this.watch('topicBroker', v => {
            this.data.set('brokersNode', v);
        });
    }

    async getController() {
        await api.getControllerBroker(this.data.get('clusterId'), {}).then((result: {timestamp: String, brokerId: Number}) => {
            this.data.set('controller', result.brokerId);
        });
    }

    handleMonitorTypeChange(monitorType: string) {
        const {current, temBrokers = [], location, idx} = this.data.get('');
        switch (current) {
            case TabKeyType.BROKER:
                const value = temBrokers.map(i => i.brokerId);
                this.data.set('brokersNode', value);
                this.dispatch('UI:broker-change', {value: temBrokers, instances: temBrokers, monitorType, location, idx});
                break;
            case TabKeyType.TOPIC:
                this.data.set('brokersNode', '');
                this.data.set('topic', '');
                this.data.set('partition', '');
                break;
            case TabKeyType.CONSUMERGROUP:
                this.data.set('consumer', '');
                this.data.set('consumerTopic', '');
                this.data.set('quote', '');
                break;
            default:
                break;
        };
    }

    // 获取节点
    async getBrokers() {
        await this.getController();
        return api.listClusterBrokers(this.data.get('clusterId'), {})
            .then((target: Array<{brokerId: number, instanceId: string}>) => {
                const location = this.data.get('location');
                const arr =  _.map(target, s => ({
                    text: this.data.get('controller') == s.brokerId ? s.brokerId + '(Controller)' : s.brokerId,
                    value: s.brokerId,
                    alias: s,
                    nodeId: s.instanceId
                }));
                this.data.set('temBrokers', target);
                this.data.set('singlebrokers', arr);
                // @ts-ignore
                if (location === 'page') {
                    this.setBroker(arr, target);
                }
            });
    }

    // 设置节点
    setBroker(arr: Array<{text: number, alias: string, value: string | number}>, target: Array<{brokerId: number, instanceId: string}>) {
        // improvement：这里可以做下删除了选中的brokersNode后默认值第一个处理
        const {temBrokers, location, idx, monitorType} = this.data.get('');
        this.dispatch('UI:broker-change', {value: temBrokers || target, instances: target, monitorType, location, idx});
    }

    // 获取主题
    getTopics() {
        return api.listClusterAllTopics(this.data.get('clusterId'), {})
            .then((target: string[]) => {
                // 默认全部是一个数组
                const arr = _.map(target, s => ({text: s, value: s}));
                this.data.set('topics', arr);
            });
    }

    // 获取消费组
    getConsumer() {
        return api.listClusterAllConsumerGroups(this.data.get('clusterId'), {})
            .then((target: string[]) => {
                this.data.set('tempConsumers', target);
                this.data.set('consumers', _.map(target, i => ({text: i, value: i})));
            });
    }

    // 节点切换
    onBrokersSingleChange(target: {value: string}) {
        const {temBrokers, current, monitorType, location, idx} = this.data.get('');
        if (current === TabKeyType.BROKER) {
            const value = _.filter(temBrokers, i => _.includes(target.value, i.brokerId));
            this.dispatch('UI:broker-change', {value, instances: temBrokers, monitorType, location, idx});
        } else {
            const value = target.value;
            const topic = this.data.get('topic');
            this.dispatch('UI:topic-change', {broker: value, topic, monitorType, type: 'broker', location, idx});
        }
    }

    // 主题切换
    onTopicChange(target: {value: string}) {
        const {monitorType, brokersNode: broker, location, idx} = this.data.get('');
        this.dispatch('UI:topic-change', {broker, topic: target.value, monitorType, type: 'topic', location, idx});
    }

    // 消费组
    onConsumerChange(target: {value: string}) {
        this.data.set('consumer', target.value);
        const {consumer, monitorType, location, idx} = this.data.get('');
        if (monitorType === 'basic') {
            this.dispatch('UI:quote-change', {consumer, monitorType, location, idx});
        } else {
            this.setConsumerTopicsInfo(target.value);
        }
    }

    // 获取消费主题&分区信息
    setConsumerTopicsInfo(consumer: string) {
        api.listClusterConsumerGroupsTopics(this.data.get('clusterId'), consumer, {})
            .then((target: ConsumerTopics) => {
                this.data.set('consumerTopicsInfo', target);
                const consumerTopics = _.map(target ?? [], item => ({text: item.topicName, value: item.topicName}));
                this.data.set('consumerTopics', consumerTopics);
                const quotes = _.map(target[0]?.partitions ?? [], item => ({text: item, value: item}));
                this.data.set('quotes', quotes);
                this.data.set('consumerTopicFlag', false);
                this.data.set('quoteFlag', false);
                this.nextTick(() => {
                    this.data.set('consumerTopicFlag', true);
                    this.data.set('quoteFlag', true);
                });
            });
    }

    // 消费组主题改变
    consumerTopicChange(target: {value: string}) {
        this.data.set('consumerTopic', target.value);
        const {consumer, consumerTopic, monitorType, location, idx} = this.data.get('');
        if (this.data.get('monitorType') === 'partition') {
            const consumerTopicsInfo: ConsumerTopics = this.data.get('consumerTopicsInfo');
            const item = _.find(consumerTopicsInfo, item => item.topicName === target.value)
            const quotes = _.map(item?.partitions || [], item => ({text: item, value: item}));
            this.data.set('quotes', quotes);
            this.data.set('quote', quotes[0]?.value ?? '');
            this.data.set('quoteFlag', false);
            this.nextTick(() => {
                this.data.set('quoteFlag', true);
            });
            this.fireConsumer();
        } else {
            this.dispatch('UI:quote-change', {consumer, consumerTopic, monitorType, location, idx});
        }
    }

    // 分区切换
    onQuoteChange(target: {value: number}) {
        this.data.set('quote', target.value);
        this.fireConsumer();
    }

    fireConsumer() {
        let {consumer, consumerTopic, quote, monitorType, location, idx} = this.data.get('');
        if (['', undefined].includes(consumerTopic)
            || ['', undefined].includes(consumerTopic)
            || ['', undefined].includes(quote)) {
            return;
        }
        this.dispatch('UI:quote-change', {consumer, consumerTopic, quote, monitorType, location, idx});
    }

    onSelectReverse(target: {value: string}) {
        const {current, monitorType} = this.data.get('');
        switch (current) {
            case TabKeyType.BROKER:
                this.reverseBroker();
                break;
            case TabKeyType.TOPIC:
                monitorType === 'broker' ? this.reverseBroker() : this.reverseTopic();
                break;
            case TabKeyType.CONSUMERGROUP:
                if (monitorType === 'basic') {
                    this.reverseConsumer();
                } else if (monitorType === 'topic') {
                    this.reverseConsumerTopic();
                } else {
                    this.reversePartition();
                }
                break;
            default:
                break;
        }
    }

    getReverseData(value: string, arr: Array<NormalObject>) {
        const res = _.filter(arr, item => !_.includes(value, item.value));
        return res.map(item => item.value);
    }

    reverseTopic() {
        const {topic, topics} = this.data.get('');
        const reversTopics = this.getReverseData(topic, topics);
        this.data.set('topic', reversTopics);
        this.onTopicChange({value: reversTopics});
    }

    reverseBroker() {
        const {singlebrokers, brokersNode} = this.data.get('');
        const reversBrokers = this.getReverseData(brokersNode, singlebrokers);
        this.data.set('brokersNode', reversBrokers);
        this.onBrokersSingleChange({value: reversBrokers});
    }

    reverseConsumer() {
        const {consumers, consumer} = this.data.get('');
        const reversConsumers = this.getReverseData(consumer, consumers);
        this.data.set('consumer', reversConsumers);
        this.onConsumerChange({value: reversConsumers});
    }

    reversePartition() {
        const {quotes, quote} = this.data.get('');
        const reversQuotes = this.getReverseData(quote, quotes);
        this.data.set('quote', reversQuotes);
        this.onQuoteChange({value: reversQuotes});
    }

    reverseConsumerTopic() {
        const {consumerTopic, consumerTopics} = this.data.get('');
        const reversTopics = this.getReverseData(consumerTopic, consumerTopics);
        this.data.set('consumerTopic', reversTopics);
        this.consumerTopicChange({value: reversTopics});
    }
}
