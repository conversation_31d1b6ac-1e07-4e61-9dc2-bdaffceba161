/**
 * 集群监控
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import m from 'moment';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppListPage} from '@baidu/sui-biz';
import {Button, Tabs, Select, DatePicker, Tooltip, Radio, Popover} from '@baidu/sui';
import {OutlinedSetting, OutlinedRefresh} from '@baidu/sui-icon';

import Chart from '@/components/monitor/chart';
import {brokerByhostTypeList} from '../../components/monitor-filter/config';
import MonitorFilter from '../../components/monitor-filter';
import baseFilter, {TabKeyType} from './base';

import {formatUtcTime} from '@/common/util';
import {SELECT_SEARCH_HEIGHT, CLUSTER_SCOPE} from '@/common/config';
import api, {bjClient} from '@/common/client';
import {TIME_LIST} from '@/common/enums';
import {TIMERANGE_LIST} from '@/common/enums/constant';
import myStore, {connectMyStore} from '@/store/index';

import './index.less';

const klass = 'bms-cluster-detail-monitor';

const TabsList = [
    {text: '集群', key: TabKeyType.CLUSTER, label: '集群监控'},
    {text: '节点', key: TabKeyType.BROKER, label: '节点监控'},
    {text: '主题', key: TabKeyType.TOPIC, label: '主题监控'},
    {text: '消费组', key: TabKeyType.CONSUMERGROUP, label: '消费组监控'}
];

const AdjustMetricsArr = [
    'WebInBytes',
    'WebOutBytes',
    'WebInBitsPerSecond',
    'WebOutBitsPerSecond',
    'WebInPPS',
    'WebOutPPS'
];

const AddFaceIDType = [
    'NetReceiveBytesPerSec',
    'NetTransmitBytesPerSec',
    'NetReceivePacketsPerSec',
    'NetTransmitPacketsPerSec',
    'NetReceiveErrorsPerSec',
    'NetTransmitErrorsPerSec',
    'NetReceiveDroppedPerSec',
    'NetTransmitDroppedPerSec',
    'NetReceiveBandwidthUsedPercent',
    'NetTransmitBandwidthUsedPercent'
];

const AddDiskIDType = [
    'DiskIOUsedPercent',
    'DiskReadBytesPerSec',
    'DiskWriteBytesPerSec',
    'DiskReadIOPerSec',
    'DiskWriteIOPerSec',
    'DiskReadAwaitMs',
    'DiskWriteAwaitMs',
    'DiskUsedPercent',
    'DiskTotalBytes',
    'DiskFreeBytes',
    'DiskUsedBytes'
];

const TIME_FILTER = TIME_LIST.toArray();

const RECENT = TIME_FILTER[0].value;

const brokerMonitorTypes = [
    {label: '服务监控', value: 'service'},
    {label: '主机监控', value: 'host'}
];

const topicMonitorTypes = [
    {label: '基础监控', value: 'basic'},
    {label: '主题分节点', value: 'broker'},
    {label: '节点分主题', value: 'topic'}
];

const consumerMonitorTypes = [
    {label: '基础监控', value: 'basic'},
    {label: '消费组分主题', value: 'topic'},
    {label: '消费组分分区', value: 'partition'}
];

const widerIndicator = [
    '生产消息速率',
    '消息滞后量'
];

@connectMyStore({
    brokerIndicators: 'brokerIndicators'
})
class NewChart extends Chart {
    inited() {
        super.inited();

        this.handleReq();
        this.watch('current', () => this.handleReq());
    }

    isCase() {
        const {metrics} = this.data.get('item');
        return _.isArray(metrics) && metrics[0].value && _.includes(AdjustMetricsArr, metrics[0].value);
    }

    isAllAndClusterTopic() {
        const {current} = this.data.get('');
        return _.includes([TabKeyType.BROKER, TabKeyType.TOPIC, TabKeyType.CONSUMERGROUP], current);
    }

    handleReq() {
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';
        this.data.set('requester', isEdgeRegion
            ? bjClient.bcmMetricDataMetricData.bind(bjClient)
            : api.bcmMetricDataMetricData.bind(api)
        );
        // 设置请求返回参数
        this.data.set('proccessor', this.proccessor.bind(this));
    }

    queryProccessor(data: {
        startTime: string;
        endTime: string;
        metricNames: string[];
        dimensions: string[];
        [x: string]: any;
    }) {
        let newData: {[x: string]: any} = _.cloneDeep(data);
        const {item, diskTypes = []} = this.data.get('');
        const diskType = item.diskType || diskTypes[0]?.value;
        if (this.isAllAndClusterTopic()) {
            if (_.includes(AddFaceIDType, newData.metricNames[0])) {
                newData.dimensions = _.map(newData.dimensions, item => [
                    ...item,
                    {name: 'FaceId', value: 'eth0'}
                ]);
            } else if (_.includes(AddDiskIDType, newData.metricNames[0])) {
                newData.dimensions = _.map(newData.dimensions, item => [
                    ...item,
                    {name: 'DiskId', value: diskType}
                ]);
            }
        }
        if (this.isCase()) {
            newData.scope = 'BCE_BCC';
            const instances = this.data.get('instances');
            newData.dimensions = _.map(instances, item => [
                {name: 'InstanceId', value: item.instanceId}
            ]);
        } else {
            newData.type = this.data.get('item.type');
        }
        newData.statistics = [newData.statistics];
        const isEdgeRegion = window.$context.getCurrentRegionId() === 'edge';

        return {
            ...newData,
            cycle: newData.periodInSecond ? newData.periodInSecond : 60,
            scope: this.isCase() ? 'BCE_BCC' : 'BCE_MQ_KAFKA',
            userId: this.$context.getUserId(),
            region: isEdgeRegion ? 'bj' : this.$context.getCurrentRegionId(),
            endTime: formatUtcTime(m(data.endTime)),
            startTime: formatUtcTime(m(data.startTime))
        };
    }

    // 处理返回数据
    proccessor(
        data: {
            metrics: Array<{
                dataPoints: Array<{average: number; minimum: number; maximum: number; sum: number; timestamp: string}>;
                dimensions: NormalObject;
                metricName: string;
                resourceId: string;
            }>;}
    ) {
        // {series:[{name:'监控项名称', data:[数据点]}], category:[]}
        let newData = {series: [], category: []};
        data.metrics.forEach((item, index) => {
            let seriesData = item.dataPoints.map(i => ({
                average: i.average,
                minimum: i.minimum,
                maximum: i.maximum,
                sum: i.sum
            }));

            let name = '';
            if (this.data.get('item.type') === 'Cluster') {
                name = item.metricName;
            } else {
                name = item.resourceId;
            }
            newData.series.push({data: seriesData, name, dimensions: item.dimensions});
        });
        data.metrics[0]?.dataPoints.forEach(i => {
            newData.category.push(i.timestamp);
        });
        const {item, instances, nameType} = this.data.get('');
        // 公网相关
        if (this.isCase() || item.type === 'Node') {
            newData.series = _.map(newData.series, seriesItem => ({
                data: this.handleData(seriesItem.data),
                name: _.find(instances, i => i.instanceId === seriesItem.name)?.brokerId
            }));
        } else if (_.includes(['BrokerId', 'Topic', 'PartitionId'], nameType)) {
            newData.series = _.map(newData.series, seriesItem => ({
                data: this.handleData(seriesItem.data),
                name: _.find(seriesItem.dimensions, dimension => dimension.name === nameType).value
            }));
        }
        return newData;
    }

    // 将返回数据的数字转化精确到两位
    handleData(data: Array<NormalObject>) {
        return _.map(data, item => {
            Object.keys(item).forEach((key: string) => {
                const itemKey = item[key];
                if (typeof itemKey === 'number') {
                    item[key] = Number(itemKey.toFixed(2));
                }
            });
            return item;
        });
    }
}

@connectMyStore({
    consumerIndicators: 'originalconsumerIndicators',
    clusterIndicators: 'originalclusterIndicators',
    topicIndicators: 'originaltopicIndicators',
    brokerIndicators: 'originalbrokerIndicators'
})
class SelectTab extends Component {
    static template = html` <div class="${klass}-select-tab">
        <p class="basic">
            时间筛选：
            <s-select
                class="ml8 mr5"
                value="{{recentSelect}}"
                datasource="{{timeList}}"
                on-change="onRecentChange"
                height="{{height}}"
                width="{{100}}"
            />
            <s-date-range-picker
                mode="second"
                value="{= timeRange =}"
                class="mr24"
                range="{{range}}"
                on-change="onSelfConfigTimeChange"
            />
            <span s-if="{{current !== '${TabKeyType.CLUSTER}'}}" style="display: inline-block; vertical-align: middle">
                监控类型：
                <s-radio-group
                    class="radio-align"
                    on-change="handleMonitorTypeChange"
                    value="{{monitorType}}"
                    max="{{1}}"
                    datasource="{{monitorTypes}}"
                >
                </s-radio-group>
            </span>
            <s-tooltip content="指标筛选" placement="top" class="right">
                <s-button class="right ml8" on-click="onConfig">
                    <s-icon-config class="button-icon" is-button="{{false}}" />
                </s-button>
            </s-tooltip>
            <s-button class="right" on-click="onRefresh">
                <s-icon-refresh class="button-icon" is-button="{{false}}" />
            </s-button>
        </p>
        <base-filter
            s-ref="base-filter"
            current="{{current}}"
            monitorType="{{monitorType}}"
            broker="{{broker}}"
            topicBroker="{{topicBroker}}"
            clusterId="{{clusterId}}"
            topic="{{topic}}"
            quote="{{quote}}"
            consumer="{{consumer}}"
            consumerTopic="{{consumerTopic}}"
            location="page"
        />
    </div>`;

    initData() {
        return {
            timeList: TIME_FILTER,
            recentSelect: RECENT,
            height: SELECT_SEARCH_HEIGHT,
            monitorTypes: [],
            monitorType: '',
            indicatorList: [],
            range: {},
            isEdgeRegion: false
        };
    }

    static components = {
        's-select': Select,
        's-button': Button,
        's-date-range-picker': DatePicker.DateRangePicker,
        's-icon-refresh': OutlinedRefresh,
        's-icon-config': OutlinedSetting,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-tooltip': Tooltip,
        'base-filter': baseFilter,
        's-popover': Popover
    };

    inited() {
        const end = new Date().getTime() + 60 * 60 * 1000;
        const begin = end - 40 * 24 * 60 * 60 * 1000;
        const range = {
            begin: new Date(begin),
            end: new Date(end)
        };
        this.data.set('range', range);
        this.data.set('isEdgeRegion', window.$context.getCurrentRegionId() === 'edge');
    }

    attached() {
        const current = this.data.get('current');
        switch (current) {
            case TabKeyType.CLUSTER:
                this.data.set('monitorTypes', []);
                this.data.set('monitorType', '');
                this.onClusterChange();
                break;
            case TabKeyType.BROKER:
                this.data.set('monitorTypes', brokerMonitorTypes);
                this.data.set('monitorType', brokerMonitorTypes[0].value);
                break;
            case TabKeyType.TOPIC:
                this.data.set('monitorTypes', topicMonitorTypes);
                this.data.set('monitorType', topicMonitorTypes[0].value);
                break;
            case TabKeyType.CONSUMERGROUP:
                this.data.set('monitorTypes', consumerMonitorTypes);
                this.data.set('monitorType', consumerMonitorTypes[0].value);
                break;
        }
        this.setIndicatorList();
        const now = new Date().getTime();
        const timeRange = TIMERANGE_LIST['1h'];
        const range = {
            begin: new Date(now - timeRange),
            end: new Date(now)
        };
        this.data.set('timeRange', range);
        this.fire('recent-change', range);
        this.getBcmWhiteList();
    }

    /**
     * 获取公共参数，region、userid
     */
    async getPublicInfo() {
        if (this.$context) {
            let region = this.$context.getCurrentRegion();
            if (typeof region === 'object') {
                region = region.id;
            }
            if (this.$context.getCurrentRegion()) {
                const publicInfo = {
                    userId: this.$context.getUserId(),
                    region: region
                };
                this.data.set('publicInfo', publicInfo);
            }
        }
    }

    async getBcmWhiteList() {
        await this.getPublicInfo();
        const publicInfo = this.data.get('publicInfo');
        const res = await api.getBcmWhiteList({...publicInfo, user_id: publicInfo.userId});
        if (res.more_data_flag) {
            this.data.set('whiteUser', true);
            const end = new Date().getTime() + 60 * 60 * 1000;
            const begin = end - 180 * 24 * 60 * 60 * 1000;
            const range = {
                begin: new Date(begin),
                end: new Date(end)
            };
            this.data.set('range', range);
            this.data.splice('timeList', [8, 0, {alias: '90D', text: '近90天', value: '90d'}]);
            this.data.splice('timeList', [9, 0, {alias: '180D', text: '近180天', value: '180d'}]);
        }
    }


    onClusterChange() {
        this.dispatch('UI:cluster-change', {monitorType: ''});
    }

    handleMonitorTypeChange(target: {value: string}) {
        this.data.set('monitorType', target.value);
        this.ref('base-filter')?.handleMonitorTypeChange(target.value);
        this.setIndicatorList();
    }

    onRecentChange(target: {value: string}) {
        const now = new Date().getTime();
        const timeRange = TIMERANGE_LIST[target.value];
        const range = {
            begin: new Date(now - timeRange),
            end: new Date(now)
        };
        this.data.set('timeRange', range);
        this.data.set('recentSelect', target.value);
        if (target.value !== 'selfConfig') {
            this.fire('recent-change', range);
        }
    }

    onConfig() {
        const {current, monitorType, currentList} = this.data.get('');
        const dialog = new MonitorFilter({data: {current, monitorType, currentList}});
        dialog.attach(document.body);
        dialog.on('confirm', () => {
            this.setIndicatorList();
        });
    }

    setIndicatorList() {
        const {current, monitorType} = this.data.get('');
        let indicators = {};
        let statistics = 'average';
        let type = '';
        switch (current) {
            case TabKeyType.BROKER:
                type = monitorType === 'service' ? 'Broker' : 'Node';
                indicators = this.data.get(`brokerIndicators.${monitorType}Indicators`);
                break;
            case TabKeyType.TOPIC:
                type = 'Topic';
                indicators = this.data.get(`topicIndicators.${monitorType}Indicators`);
                break;
            case TabKeyType.CONSUMERGROUP:
                type = 'ConsumerGroup';
                indicators = this.data.get(`consumerIndicators.${monitorType}Indicators`);
                break;
            case TabKeyType.CLUSTER:
                type = 'Cluster';
                indicators = this.data.get('clusterIndicators');
                break;
            default:
                break;
        }

        let resultList = [];
        Object.getOwnPropertyNames(indicators).forEach(item => {
            const list = _.filter(indicators[item], indicator => {
                return indicator.checked;
            });
            resultList = resultList.concat(list);
        });
        resultList = resultList.map(item => ({
            name: item.label,
            statistics: item.value === 'ZooKeeperSessionState' ? 'maximum' : statistics,
            metrics: [{name: item.label, value: item.value}],
            unit: item.unit || '',
            type,
            tip: item.tip
        }));
        this.fire('indicator-change', {indicatorList: resultList});
    }

    onSelfConfigTimeChange(target: {value: any}) {
        let begin = new Date(target.value.begin).getTime();
        let end = new Date(target.value.end).getTime();
        this.data.set('recentSelect', 'selfConfig');
        if (begin === end) {
            begin = begin - 1000;
        }
        this.fire('recent-change', {begin, end});
    }

    onRefresh() {
        const {recentSelect, timeRange} = this.data.get('');
        if (recentSelect !== 'selfConfig') {
            const now = new Date().getTime();
            const timeRange = TIMERANGE_LIST[recentSelect];
            const range = {
                begin: new Date(now - timeRange),
                end: new Date(now)
            };
            this.data.set('timeRange', range);
            this.data.set('recentSelect', recentSelect);
            this.fire('recent-change', range);
        } else {
            this.fire('recent-change', timeRange);
        }
        this.fire('refresh', {});
    }
}

export default class Monitor extends Component implements DetailRefresh {
    static template = html` <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="page-title">
                <h2 class="title">集群监控</h2>
            </div>
            <s-tabs on-change="onTabChange" type="line" active="{= current =}">
                <s-tabpane s-for="item in tabList" label="{{item.label}}" key="{{item.key}}" lazy>
                    <select-tab
                        s-ref="select-tab"
                        clusterId="{{clusterId}}"
                        broker="{{broker}}"
                        topicBroker="{{topicBroker}}"
                        topic="{{topic}}"
                        quote="{{quote}}"
                        consumer="{{consumer}}"
                        consumerTopic="{{consumerTopic}}"
                        recent="{{recent}}"
                        current="{{current}}"
                        on-indicator-change="onIndicatorChange"
                        on-recent-change="onMonitorInfoChange"
                        on-refresh="refreshChart"
                    />
                    <div class="${klass}__pie">
                        <div
                            s-for="item,i in arr"
                            key="{{item.metricName}}"
                            class="${klass}__pie_item_{{i % 2 === 0 ? 'odd' : 'even'}} {{item.widerIndicator ? '${klass}__pie_item_wider' : '${klass}__pie_item'}}"
                        >
                            <template s-if="{{item.showDiskSelect}}">
                                <s-select
                                    class="disk-select"
                                    width="100"
                                    datasource="{{diskTypes}}"
                                    value="{= item.diskType =}"
                                    on-change="onDiskChange($event, i)"
                                />
                            </template>
                            <template s-if="{{item.tip}}">
                                <span class="chart-tip">{{item.tip}}</span>
                            </template>
                            <chart
                                s-ref="chart-{{i}}"
                                item="{{item}}"
                                diskTypes="{{diskTypes}}"
                                nameType="{{nameType}}"
                                startTime="{{recent.begin}}"
                                endTime="{{recent.end}}"
                                dimensions="{{dimensions}}"
                                hideLegend="{{dimensions.length <= 1}}"
                                scope="${CLUSTER_SCOPE}"
                                instances="{{instances}}"
                                current="{{current}}"
                                precision="{{2}}"
                            >
                                <s-select
                                    class="detail-title-slot"
                                    slot="filter-title"
                                    datasource="{{titleSource}}"
                                    on-change="onTitleChange($event, i)"
                                    value="{{ item.metrics[0].value }}"
                                />
                                <base-filter
                                    current="{{current}}"
                                    class="detail-filter-item"
                                    slot="filter-item"
                                    monitorType="{{monitorType}}"
                                    location="slot"
                                    idx="{{i}}"
                                    broker="{{broker}}"
                                    topicBroker="{{topicBroker}}"
                                    topic="{{topic}}"
                                    quote="{{quote}}"
                                    consumer="{{consumer}}"
                                    clusterId="{{clusterId}}"
                                    consumerTopic="{{consumerTopic}}"
                                />
                            </chart>
                        </div>
                    </div>
                </s-tabpane>
            </s-tabs>
        </app-list-page>
    </div>`;

    static messages = {
        'UI:cluster-change': function (e: any) {
            this.onSelectChange('cluster', e.value);
        },
        'UI:broker-change': function (e: any) {
            this.data.set('broker', e.value.value);
            this.onSelectChange('broker', e.value);
        },
        'UI:topic-change': function (e: any) {
            this.data.set('topic', e.value.topic);
            this.data.set('topicBroker', e.value.broker);
            this.onSelectChange('topic', e.value);
        },
        'UI:quote-change': function (e: any) {
            this.data.set('consumer', e.value.consumer);
            this.data.set('consumerTopic', e.value.consumerTopic);
            this.data.set('quote', e.value.quote);
            this.onSelectChange('quote', e.value);
        }
    };

    static components = {
        'app-list-page': AppListPage,
        's-button': Button,
        'select-tab': SelectTab,
        chart: NewChart,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        's-select': Select,
        'base-filter': baseFilter
    };

    initData() {
        return {
            tabList: TabsList,
            current: TabKeyType.CLUSTER,
            arr: [],
            diskTypes: [],
            dimensions: []
        };
    }

    inited() {
        const topic = this.data.get('topic');
        if (topic) {
            this.data.set('current', TabKeyType.TOPIC);
        }
        this.getDisks();
        const {publicIpEnabled} = this.data.get('');
        if (!publicIpEnabled) {
            myStore.dispatch('changeBrokerIndicators', {
                monitorType: brokerMonitorTypes[1].value,
                type: brokerByhostTypeList[2].value,
                index: 0,
                target: {value: false}
            });
            myStore.dispatch('changeBrokerIndicators', {
                monitorType: brokerMonitorTypes[1].value,
                type: brokerByhostTypeList[2].value,
                index: 1,
                target: {value: false}
            });
            myStore.dispatch('updateBrokerIndicators', brokerMonitorTypes[1].value);
            myStore.dispatch('updateBrokerIndicators', brokerMonitorTypes[1].value);
        }
    }

    // 下拉框切换
    onSelectChange(type: 'cluster' | 'broker' | 'topic' | 'quote', target: NormalObject) {
        const {clusterId} = this.data.get('');
        const {monitorType, location, idx} = target;
        let dimensions: Array<any> = [];
        let prefix = {
            name: 'ClusterId',
            value: clusterId
        };
        let nameType = 'basic';
        switch (type) {
            case 'cluster': {
                dimensions.push([{...prefix}]);
                break;
            }
            case 'broker': {
                if (monitorType === 'service') {
                    this.data.set('instances', target.instances);
                    dimensions = _.map(target.value, item => [
                        {...prefix},
                        {name: 'BrokerId', value: item.brokerId}
                    ]);
                } else if (monitorType === 'host') {
                    this.data.set('instances', target.instances);
                    dimensions = _.map(target.value, item => [
                        {...prefix},
                        {name: 'NodeId', value: item.instanceId}
                    ]);
                }
                break;
            }
            case 'topic': {
                if (monitorType === 'basic') {
                    dimensions = _.map(target.topic, item => [
                        {...prefix},
                        {name: 'Topic', value: item}
                    ]);
                } else if (monitorType === 'topic') {
                    nameType = 'Topic';
                    dimensions = _.map(target.topic, item => [
                        {...prefix},
                        {name: 'BrokerId', value: target.broker},
                        {name: 'Topic', value: item}
                    ]);
                } else if (monitorType === 'broker') {
                    nameType = 'BrokerId';
                    dimensions = _.map(target.broker, item => [
                        {...prefix},
                        {name: 'Topic', value: target.topic},
                        {name: 'BrokerId', value: item}
                    ]);
                }
                break;
            }
            case 'quote':
                if (monitorType === 'basic') {
                    dimensions = _.map(target.consumer, item => [
                        {...prefix},
                        {name: 'ConsumerGroup', value: item}
                    ]);
                } else if (monitorType === 'topic') {
                    nameType = 'Topic';
                    dimensions = _.map(target.consumerTopic, item => [
                        {...prefix},
                        {name: 'ConsumerGroup', value: target.consumer},
                        {name: 'Topic', value: item}
                    ]);
                } else if (monitorType === 'partition') {
                    nameType = 'PartitionId';
                    dimensions = _.map(target.quote, item => [
                        {...prefix},
                        {name: 'ConsumerGroup', value: target.consumer},
                        {name: 'Topic', value: target.consumerTopic},
                        {name: 'PartitionId', value: item}
                    ]);
                }
                break;
        }
        this.data.set('nameType', nameType);
        this.data.set('monitorType', monitorType);
        this.data.set('dimensions', dimensions);
        this.nextTick(() => {
            this.refreshChart();
        });
    }

    onTitleChange(target: { value: string }, index: number) {
        const titleSource = this.data.get("titleSource");
        const idx = _.findIndex(
            titleSource,
            (item) => item.value === target.value
        );
        this.nextTick(() => {
            const node = this.ref(`chart-${index}`) as Chart;
            const detailDialog = node.ref("chart").dialog;
            if (detailDialog && detailDialog.el) {
                const targetData = _.cloneDeep(
                    this.ref(`chart-${idx}`).ref("chart").data.raw
                );
                detailDialog.data.set('title', targetData.metrics[0]?.name);
                detailDialog.data.raw.unit = targetData.unit;
                detailDialog.data.raw.detailConf = _.cloneDeep(targetData.conf);
                detailDialog.data.raw.metricConfig = _.cloneDeep(
                    targetData.metricConfig
                );
                detailDialog.data.raw.BcmRequester = _.cloneDeep(
                    targetData.BcmRequester
                );
                detailDialog.data.raw.metrics = _.cloneDeep(targetData.metrics);
                detailDialog.data.set('detailMetricConf', _.cloneDeep(
                    targetData.metricConfig
                ));
                detailDialog.handleRefresh();
            }
        });
    }

    // tab切换
    onTabChange(target: {value: {key: TabKeyType}}) {
        this.data.set('broker', '');
        this.data.set('topicBroker', '');
        this.data.set('topic', '');
        this.data.set('quote', '');
        this.data.set('consumer', '');
        this.data.set('consumerTopic', '');
        this.data.set('current', target.value.key);
    }

    onDiskChange(target: {value: string}, index: number) {
        this.data.set(`arr[${index}].diskType`, target.value);
    }

    onIndicatorChange(target: NormalObject) {
        const diskTypes = this.data.get('diskTypes');
        const titleSource: any[] = [];
        target.indicatorList.forEach((item: NormalObject, index: number) => {
            let indicator = item;
            if (_.includes(AddDiskIDType, item.metrics[0].value)) {
                indicator = {
                    ...indicator,
                    showDiskSelect: true,
                    diskType: diskTypes[0].value
                };
            }
            target.indicatorList[index] = {
                ...indicator,
                widerIndicator: _.includes(widerIndicator, item.name)
            };
            titleSource.push({
                label: item.metrics[0].name,
                text: item.metrics[0].name,
                value: item.metrics[0].value
            });
        });
        this.data.set('arr', target.indicatorList);
        this.data.set('titleSource', titleSource);
    }

    getDisks() {
        return api
            .listDisks(this.data.get('clusterId'), {})
            .then((target: Array<{diskId: number; diskName: string}>) => {
                const arr = _.map(target, s => ({text: s.diskName, value: s.diskId, alias: s.diskName}));
                this.data.set('diskTypes', arr);
            });
    }

    // 时间刷新
    onMonitorInfoChange(value: string) {
        this.data.set('recent', value);
        this.refreshChart();
    }

    // 刷新图标
    refreshChart() {
        _.each(this.data.get('arr'), (item, index) => {
            const node = this.ref(`chart-${index}`) as Chart;
            node && node.refresh();
        });
    }
}
