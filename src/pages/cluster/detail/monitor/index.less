/**
 * @file index.less
 * <AUTHOR>
 */

@klass: bms-cluster-detail-monitor;

.@{klass} {
    .basic {
        margin-bottom: 16px;
        line-height: 20px;

        .s-checkbox-group-wrapper {
            margin-left: 24px;

            &:first-child {
                margin-left: 0;
            }
        }

        .right {
            float: right;
            width: 32px;
            height: 32px;
            box-sizing: border-box;
        }
    }

    .radio-align {
        display: inline-block;
        .s-radio-group {
            display: inline-block;

            .s-wrapper {
                vertical-align: middle;
                margin-top: 5px;
            }
        }
    }

    &_content {
        .title {
            border: 0;
        }

        .page-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--whiteColor);
            padding-right: 24px;
        }
    }

    .s-tabs {
        .s-tabnav {
            width: 100%;

            .s-tabnav-scroll {
                .s-tabnav-nav {
                    .s-tabnav-nav-item {
                        width: auto;
                    }
                    .s-tabnav-nav-selected {
                        &::after {
                            width: auto;
                        }
                    }
                }
            }
        }

        .s-tabpane-wrapper {
            margin-top: 16px;
        }
    }

    &__pie {
        margin-top: 20px;
        display: inline-block;
        width: 100%;

        &_item {
            width: 50%;
            display: inline-block;
            position: relative;
            .disk-select {
                position: absolute;
                right: 40px;
                top: 0;
                z-index: 999;
            }

            .chart-tip {
                position: absolute;
                left: 80px;
                top: 52px;
                z-index: 999;
            }

            &_even {
                padding-left: 3px;
            }

            &_odd {
                padding-right: 3px;
            }
        }
        &_item_wider {
            width: 100%;
            display: inline-block;

            &_even {
                padding-left: 3px;
            }

            &_odd {
                padding-right: 3px;
            }
        }
    }
}
