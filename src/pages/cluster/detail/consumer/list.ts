/**
 * 消费组
 *
 * @file list.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Dialog, Pagination, Table, Notification, Button} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';

import CreateBtn from '@/components/create-btn';
import PaginationTable from '@/components/common-table/pagination-table';
import EllipsisTip from '@/components/ellipsis-tip';
import {TABLE_SUI, PAGER_SUI, ROUTE_PATH} from '@/common/config';
import {formatTime} from '@/common/util';
import Permission from '@/common/permission';

import ResetSite from '@/pages/consumer-group/list/reset-site'; // 重置位点

import api from '@/common/client';

const klass = 'bms-cluster-detail-consumer';

type ConsumerItem = {
    groupName: string;
    updateTime: string;
};

export default class Consumer extends PaginationTable implements DetailRefresh {

    searchKey = 'groupName';

    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">消费组列表</h2>
            </div>
            <div slot="bulk">
                <s-searchbox
                    class="searchbox"
                    placeholder="请输入消费组名称进行搜索"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="{{170}}"
                >
                    <div slot="prefix"></div>
                </s-searchbox>
            </div>
            <div slot="filter">
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}">
                <div slot="c-groupName">
                    <a class="a-btn" href="#${ROUTE_PATH.clusterDetailConsumerSubscription}?name={{name}}&clusterId={{clusterId}}&consumerGroup={{row.groupName}}">
                        <ellipsis-tip text="{{row.groupName}}" placement="bottom" />
                    </a>
                </div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onReset(row)"
                            disabled="{{detail.status | disabledReset(row.state)}}">
                            重置位点
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{detail.status | disabledDelete(row.state)}}"
                            on-click="onDelete(row)">
                            删除
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty vertical actionText="" />
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'ellipsis-tip': EllipsisTip,
        'create-btn': CreateBtn,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty
    };

    initData() {
        return {
            searchbox: {
                keyword: ''
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'groupName',
                        label: '消费组名称'
                    },
                    {
                        name: 'state',
                        label: '消费组状态'
                    },
                    {
                        name: 'groupCoordinatorId',
                        label: 'Coordinator (ID)',
                        width: 120,
                        render: (item: {groupCoordinatorId: number}) => {
                            return item.groupCoordinatorId !== -1 ? item.groupCoordinatorId : '未知';
                        }
                    },
                    {
                        name: 'updateTime',
                        label: '最后消费时间',
                        render: (item: {updateTime: string}) => formatTime(item.updateTime)
                    },
                    {
                        name: 'operation',
                        label: '操作',
                        width: 280
                    }
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        disabledReset(status: string, state: string) {
            return Permission.CLUSTER.CONSUMERGROUP.disabledReset(status) || state !== 'EMPTY';
        },
        disabledDelete(status: string, state: string) {
            return Permission.CLUSTER.CONSUMERGROUP.disabledDelete(status) || state !== 'EMPTY';
        },
    };

    attached() {
        this.getComList();
    }

    async getNewTableList() {
        return await api.listClusterConsumerGroups(this.data.get('clusterId'), {});
    }

    // 删除
    onDelete(row: ConsumerItem) {
        Dialog.warning({
            content: '您确定删除此消费组吗？',
            okText: '确定',
            onOk: async () => {
                const {clusterId} = this.data.get();
                await api.deleteClusterConsumerGroup(clusterId, row.groupName, {});
                Notification.success('删除成功');
                this.resetTable();
            }
        });
    }

    // 重置
    onReset(row: ConsumerItem) {
        const dialog = new ResetSite({
            data: {
                clusterId: this.data.get('clusterId'),
                mode: 'cluster',
                consumerGroupName: row.groupName
            }
        });
        dialog.attach(document.body);
    }

    // refresh更新
    refreshInfo() {
        return this.getComList();
    }
}
