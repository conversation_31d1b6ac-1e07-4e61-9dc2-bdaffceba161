/**
 * 订阅关系
 *
 * @file Subscription.ts
 * <AUTHOR>
 */
import {SanComponent} from 'san';
import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Pagination, Table, Button} from '@baidu/sui';
import {AppListPage} from '@baidu/sui-biz';
import CommonTable from '@/components/common-table';
import {TABLE_SUI, PAGER_SUI} from '@/common/config';

import {pickEmpty, formatTime, formatEmpty} from '@/common/util';
import {SubscriptionCell} from '@/pages/cluster/detail/topic/detail/subscription';

import api from '@/common/client';

const klass = 'bms-cluster-detail-topic-quote';

type SubscriptionItem = {
    id: number
}

export default class Subscription extends CommonTable {
    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">订阅关系</h2>
            </div>
            <div class="mt20 mb15">
                <span class="topic__text_distance">
                    <span class="mr5">订阅主题数目：</span>
                    <span>{{topicNum}}</span>
                </span>
                <span>
                    <span class="mr5">最后消费时间：</span>
                    <span>{{lastUpdateTime}}</span>
                </span>
            </div>
            <s-table
                class="btn-format-table"
                s-ref="table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
            >
                <div slot="c-operation">
                    <span class="operation">
                        <s-button 
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onDetail(row, rowIndex, $event)">
                            查看详情
                        </s-button>
                    </span>
                </div>
                <div slot="sub-detail">
                    <subscription-cell
                        clusterId="{{clusterId}}"
                        topic="{{row.topic}}"
                        consumerId="{{consumerGroup}}"
                        mode="consumer"
                    />
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-table': Table,
        's-pagination': Pagination,
        's-button': Button,
        'subscription-cell': SubscriptionCell
    }

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'num',
                        label: '编号',
                        render: (item: SubscriptionItem, key: string, obj: Object, rowIndex: number) => {
                            const {pageSize, page} = this.data.get('pager')
                            return pageSize * (page - 1) + rowIndex + 1;
                        }
                    },
                    {name: 'topic', label: '主题名称'},
                    {name: 'operation', label: '操作', width: 90},
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    attached() {
        this.getComList();
        this.getInfo();
    }

    async getTableList() {
        this.resetTableDetail();
        const {pager, clusterId, consumerGroup} = this.data.get();
        const params = pickEmpty({
            pageNo: pager.page,
            pageSize: pager.pageSize
        });
        const {totalCount, result} = await api.getClusterConsumerGroupDetail(clusterId, consumerGroup, params);
        this.data.set('pager.count', totalCount);
        this.data.set('table.datasource', _.map(result, i => ({topic: i, subSlot: 'sub-detail'})));
    }

    getInfo() {
        const {clusterId, consumerGroup} = this.data.get();
        api.getClusterSubscribedTopicsSummary(clusterId, consumerGroup, {})
            .then((target: {topicNum: number, lastUpdateTime: string}) => {
                this.data.set('topicNum', formatEmpty(target.topicNum));
                this.data.set('lastUpdateTime', formatTime(target.lastUpdateTime));
            });
    }

    // 面板切换前对详情点击重置
    resetTableDetail() {
        const node = (this.ref('table') as unknown as SanComponent<{}>);
        const expandedIndex = node.data.get('expandedIndex');
        if (_.isArray(expandedIndex) && expandedIndex.length > 0) {
            node.data.set('expandedIndex', []);
        }
    }

    /**
     * 详情点击
     */
    onDetail(row: Object, rowIndex: number, e: Event) {
        const node = (this.ref('table') as unknown as SanComponent<{}>);
        const expandedIndex = node.data.get('expandedIndex');
        expandedIndex.indexOf(rowIndex) === -1 && this.resetTableDetail();
        this.toggleRow(rowIndex, e);
    }

    /**
     * 下拉表格toggle
     */
    toggleRow(rowIndex: number, e: Event | null) {
        // @ts-ignore
        (this.ref('table') as unknown as Table).toggleExpandRow(e, rowIndex);
    }
}