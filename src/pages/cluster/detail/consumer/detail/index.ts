/**
 * 消费组详情
 *
 * @file index.ts
 * <AUTHOR>
 */

import {decorators, html, redirect} from '@baiducloud/runtime';
import {AppTabPage, AppDetailPage} from '@baidu/sui-biz';
import {Button, Select, Loading} from '@baidu/sui';

import DetailNav from '@/components/nav/detail';
import {ROUTE_PATH} from '@/common/config';

import Subscription from './subscription';

const klass = 'bms-cluster-consumer-info';


enum Refs {
    SUBSCRIPTION = 'subscription'
}

@decorators.asPage(ROUTE_PATH.clusterDetailConsumerSubscription)
export default class extends AppDetailPage {

    pageTitle = '消费组详情';

    static template = html`
    <div class="${klass}">
        <s-detail-page class="bms-detail-page">
            <nav
                slot="pageTitle"
                title="{{consumerGroup}}"
                back="#${ROUTE_PATH.clusterDetailConsumer}?name={{name}}&clusterId={{clusterId}}"
            >
            </nav>
            <div class="bms-detail-page__content">
                <app-tab-page
                    class="bms-tab-page"
                    skin="accordion"
                    position="left"
                    direction="vertical"
                    active="{= active =}"
                    on-change="onTabChange">
                    <app-tab-page-panel
                        style="{{active | show(1)}}"
                        label="订阅关系"
                        url="#${ROUTE_PATH.clusterDetailConsumerSubscription}?{{nextSuffix}}">
                        <subscription
                            s-ref="${Refs.SUBSCRIPTION}"
                            s-if="{{active === 1}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            consumerGroup="{{consumerGroup}}"
                        />
                    </app-tab-page-panel>
                </app-tab-page>
            </div>
        </s-detail-page>
    </div>`;

    static components = {
        's-detail-page': AppDetailPage,
        'nav': DetailNav,
        's-button': Button,
        's-select': Select,
        's-option': Select.Option,
        's-loading': Loading,
        'app-tab-page': AppTabPage,
        'app-tab-page-panel': AppTabPage.TabPanel,
        'subscription': Subscription
    };

    initData() {
        return {
            isLoading: false
        };
    }

    static computed: SanComputedProps = {
        clusterId(): string {
            return this.data.get('route.query.clusterId');
        },
        name(): string {
            return this.data.get('route.query.name');
        },
        consumerGroup() {
            return this.data.get('route.query.consumerGroup');
        },

        nextSuffix() {
            const clusterId = this.data.get('clusterId');
            const name = this.data.get('name');
            const consumerGroup = this.data.get('consumerGroup');
            return `name=${name}&clusterId=${clusterId}&consumerGroup=${consumerGroup}`;
        },
    };

    static filters: SanFilterProps = {
        show(active: string, key: string) {
            return active === key ? '' : 'display:none';
        }
    };

    attached() {}

    onRegionChange() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }
}
