/**
 * 集群详情
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {AppDetailPage, AppTabPage} from '@baidu/sui-biz';
import {Button, Select, Loading, Notification, Alert} from '@baidu/sui';
import {ClusterStatusType} from '@/common/enums/constant';
import {getTimer} from '@/common/util';

import {ClusterStatus} from '@/common/enums';
import DetailNav from '@/components/nav/detail';
import api from '@/common/client';
import {MINUTE, ROUTE_PATH} from '@/common/config';
import Info from './info';
import Topic from './topic/list';
import Consumer from './consumer/list';
import UserList from './user/list';
import Monitor from './monitor';
import NoRight from './no-right';
import Log from './log';
import AuthorityList from './authority/list';
import Message from './message';
import Operation from './operation';
import Storage from './storage-analyse';
import Process from './process';

import './index.less';

const klass = 'bms-cluster-detail';

export type ClusterDetail = {
    clusterId: string;
    name: string;
    createTime: string;
    version: string;
    status: string;
    runningTime: string;
    region: string;
    payment: string;
    timeLength: number;
    configName: string;
    configId: string;
    vpc: {
        vpcId: string;
        vpcUuid: string;
        name: string;
        cidr: string;
    };
    subnets: string[];
    logicalZones: string[];
    securityGroups: object[];
    publicIpEnabled: boolean;
    publicIpBandwidth: number;
    storageMeta: {
        storageType: string;
        storageSize: string;
        numberOfDisk: number;
    };
    nodeType: string;
    numberOfBrokerNodes: number;
    numberOfBrokerNodesPerZone: number;
    aclEnabled: boolean;
    authenticationModes: string[];
    storagePolicy: object;
    storagePolicyEnabled: boolean;
};

enum Refs {
    INFO = 'info',
    TOPIC = 'topic',
    CONSUMER = 'consumer',
    USER = 'user',
    AUTHORITY = 'authority',
    PROCESS = 'process',
    STORAGE = 'storage',
    MONITOR = 'monitor',
    LOG = 'log',
    MESSAGE = 'message',
    OPERATION = 'operation',
}

// 这里需要按照顺序来
const Pages = [
    ROUTE_PATH.clusterDetailInfo,
    ROUTE_PATH.clusterDetailTopic,
    ROUTE_PATH.clusterDetailConsumer,
    ROUTE_PATH.clusterDetailUser,
    ROUTE_PATH.clusterDetailAuthority,
    ROUTE_PATH.clusterDetailProcess,
    ROUTE_PATH.clusterDetailMessage,
    ROUTE_PATH.clusterDetailStorage,
    ROUTE_PATH.clusterDetailMonitor,
    ROUTE_PATH.clusterDetailLog,
    ROUTE_PATH.clusterOperationList,
];

type RefsClass = Info | Topic | Consumer | UserList | AuthorityList | Process | Monitor | Log | Operation;

let timeCount: number;
let detailError = 0;

@decorators.asPage(...Pages)
export default class extends AppDetailPage {

    pageTitle = '集群详情';

    static template = html`
    <div class="${klass}">
        <s-detail-page class="bms-detail-page">
            <nav
                slot="pageTitle"
                title="{{name}}"
                options="{{options}}"
                back="#${ROUTE_PATH.clusterList}"
            >
                <div slot="right">
                    <s-button
                        width="46"
                        loading="{{isLoading}}"
                        on-click="onRefresh">
                        刷新
                    </s-button>
                </div>
            </nav>
            <s-alert
                skin="warning"
                class="${klass}__alert"
                s-if="{{showAlert}}">
                {{alertText}}
            </s-alert>
            <s-loading class="detail-loading" s-if="{{loading}}" loading size="large"></s-loading>
            <div s-else class="bms-detail-page__content {{isDeploying ? 'deploying' : ''}}">
                <app-tab-page
                    class="bms-tab-page"
                    skin="accordion"
                    position="left"
                    direction="vertical"
                    active="{= active =}"
                    on-change="onTabChange">
                    <app-tab-page-panel
                        style="{{active | show(1)}}"
                        label="集群详情"
                        url="#${ROUTE_PATH.clusterDetailInfo}?{{nextSuffix}}">
                        <info
                            s-ref="${Refs.INFO}"
                            s-if="{{active === 1}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            isMultipleDisks="{{isMultipleDisks}}"
                            on-refresh-page="onRefresh"
                            on-active-log-tab="OpenNodeLog"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(2)}}"
                        label="主题管理"
                        url="#${ROUTE_PATH.clusterDetailTopic}?{{nextSuffix}}">
                        <topic
                            s-ref="${Refs.TOPIC}"
                            s-if="{{active === 2}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(3)}}"
                        label="消费组管理"
                        url="#${ROUTE_PATH.clusterDetailConsumer}?{{nextSuffix}}">
                        <consumer
                            s-ref="${Refs.CONSUMER}"
                            s-if="{{active === 3}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(4)}}"
                        label="用户管理"
                        url="#${ROUTE_PATH.clusterDetailUser}?{{nextSuffix}}">
                        <user-list
                            s-ref="${Refs.USER}"
                            s-if="{{active === 4 && userManage}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"/>
                        <noright
                            s-ref="${Refs.USER}"
                            type="user"
                            s-if="{{active === 4 && !userManage}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"/>
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(5)}}"
                        label="权限管理"
                        url="#${ROUTE_PATH.clusterDetailAuthority}?{{nextSuffix}}">
                        <authority-list
                            s-ref="${Refs.AUTHORITY}"
                            s-if="{{active === 5 && accessManage}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"/>
                        <noright
                            s-ref="${Refs.AUTHORITY}"
                            type="authority"
                            s-if="{{active === 5 && !accessManage}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(6)}}"
                        label="流控管理"
                        url="#${ROUTE_PATH.clusterDetailProcess}?{{nextSuffix}}">
                        <process-list
                            s-ref="${Refs.PROCESS}"
                            s-if="{{active === 6}}"
                            userManage="{{userManage}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"/>
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(7)}}"
                        label="消息查询"
                        url="#${ROUTE_PATH.clusterDetailMessage}?{{nextSuffix}}">
                        <message
                            s-ref="${Refs.MESSAGE}"
                            s-if="{{active === 7}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            publicIpEnabled="{{detail.publicIpEnabled}}"
                            name="{{name}}"
                            topic="{{topic}}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(8)}}"
                        label="存储分析"
                        url="#${ROUTE_PATH.clusterDetailStorage}?{{nextSuffix}}">
                        <storage
                            s-ref="${Refs.STORAGE}"
                            s-if="{{active === 8}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(9)}}"
                        label="集群监控"
                        url="#${ROUTE_PATH.clusterDetailMonitor}?{{monitorSuffix}}">
                        <monitor
                            s-ref="${Refs.MONITOR}"
                            s-if="{{active === 9}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            topic="{{topic}}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(10)}}"
                        label="集群日志"
                        url="#${ROUTE_PATH.clusterDetailLog}?{{nextSuffix}}">
                        <log
                            s-ref="${Refs.LOG}"
                            s-if="{{active === 10}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            nodeId="{{nodeId}}"
                        />
                    </app-tab-page-panel>
                    <app-tab-page-panel
                        style="{{active | show(11)}}"
                        label="任务管理"
                        url="#${ROUTE_PATH.clusterOperationList}?{{nextSuffix}}">
                        <operation
                            s-ref="${Refs.OPERATION}"
                            s-if="{{active === 11}}"
                            detail="{{detail}}"
                            clusterId="{{clusterId}}"
                            name="{{name}}"
                            topic="{{topic}}"
                        />
                    </app-tab-page-panel>
                </app-tab-page>
            </div>
        </s-detail-page>
    </div>`;

    static components = {
        's-detail-page': AppDetailPage,
        'nav': DetailNav,
        's-button': Button,
        's-select': Select,
        's-option': Select.Option,
        's-loading': Loading,
        's-alert': Alert,
        'app-tab-page': AppTabPage,
        'app-tab-page-panel': AppTabPage.TabPanel,
        'info': Info,
        'topic': Topic,
        'consumer': Consumer,
        'user-list': UserList,
        'monitor': Monitor,
        'log': Log,
        'message': Message,
        'authority-list': AuthorityList,
        'operation': Operation,
        'noright': NoRight,
        'storage': Storage,
        'process-list': Process
    };

    initData() {
        return {
            active: 1,
            isLoading: false,
            isMultipleDisks: false,
            onRouterChange: this.onRouterChange.bind(this),
            userManage: true,
            accessManage: true,
            nodeId: '',
            loading: true
        };
    }

    static computed: SanComputedProps = {
        clusterId(): string {
            return this.data.get('route.query.clusterId');
        },
        name(): string {
            return this.data.get('route.query.name');
        },
        topic() {
            return this.data.get('route.query.topic');
        },
        isDeploying() {
            const status = this.data.get('detail.status');
            return status === ClusterStatusType.DEPLOYING;
        },
        showAlert() {
            const status = this.data.get('detail.status');
            return _.includes([ClusterStatusType.DEPLOYING, ClusterStatusType.UPDATING, ClusterStatusType.UPDATE_ROLLBACKING, ClusterStatusType.REBOOTING, ClusterStatusType.PRE_REBOOTING, ClusterStatusType.PRE_UPDATING], status);
        },
        alertText() {
            const status = this.data.get('detail.status');
            let alertText;
            switch (status) {
                case ClusterStatusType.DEPLOYING:
                    alertText = '部署中，预计需要10~30分钟。请在部署成功后创建主题、用户及权限。';
                    break;
                case ClusterStatusType.UPDATING:
                    alertText = '变更中，预计需要10~30分钟。变更进行期间仅支持各类查看操作，请在变更完成后恢复正常使用。';
                    break;
                case ClusterStatusType.PRE_UPDATING:
                    alertText = '待变更，预计需要10~30分钟。变更进行期间仅支持各类查看操作，请在变更完成后恢复正常使用。';
                    break;
                case ClusterStatusType.UPDATE_ROLLBACKING:
                    alertText = '变更回滚中，预计需要10~30分钟。回滚进行期间仅支持各类查看操作，请在回滚完成后恢复正常使用。';
                    break;
                case ClusterStatusType.REBOOTING:
                    alertText = '重启中，预计需要10~30分钟。请在重启成功后创建主题、用户及权限。';
                    break;
                case ClusterStatusType.PRE_REBOOTING:
                    alertText = '待重启，预计需要10~30分钟。请在重启成功后创建主题、用户及权限。';
                    break;
            }
            return alertText;
        },
        nextSuffix() {
            const clusterId = this.data.get('clusterId');
            const name = this.data.get('name');
            return `name=${name}&clusterId=${clusterId}`;
        },
        monitorSuffix() {
            const clusterId = this.data.get('clusterId');
            const name = this.data.get('name');
            const topic = this.data.get('topic');
            return `name=${name}&clusterId=${clusterId}${topic ? `&topic=${topic}` : []}`;
        }
    };

    static filters: SanFilterProps = {
        show(active: string, key: string) {
            return active === key ? '' : 'display:none';
        }
    };

    inited() {
        const index = _.findIndex(Pages, page => page === this.data.get('route.path'));
        index > -1 && this.data.set('active', index + 1);

        window.addEventListener('hashchange', this.data.get('onRouterChange'));
    }

    attached() {
        this.getDetail();
        this.getDiskWhiteInfo();
        const time = this.data.get('active') === 11 ? MINUTE / 2 : MINUTE;
        timeCount = getTimer(() => this.onRefresh(false), time);
    }

    detached() {
        window.removeEventListener('hashchange', this.data.get('onRouterChange'));
        clearInterval(timeCount);
    }

    onRouterChange(target: HashChangeEvent) {
        const active = this.data.get('active');
        const index = _.findIndex(Pages, i => target.newURL.indexOf(i) > -1);
        index > -1 && index + 1 !== active && this.data.set('active', index + 1);
    }

    // 获取磁盘白名单信息
    getDiskWhiteInfo() {
        return api.getUserAcls({featureTypes: ['MultipleDisksWhitelist']})
            .then((target: {isExist: boolean}) => {
                this.data.set('isMultipleDisks', target.isExist);
            });
    }

    // 获取详情信息
    getDetail(): Promise<void> {
        return api.getClusterDetail(this.data.get('clusterId'), {})
            .then((detail: ClusterDetail) => {
                this.data.set('options', ClusterStatus.fromValue(detail.status));
                // eslint-disable-next-line max-len
                const includeSASL = _.includes(detail.authenticationModes, 'SASL_SCRAM') || _.includes(detail.authenticationModes, 'SASL_PLAIN');
                this.data.set('userManage', includeSASL);
                this.data.set('accessManage', detail.aclEnabled && includeSASL);
                this.data.set('detail', detail);
                this.data.set('loading', false);
                return Promise.resolve();
            });
    }

    // 刷新
    async onRefresh(loadStatus = true) {
        this.data.set('isLoading', loadStatus);
        try {
            await this.getDetail().then(() => {
                this.nextTick(() => {
                    const arr = Object.values(Refs);
                    const active = this.data.get('active');
                    const node: RefsClass = this.ref(arr[active - 1]);
                    if (node?.subTag === 'log') {
                        return;
                    }
                    const time = active === 11 ? MINUTE / 2 : MINUTE;
                    node.refreshInfo(loadStatus).then(() => {
                        if (loadStatus) {
                            Notification.success('刷新成功');
                            clearInterval(timeCount);
                            timeCount = getTimer(() => this.onRefresh(false), time);
                        }
                        });
                    });
                }).finally(() => this.data.set('isLoading', false));
        }
        catch {
            this.data.set('isLoading', false);
            detailError += 1;
            if (detailError >= 5) {
                Notification.error('刷新失败');
                detailError = 0;
                clearInterval(timeCount);
            }
        }
    }

    OpenNodeLog(target: {nodeId: string}) {
        this.data.set('active', 10);
        this.data.set('nodeId', target.nodeId);
    }

    // region 切换
    onRegionChange() {
        redirect(`#${ROUTE_PATH.clusterList}`);
    }
}
