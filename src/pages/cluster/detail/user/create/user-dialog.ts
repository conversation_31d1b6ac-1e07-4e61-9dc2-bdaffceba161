/**
 * user-dialog
 *
 * @file user-dialog.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {
    Dialog,
    Notification,
    Form,
    Input,
    Select,
    Checkbox
} from '@baidu/sui';
import api from '@/common/client';
import {OutlinedEye, OutlinedEyeClose} from '@baidu/sui-icon';
import {rsaEncrypt} from '@/common/util';

import './user-dialog.less';
import {IAuthenticationModeValue} from '@/common/enums/constant';

export default class UserDialog extends Component {
    static template = html`
    <template>
        <s-dialog
            class="cluster-user-dialog ue-dialog-shot"
            open="{= open =}"
            on-confirm="confirm"
            on-close="close"
            loadingAfterConfirm="{{false}}"
            confirming="{{confirming}}"
            title="{{title}}"
            width="{{800}}">
            <s-form
                data="{= formData =}"
                s-ref="form"
                label-align="left"
                class="form-format"
                rules="{{rules}}">
                <s-form-item
                    s-if="isCreate"
                    prop="username"
                    label="用户名:"
                    help="英文字母开头，4～16位字符，只能包含英文字母，数字，下划线(_)">
                    <s-input
                        placeholder="请输入用户名称"
                        width="{{inputWidth}}"
                        value="{= formData.username =}">
                    </s-input>
                </s-form-item>
                <s-form-item
                    s-else
                    class="stringfy-form-item"
                    label="用户名:">
                    <div>{{formData.username}}</div>
                </s-form-item>
                <s-form-item
                    prop="password"
                    label="密码:"
                    help="8～32位字符，英文字母、数字和符号必须同时存在，符号仅限!@#$%^*()">
                    <s-input
                        placeholder="请输入密码"
                        type="{{type1}}"
                        width="{{250}}"
                        value="{= formData.password =}">
                        <s-icon-eye
                            s-if="{{type1 === 'text'}}"
                            on-click="onPwdClick('type1', 'password')"
                            slot="suffix"
                        />
                        <s-icon-eye-close
                            s-else
                            on-click="onPwdClick('type1', 'text')"
                            slot="suffix"
                        />
                    </s-input>
                </s-form-item>
                <s-form-item
                    prop="confirmPassword"
                    label="确认密码:" >
                    <s-input
                        placeholder="请再次输入密码"
                        type="{{type2}}"
                        width="{{250}}"
                        value="{= formData.confirmPassword =}">
                        <s-icon-eye
                            s-if="{{type2 === 'text'}}"
                            on-click="onPwdClick('type2', 'password')"
                            slot="suffix"
                        />
                        <s-icon-eye-close
                            s-else
                            on-click="onPwdClick('type2', 'text')"
                            slot="suffix"
                        />
                    </s-input>
                </s-form-item>
                <s-form-item
                    s-if="isCreate"
                    label="认证机制:"
                    prop="saslMechanisms"
                    class="form-item-mechanisms"
                >
                    <s-checkbox-group
                        datasource="{{saslMechanismsDatasource}}"
                        value="{= formData.saslMechanisms =}"
                    />
                </s-form-item>
            </s-form>
        </s-dialog>
    </template>
    `;
    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-textarea': Input.TextArea,
        's-select': Select,
        's-icon-eye': OutlinedEye,
        's-icon-eye-close': OutlinedEyeClose,
        's-checkbox-group': Checkbox.CheckboxGroup
    };
    static computed: SanComputedProps = {
        title() {
            return this.data.get('mode') === 'create' ? '创建用户' : '重置密码';
        },
        isCreate() {
            const mode = this.data.get('mode');
            return mode === 'create';
        }
    };

    initData() {
        return {
            mode: 'create',
            open: true,
            type1: 'password',
            type2: 'password',
            confirming: false,
            inputWidth: 280,
            saslMechanismsDatasource: [],
            formData: {
                username: '',
                password: '',
                confirmPassword: '',
                saslMechanisms: ''
            },
            rules: {
                username: [
                    {required: true, message: '请输入名称'},
                    {
                        validator(rule: any, value: string, callback: Function) {
                            let pattern = /^[a-zA-Z][\w]{3,15}$/;
                            if (!pattern.test(value)) {
                                return callback('英文字母开头，4～16位字符，只能包含英文字母，数字，下划线(_)');
                            }
                            callback();
                        }
                    }
                ],
                password: [
                    {required: true, message: '请输入密码'},
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            let pattern = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^*()])[a-zA-Z\d!@#$%^*()]{8,32}$/;
                            if (!pattern.test(value)) {
                                return callback('8～32位字符，英文字母、数字和符号必须同时存在，符号仅限!@#$%^*()');
                            }
                            callback();
                        }
                    }
                ],
                confirmPassword: [
                    {required: true, message: '请再次输入密码'},
                    {
                        validator: (rule: any, value: string, callback: Function) => {
                            if (value !== this.data.get('formData.password')) {
                                return callback('两次密码输入不一致');
                            }
                            callback();
                        }
                    }
                ]
            }
        };
    }

    attached() {
        const isCreate = this.data.get('isCreate');
        isCreate && this.initSaslMechanisms();
    }

    /** 初始化认证机制 */
    initSaslMechanisms() {
        const detail = this.data.get('detail');
        const {authenticationModes} = detail;
        const hasSCRAM = authenticationModes.some((item: IAuthenticationModeValue) => (
            item === IAuthenticationModeValue.SASL_SCRAM
        ));
        const hasPLAIN = authenticationModes.some((item: IAuthenticationModeValue) => (
            item === IAuthenticationModeValue.SASL_PLAIN
        ));
        const PlainItem = {label: 'PLAIN', value: 'PLAIN', disabled: true};
        const Scram_Arr = [
            {label: 'SCRAM-SHA-256', value: 'SCRAM-SHA-256'},
            {label: 'SCRAM-SHA-512', value: 'SCRAM-SHA-512', disabled: true},
        ];
        let datasource: typeof Scram_Arr = [];

        if (hasPLAIN && hasSCRAM) {
            datasource = [PlainItem, ...Scram_Arr];
        }
        else if (hasPLAIN && !hasSCRAM) {
            datasource = [PlainItem];
        }
        else if (hasSCRAM && !hasPLAIN) {
            datasource = Scram_Arr;
        }

        let initSaslMechanisms: string[] = [];
        datasource.forEach(item => {
            item.disabled && initSaslMechanisms.push(item.value);
        });
        this.data.set('saslMechanismsDatasource', datasource);
        this.data.set('formData.saslMechanisms', initSaslMechanisms);
    }

    async confirm() {
        try {
            this.data.set('confirming', true);
            await (this.ref('form') as unknown as Form).validateFields();
            const isCreate = this.data.get('isCreate');
            const {username, password, saslMechanisms} = this.data.get('formData');
            if (isCreate) {
                const params = {
                    username,
                    password: rsaEncrypt(password),
                    saslMechanisms
                };
                await api.createClusterUser(this.data.get('clusterId'), params);
            }
            else {
                await api.updateClusterUser(this.data.get('clusterId'), username, {password: rsaEncrypt(password)});
            }
            Notification.success(isCreate ? '创建成功' : '密码重置成功');
            this.fire('success', {});
            this.close();
        }
        catch (e: any) {
            Notification.error(e.message?.field?.Username);
        }
        finally {
            this.data.set('confirming', false);
        }
    }

    onPwdClick(value: string, type: string) {
        this.data.set(`${value}`, type);
    }

    close() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
