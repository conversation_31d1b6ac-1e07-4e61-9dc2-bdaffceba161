/**
 * 用户管理
 *
 * @file list.ts
 * <AUTHOR>
 */

import {html} from '@baiducloud/runtime';
import {Dialog, Pagination, Table, Notification, Button} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';
import CreateBtn from '@/components/create-btn';
import PaginationTable from '@/components/common-table/pagination-table';

import EllipsisTip from '@/components/ellipsis-tip';
import {TABLE_SUI, PAGER_SUI} from '@/common/config';
import {formatTime} from '@/common/util';
import Permission from '@/common/permission';

import api from '@/common/client';

import UserDialog from './create/user-dialog';

const klass = 'bms-cluster-detail-user';

type ClusterUserItem = {
    username: string;
    createTime: string;
    saslMechanisms: string[];
};

export default class UserList extends PaginationTable implements DetailRefresh {

    searchKey = 'username';

    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">用户列表</h2>
            </div>
            <div slot="bulk">
                <create-btn
                    text="创建用户"
                    on-click="onCreate"
                    disabled="{{detail.status | createDisabled}}"
                />
            </div>
            <div slot="filter">
                <s-searchbox
                    class="searchbox"
                    placeholder="请输入用户名进行搜索"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="{{170}}"
                >
                    <div slot="prefix"></div>
                </s-searchbox>
                <s-button on-click="getComList" class="ml5 s-icon-button">
                    <s-icon-refresh />
                </s-button>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}">
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{detail.status | disabledReset}}"
                            on-click="resetPassword(row)">
                            重置密码
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{detail.status | disabledDelete}}"
                            on-click="onDelete(row)">
                            删除
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty
                        vertical
                    >
                        <span slot="action">
                            <s-button
                                skin="stringfy"
                                on-click="onCreate"
                                disabled="{{detail.status | createDisabled}}">
                                马上创建>
                            </s-button>
                        </span>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'ellipsis-tip': EllipsisTip,
        'create-btn': CreateBtn,
        's-icon-refresh': OutlinedRefresh,
        's-empty': Empty
    };

    initData() {
        return {
            searchbox: {
                keyword: ''
            },
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'username',
                        label: '用户名'
                    },
                    {
                        name: 'saslMechanisms',
                        label: '认证机制',
                        width: 300,
                    },
                    {
                        name: 'createTime',
                        label: '创建时间',
                        render: (item: ClusterUserItem) => formatTime(item.createTime)
                    },
                    {
                        name: 'operation',
                        label: '操作',
                        width: 280
                    }
                ]
            },
            pager: {...PAGER_SUI}
        };
    }

    static filters = {
        createDisabled(state: string) {
            return !Permission.CLUSTER.USER.canCreate(state);
        },
        disabledReset: Permission.CLUSTER.USER.disabledReset,
        disabledDelete: Permission.CLUSTER.USER.disabledDelete
    };

    attached() {
        this.getComList();
    }

    async getNewTableList() {
        return await api.getClusterUserList(this.data.get('clusterId'), {});
    }

    // 新建用户
    onCreate() {
        const dialog = new UserDialog({
            data: {
                clusterId: this.data.get('clusterId'),
                detail: this.data.get('detail')
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.resetTable());
    }

    // 重置密码
    resetPassword(row: ClusterUserItem) {
        const dialog = new UserDialog({
            data: {
                mode: 'edit',
                clusterId: this.data.get('clusterId'),
                formData: {
                    username: row.username
                }
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => this.getComList());
    }

    // 删除
    onDelete(row: ClusterUserItem) {
        Dialog.warning({
            content: '您确定删除此用户吗？',
            okText: '确定',
            onOk: async () => {
                await api.deleteClusterUser(this.data.get('clusterId'), row.username);
                Notification.success('删除成功');
                this.resetTable();
            }
        });
    }

    // refresh更新
    refreshInfo() {
        return this.getComList();
    }
}
