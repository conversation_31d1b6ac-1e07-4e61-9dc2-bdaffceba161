/**
 * 消息详情弹窗
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Notification, Button, Form, Input} from '@baidu/sui';
import {OutlinedCopy, OutlinedDownload} from '@baidu/sui-icon';
import {copyDashboard} from '@/common/util';
import {COLOR_CONF} from '@/common/config';
import OperationContent from '@/components/operation-content';
import {formatTime} from '@/common/util';
import api from '@/common/client';

import './index.less';
const klass = 'message-detail';
export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            title="消息详情"
            open="{{dialogOpen}}"
            class="${klass}"
            otherClose="{{false}}"
            maskClose="{{false}}"
            getContainer="{{getContainer}}"
            size="{{650}}"
        >
            <s-form label-align="left" class="form-item-center message-form">
                <s-form-item prop="topicName" label="主题名称：">
                    {{message.topicName}}
                </s-form-item>
                <s-form-item prop="partitionId" label="所在分区：">
                    {{message.partitionId}}
                </s-form-item>
                <s-form-item prop="offset" label="当前位点：">
                    {{message.offset}}
                </s-form-item>
                <s-form-item prop="timestamp" label="创建时间：">
                    {{message.timestamp | formatTime}}
                </s-form-item>
                <s-form-item label="消息正文：" class="message-copy-area">
                    <span class="value mr8">消息大小：{{message.size}} Bytes</span>
                    <span on-click="copyText">
                        <s-copy color="${COLOR_CONF.defaultColor}"/>
                        复制消息
                    </span>
                    <span on-click="onDownLoad" class="ml16">
                        <s-download color="${COLOR_CONF.defaultColor}"/>
                        下载消息
                    </span>
                </s-form-item>
                <s-form-item class="panel-item">
                    <div class="message-panel">
                        <div class="message-item">
                            <span class="key">Key:</span>
                            <span class="value">{{message.key}}</span>
                        </div>
                        <div class="message-item">
                            <span class="key">Value:</span>
                            <s-textarea
                                width="400"
                                height="300"
                                readonly
                                class="value"
                                value="{{message.value | filterValue}}"
                            >
                            </s-textarea>
                        </div>
                        <div class="message-item">
                            <span class="key">Header:</span>
                            <operation-content
                                width="{{400}}"
                                height="{{200}}"
                                data="{{message.headers | filterHeader}}"
                            ></operation-content>
                        </div>
                    </div>
                </s-form-item>
            </s-form>

        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-button': Button,
        's-form': Form,
        's-form-item': Form.Item,
        's-copy': OutlinedCopy,
        's-download': OutlinedDownload,
        'operation-content': OperationContent,
        's-textarea': Input.TextArea,
    };

    initData() {
        return {
            dialogOpen: true
        };
    }

    static filters: SanFilterProps = {
        filterHeader(header: string) {
            return JSON.stringify(header, null, '\t');
        },
        filterValue(value: string) {
            const length = value.length;
            return length > 1000 ? value.substring(0, 1000) + '...' : value;
        },
        formatTime
    };

    copyText() {
        const {message = ''} = this.data.get('');
        copyDashboard(JSON.stringify(message));
        Notification.success('消息复制成功!');
    }

    onDownLoad() {
        const {message = ''} = this.data.get('');
        const data = JSON.stringify(message, null, '\t');
        const url = window.URL || window.webkitURL || window;
        const blob = new Blob([data]);
        const a = document.createElement('a');
        a.href = url.createObjectURL(blob);
        // 设置 download 属性
        a.download = '消息详情.json';
        a.click();
    }

    // 取消
    onClose() {
        this.data.set('dialogOpen', false);
        this.dispose && this.dispose();
    }
}
