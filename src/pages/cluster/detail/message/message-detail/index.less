@klass: message-detail;
@keyColor: #5C5F66;
@valueColor: #151B26;
@copyColor: #2468F2;
.@{klass} {
    .message-panel {
        border: 1px solid rgba(232,233,235,1);
        border-radius: 4px;
        padding: 16px;
        min-width: 476px;
        margin-left: 83px;
        .message-item {
            line-height: 20px;
            font-weight: 400;
            font-size: 12px;
            margin-top: 16px;
            display: flex;
            align-items: flex-start;
            .key {
                color: @keyColor;
                margin-right: 8px;
                width: 45px;
                display: inline-block;
            }
            .value {
                color: @valueColor;
                max-width: 400px;
                word-wrap: break-word;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 10;
            }

            &:first-child {
                margin-top: 0px;
            }
        }
    }
    .message-copy-area {
        color: @copyColor;
        .value {
            color: @valueColor;
            cursor: auto;
        }
        cursor: pointer;
    }
    .message-form {
        .s-form-item {
            margin-bottom: 16px;

            .s-form-item-label>label {
                padding-left: 0;
            }
        }
        .panel-item {
            margin-top: -4px;
        }
    }
}
