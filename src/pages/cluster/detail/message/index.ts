/**
 * 消息查询
 *
 * @file message.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Pagination, Table, Button, Select, InputNumber, DatePicker, Alert, Tooltip} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import {OutlinedDownload} from '@baidu/sui-icon';
import CommanTable from '@/components/common-table/index';
import {QueryType} from '@/common/enums/index';
import MessageDetail from './message-detail';
import tipSelect from '@/components/tip-select';
import {TABLE_SUI} from '@/common/config';
import {formatTime} from '@/common/util';
import api from '@/common/client';
import './index.less';
const klass = 'bms-cluster-detail-message';

interface ClusterMessageItem {
    topicName: string;
    partitionId: number;
    offset: number;
    timestamp: number;
    key: string;
    value: string;
    size: number;
    headers: Array<{
        key: string;
        value: string;
    }>;
};

interface OffsetType {
    partitionId: number;
    topicName: string;
    minOffset: number;
    maxOffset: number;
};

export default class Message extends CommanTable implements DetailRefresh {

    static template = html`
    <div class="${klass} bms-list-page bms-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle">
                <h2 class="title">
                    消息查询
                    <s-alert skin="warning" class="title-alert">
                        支持查询结果最多显示20条消息，且总大小不能超过10MB。
                    </s-alert>
                </h2>
            </div>
            <div slot="bulk">
                <span class="mr8 message-label">主题：</span>
                <s-select
                    datasource="{{topicList}}"
                    value="{{topic}}"
                    disabled="{{topicDisabled}}"
                    filterable
                    width="120"
                    on-change="onTopicChange"
                />
                <span class="mr8 ml16 message-label">查询方式：</span>
                <s-select
                    datasource="{{queryType}}"
                    value="{{type}}"
                    width="120"
                    on-change="onTypeChange"
                />
                <span class="mr8 ml16 message-label">分区ID：</span>
                <s-select
                    datasource="{{partitionList}}"
                    value="{{partition}}"
                    width="120"
                    filterable
                    on-change="onPartitionChange"
                />
                <template
                    s-if="type === 'byTime'"
                >
                    <span class="mr8 ml16 message-label">起始时间：</span>
                    <s-date-picker value="{=startTime=}" mode="second" />
                </template>
                <template s-else>
                    <span class="mr8 ml16 message-label">起始位点：</span>
                    <s-tooltip s-if="showInputQuery" content="{{'取值范围为' + minOffset + '到' + (maxOffset - 1)}}">
                        <s-inputnumber
                            value="{=startOffset=}"
                            width="120"
                            min="{{minOffset}}"
                            max="{{maxOffset - 1}}"
                        />
                    </s-tooltip>
                    <s-inputnumber
                        s-else
                        value="{=startOffset=}"
                        width="120"
                        min="{{minOffset}}"
                        max="{{maxOffset - 1}}"
                    />
                </template>
                <s-tooltip s-if="disableQuery" content="{{queryTip}}">
                    <s-button
                        skin="primary"
                        class="ml16"
                        width="34"
                        loading="{{btnLoading}}"
                        disabled="{{true}}"
                        on-click="onQuery">
                        查询
                    </s-button>
                </s-tooltip>
                <s-button
                    s-else
                    skin="primary"
                    class="ml16"
                    width="34"
                    loading="{{btnLoading}}"
                    on-click="onQuery">
                    查询
                </s-button>
                <s-button
                    skin="normal"
                    class="ml8"
                    width="34"
                    on-click="onReset">
                    重置
                </s-button>
            </div>
            <div slot="filter">
                <s-tooltip content="{{downloadTip}}">
                    <s-button on-click="downLoadMessage" class="s-icon-button" disabled="{{disableDownLoad}}">
                        <s-icon-download />
                    </s-button>
                </s-tooltip>
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}">
                <div slot="c-timestamp">{{row.timestamp | formatTime}}</div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onView($event, row)">
                            查看
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty vertical actionText=""/>
                </div>
            </s-table>
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        's-icon-download': OutlinedDownload,
        's-empty': Empty,
        's-select': Select,
        's-inputnumber': InputNumber,
        's-date-picker': DatePicker,
        's-alert': Alert,
        's-tooltip': Tooltip,
        'tip-select': tipSelect
    };

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: [
                    {
                        name: 'partitionId',
                        label: '分区ID',
                    },
                    {
                        name: 'offset',
                        label: '位点',
                    },
                    {
                        name: 'timestamp',
                        label: '创建时间'
                    },
                    {
                        name: 'operation',
                        label: '操作'
                    }
                ]
            },
            queryType: QueryType.toArray(),
            minOffset: 0,
            maxOffset: 1,
            showInputQuery: false,
            originalList: [],
            topicList: [],
            topic: ''
        };
    }

    static filters = {
        formatTime
    };

    static computed: SanComputedProps = {
        disableQuery(): boolean {
            const timer = this.data.get('timer');
            const topic = this.data.get('topic');
            const type = this.data.get('type');
            const partition = this.data.get('partition');
            const isNotFullInput = !topic || !type || partition === '';
            return timer || isNotFullInput;
        },
        disableDownLoad(): boolean {
            const datasource = this.data.get('table.datasource');
            const downloadTimer = this.data.get('downloadTimer');
            return datasource.length === 0 || downloadTimer;
        },
        queryTip(): string {
            const timer = this.data.get('timer');
            const tip = timer ? '每10s可查询一次' : '请补全信息后查询';
            return tip;
        },
        downloadTip(): string {
            const downloadTimer = this.data.get('downloadTimer');
            const tip = downloadTimer ? '每10s可下载一次' : '下载查询结果';
            return tip;
        }
    };

    async attached() {
        const {topic, topicDisabled} = this.data.get('');
        if (topic && topicDisabled) {
            this.data.set('topicList', [{text: topic, value: topic}]);
            this.watch('topic', async () => await this.getOffsets());
            await this.getOffsets();
        }
        else if (topic && !topicDisabled) {
            await this.getTopicList();
            await this.getOffsets();
        }
        else if (!topicDisabled) {
            await this.getTopicList();
        }
        await this.getComList();
    }

    async getOffsets() {
        const {clusterId, topic: topicName} = this.data.get('');
        const result = await api.listTopicOffset(clusterId, topicName, {});
        this.data.set('originalList', result);
        if (this.data.get('type')) {
            this.onTypeChange({value: this.data.get('type')});
        }
    }

    async getTopicList() {
        await api.listClusterTopic(this.data.get('clusterId'), {})
            .then((target: Array<{topicName: string}>) => {
                const arr = _.map(target, s => ({text: s.topicName, value: s.topicName}));
                this.data.set('topicList', arr);
            });
    }

    /**
     * 获取表格，重写是为了支持自动刷新不展示loading
     */
    async getComList(loadStatus = true) {
        this.data.set('table.loading', loadStatus);
        this.data.set('table.error', false);
        try {
            await this.getTableList();
            this.data.set('table.loading', false);
        }
        catch (error) {
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    async getTableList() {}

    async onTopicChange(target: {value: string}) {
        this.data.set('topic', target.value);
        this.data.set('type', '');
        this.data.set('partition', '');
        this.data.set('minOffset', 0);
        this.data.set('maxOffset', 1);
        this.data.set('startOffset', '');
        this.data.set('showInputQuery', false);
        if (!this.data.get('topicDisabled') && this.data.get('topic') !== '') {
            await this.getOffsets();
        }
    }

    async onTypeChange(target: {value: string}) {
        this.data.set('type', target.value);
        const originalList = this.data.get('originalList');
        const partitionList = originalList.map((item: OffsetType) => ({
            text: item.minOffset !== item.maxOffset ? item.partitionId : item.partitionId + '(分区暂无消息)',
            value: item.partitionId,
            disabled: item.minOffset === item.maxOffset
        }));
        this.data.set('partitionList', partitionList);
        if (target.value === 'byTime') {
            const index = _.findIndex(partitionList, (item: {text: string, value: number, disabled: boolean}) => {
                return !item.disabled;
            });
            partitionList.splice(0, 0, {text: '全部', value: -1, disabled: index === -1});
            this.data.set('partitionList', partitionList);
            if (index !== -1) {
                this.data.set('partition', -1);
                const now = new Date().getTime();
                this.data.set('startTime', new Date(now));
            } else {
                this.data.set('partition', '');
            }
        } else {
            this.data.set('partitionList', partitionList);
            const index = _.findIndex(partitionList, (item: {text: string, value: number, disabled: boolean}) => {
                return !item.disabled;
            });
            if (index > -1) {
                const partition = partitionList[index].value;
                this.onPartitionChange({value: partition});
            }
            else {
                this.data.set('partition', '');
                this.data.set('minOffset', 0);
                this.data.set('maxOffset', 1);
                this.data.set('startOffset', '');
                this.data.set('showInputQuery', false);
            }
        }
    }

    onPartitionChange(target: {value: number}) {
        this.data.set('partition', target.value);
        const {originalList} = this.data.get('');
        const index = _.findIndex(originalList, (item: OffsetType) => item.partitionId === target.value);
        this.data.set('showInputQuery', true);
        this.data.set('minOffset', originalList[index].minOffset);
        this.data.set('maxOffset', originalList[index].maxOffset);
        this.data.set('startOffset', originalList[index].minOffset);
    }

    async onQuery() {
        const {clusterId, topic: topicName, partition: partitionId, type, startTime, startOffset} = this.data.get('');
        let params = {};
        const time = new Date(startTime).getTime();
        if (type === 'byTime') {
            params = {
                partitionId,
                queryType: type,
                startTime: time > 0 ? time : 0
            };
        } else {
            params = {
                partitionId,
                queryType: type,
                startOffset
            };
        }
        this.data.set('btnLoading', true);
        this.data.set('table.loading', true);
        const result = await api.listMessages(clusterId, topicName, params);
        this.data.set('table.datasource', result);
        this.data.set('btnLoading', false);
        this.data.set('table.loading', false);
        this.data.set('timer', true);
        setTimeout(() => this.data.set('timer', false), 10 * 1000);
    }

    onReset() {
        if (!this.data.get('topicDisabled')) {
            this.data.set('topic', '');
        }
        this.data.set('type', '');
        this.data.set('partition', '');
        this.data.set('startTime', '');
        this.data.set('startOffset', '');
        this.data.set('minOffset', 0);
        this.data.set('maxOffset', 1);
        this.data.set('table.datasource', []);
        this.data.set('showInputQuery', false);
    }

    onView(event: Event, row: ClusterMessageItem) {
        event.stopPropagation();
        const dialog = new MessageDetail({data: {message: row}});
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    downLoadMessage() {
        const message = this.data.get('table.datasource');
        const data = JSON.stringify(message, null, '\t');
        const url = window.URL || window.webkitURL || window;
        const blob = new Blob([data]);
        const a = document.createElement('a');
        a.href = url.createObjectURL(blob);
        // 设置 download 属性
        a.download = '消息.json';
        a.click();
        this.data.set('downloadTimer', true);
        setTimeout(() => this.data.set('downloadTimer', false), 10 * 1000);
    }

    // refresh更新
    refreshInfo() {
        return this.getComList();
    }
}
