/**
 * 激活页
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {Notification, Alert} from '@baidu/sui';
import {AppOrderPage} from '@baidu/sui-biz';
import {Component} from 'san';

import {DOCS_LINK, ROUTE_PATH} from '@/common/config';
import api from '@/common/client';
import checkActive from '@/common/check-active';

import './index.less';
const klass = 'bms-active';

const $flag = ServiceFactory.resolve('$flag');

const isXushang = $flag.KafkaXushang;
const isPrivate = $flag.KafkaPublicTopicMonitor;

@decorators.asPage(ROUTE_PATH.newActive)
export default class extends Component {
    static template = html`
    <div>
        <div class="${klass}-order-container">
            <div class="${klass}_verify_tip" s-if="{{!isVerify && !isPrivate && !loadingAccountInfo}}">
                <s-alert skin="warning">
                    产品开通提示：如需要开通消息服务，请先进行<a
                        href="/qualify/#/qualify/index"
                        target="blank">实名认证</a>，本产品开通并授权后才会开始服务计费。
                </s-alert>
            </div>
            <s-order-page
                class="${klass}-s-order-page"
                title="{{title}}"
                bgImgSrc="{{bgImgSrc}}"
                desc="{{desc}}"
                process="{{process}}"
                scene="{{scene}}"
                advantage="{{advantage}}"
                feature="{{feature}}"
                relation="{{relation}}"
                guide="{{guide}}"
                useNewVersion="{{true}}"
                openBtnDisabled="{{openBtnDisabled}}"
                on-click="onOrderCreate"
            >
                <span slot="tip">{{tip | raw}}</span>
            </s-order-page>
        </div>
    </div>`;

    static components = {
        's-order-page': AppOrderPage,
        's-alert': Alert
    };

    static computed: SanComputedProps = {
        openBtnDisabled() {
            const notVerify = !this.data.get('isVerify') && !isPrivate;
            const loadingAccountInfo = this.data.get('loadingAccountInfo');
            return notVerify && !loadingAccountInfo;
        },
        regionAlert() {
            const regionTip = '消息服务暂不支持 华东-上海 区域，请有需求的用户选择 华东-苏州 区域，如有不便敬请谅解。';
            return isXushang ? regionTip : '百度' + regionTip;
        },
        desc() {
            const des1 = '消息服务（Messaging System BMS）是全兼容Apache Kafka的分布式、高可扩展、高通量的托管消息队列服务，您可以直接享用Kafka带来的先进功能而无需考虑集群运维，并按照使用量付费。';
            const des2 = '百度消息服务（Baidu Messaging System BMS）是全兼容Apache Kafka的分布式、高可扩展、高通量的托管消息队列服务，您可以直接享用Kafka带来的先进功能而无需考虑集群运维，并按照使用量付费。';
            return isXushang ? des1 : des2;
        },
        tip() {
            const tip1 = `百度消息服务 for Kafka 产品需同时开通云服务器 BCC、云磁盘 CDS、弹性网卡 EIP 3个产品。具体产品信息可查看<a href="${DOCS_LINK.kafkaOrdinaryHelp}">帮助文档</a>。产品支持后付费和预付费，付费信息可查看<a href="${DOCS_LINK.kafkaCharging}">计费规则</a>。`;
            const tip2 = '消息服务 for Kafka 产品需同时开通云服务器 BCC、云磁盘 CDS、弹性网卡 EIP 3个产品。';
            return isXushang ? tip2 : tip1;
        },
        title() {
            const title1 = '消息服务 for Kafka 高吞吐、可扩展的分布式消息服务';
            const title2 = '百度消息服务 for Kafka 高吞吐、可扩展的分布式消息服务';
            return isXushang ? title1 : title2;
        },
    };

    initData() {
        return {
            bgImgSrc: require('../../static/background.png'),
            advantage: {
                content: [
                    {
                        title: "开箱即用",
                        desc: "100%兼容开源社区Kafka，开通服务后便能创建Kafka主题，并利用Kafka客户端与消息服务通讯。",
                        imgSrc: require('../../static/advantage1.png'),
                    },
                    {
                        title: "实时处理",
                        desc: "您可以连续收集海量数据，以方便对事件、设备、业务等做出实时反应。",
                        imgSrc: require('../../static/advantage2.png'),
                    },
                    {
                        title: "弹性计算",
                        desc: "消息服务可以按需扩容，在灵活方便的同时大大降低了用户使用成本。",
                        imgSrc: require('../../static/advantage3.png'),
                    },
                    {
                        title: "高可用性",
                        desc: "消息服务 提供SLA保证服务的可用性。此外，数据提供多副本的方式保证数据的持久性。",
                        imgSrc: require('../../static/advantage4.png'),
                    }
                ]
            },
            isReOrder: false,
            isVerify: false,
            loadingAccountInfo: false
        };
    }

    inited() {
        this.getAccount();
    }


    // 获取用户信息
    getAccount() {
        this.data.set('loadingAccountInfo', true);
        api.iamAccountDetail()
            .then((target: {accountType: 'individual' | 'enterprise'}) => {
                    this.data.set('accountType', target.accountType);
                    this.data.set('isVerify',
                        target.accountType === 'enterprise'
                            ? this.$context.isEnterprise()
                            : this.$context.isVerifyUser());
                    this.data.set('loadingAccountInfo', false);
            })
            .catch((e: Error) => this.data.set('loadingAccountInfo', false));
    }

    // 服务开通
    onOrderCreate() {
        api.bpsOrderCreate({})
            .then(() => {
                Notification.success('开通成功');
                redirect(`#${ROUTE_PATH.clusterList}`);
            })
            .catch(() => this.data.set('isReOrder', true));
    }

    // region 切换
    onRegionChange() {
        this.getAccount();
        checkActive();
    }
}
