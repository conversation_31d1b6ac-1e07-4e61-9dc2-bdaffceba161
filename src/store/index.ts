/* eslint-disable max-len */
/**
 * store
 *
 * @file index.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {Store, connect} from 'san-store';
import {builder} from 'san-update';

const originalconsumerIndicators = {
    basicIndicators: {
        comman: [
            {label: '消息滞后量', value: 'ConsumerOffsetLag', alias: 'ConsumerOffsetLag', disabled: true, checked: true, unit: '条'},
            {label: '日志最大 Offset', value: 'ConsumerLogMaxOffset', alias: 'ConsumerLogMaxOffset', unit: '条'},
            {label: '当前消费 Offset', value: 'ConsumerOffsetConsumed', alias: 'ConsumerOffsetConsumed', unit: '条'},
            {label: '消费消息速率', value: 'ConsumerMessagesOutPerSec', alias: 'ConsumerMessagesOutPerSec', unit: '条/s'},
        ]
    },
    topicIndicators: {
        comman: [
            {label: '消息滞后量', value: 'TopicOffsetLag', alias: 'TopicOffsetLag', disabled: true, checked: true, unit: '条'},
            {label: '日志最大 Offset', value: 'TopicLogMaxOffset', alias: 'TopicLogMaxOffset', checked: true, unit: '条'},
            {label: '当前消费 Offset', value: 'TopicOffsetConsumed', alias: 'TopicOffsetConsumed', checked: true, unit: '条'},
            {label: '消费消息速度', value: 'TopicMessagesOutPerSec', alias: 'TopicMessagesOutPerSec', unit: '条/s'},
        ]
    },
    partitionIndicators: {
        comman: [
            {label: '消息滞后量', value: 'OffsetLag', alias: 'OffsetLag', disabled: true, checked: true, unit: '条'},
            {label: '日志最大 Offset', value: 'LogMaxOffset', alias: 'LogMaxOffset', checked: true, unit: '条'},
            {label: '当前消费 Offset', value: 'OffsetConsumed', alias: 'OffsetConsumed', checked: true, unit: '条'},
            {label: '消费消息速度', value: 'MessagesOutPerSec', alias: 'MessagesOutPerSec', unit: '条/s'}
        ]
    },
};

const originalclusterIndicators = {
    // 常用
    comman: [
        {label: '生产消息速率', value: 'ClusterMessagesInPerSec', alias: 'ClusterMessagesInPerSec', disabled: true, checked: true, unit: '条/s'},
        {label: '生产消息流量', value: 'ClusterBytesInPerSec', alias: 'ClusterBytesInPerSec', checked: true, unit: 'Bytes/s'},
        {label: '消费消息流量', value: 'ClusterBytesOutPerSec', alias: 'ClusterBytesOutPerSec', checked: true, unit: 'Bytes/s'},
        {label: '写入请求频率', value: 'ClusterTotalProduceRequestsPerSec', alias: 'ClusterTotalProduceRequestsPerSec', unit: '次/s'},
        {label: '读取请求频率', value: 'ClusterTotalFetchRequestsPerSec', alias: 'ClusterTotalFetchRequestsPerSec', unit: '次/s'},
    ],
    // 其他
    other: [
        {label: '活跃Controller节点个数', value: 'ClusterActiveControllerCount', alias: 'ClusterActiveControllerCount', unit: '个'},
        {label: '主题总数', value: 'ClusterTopicCount', alias: 'ClusterTopicCount', checked: true, unit: '个'},
        {label: 'Leader 分区总数', value: 'ClusterLeaderCount', alias: 'ClusterLeaderCount', unit: '个'},
        {label: '分区总数', value: 'ClusterPartitionCount', alias: 'ClusterPartitionCount', checked: true, unit: '个'},
        {label: '下线分区总数', value: 'ClusterOfflinePartitionsCount', alias: 'ClusterOfflinePartitionsCount', unit: '个'},
        {label: '磁盘空间使用量', value: 'ClusterDiskUsedBytes', alias: 'ClusterDiskUsedBytes', unit: 'Bytes'},
        {label: '数据盘空间使用量', value: 'ClusterDataDiskUsedBytes', alias: 'ClusterDataDiskUsedBytes', checked: true, unit: 'Bytes'},
        {label: '磁盘空间使用率', value: 'ClusterDiskUsedPercent', alias: 'ClusterDiskUsedPercent', unit: '%'},
        {label: '数据盘空间使用率', value: 'ClusterDataDiskUsedPercent', alias: 'ClusterDataDiskUsedPercent', checked: true, unit: '%'},
        {label: '最大磁盘空间使用率', value: 'ClusterMaxDiskUsedPercent', alias: 'ClusterMaxDiskUsedPercent', unit: '%'},
        {label: '最大数据盘空间使用率', value: 'ClusterMaxDataDiskUsedPercent', alias: 'ClusterMaxDataDiskUsedPercent', unit: '%'},
        {label: 'Broker进程死亡个数', value: 'ClusterBrokerDeadCount', alias: 'ClusterBrokerDeadCount', unit: '个'},
        {label: 'Zookeeper进程死亡个数', value: 'ClusterZookeeperDeadCount', alias: 'ClusterZookeeperDeadCount', unit: '个'},
        {label: '日志删除速率', value: 'ClusterDeleteSizeByThresholdPerSec', alias: 'ClusterDeleteSizeByThresholdPerSec', unit: 'Bytes/s'},
        {label: '日志文件删除速率', value: 'ClusterDeleteSegmentsByThresholdPerSec', alias: 'ClusterDeleteSegmentsByThresholdPerSec', unit: '个/s'},
        {label: '写入远程流量', value: 'ClusterRemoteCopyBytesPerSec', alias: 'ClusterRemoteCopyBytesPerSec', unit: 'Bytes/s'},
        {label: '读取远程流量', value: 'ClusterRemoteFetchBytesPerSec', alias: 'ClusterRemoteFetchBytesPerSec', unit: 'Bytes/s'},
        {label: '写入远程请求频率', value: 'ClusterRemoteCopyRequestsPerSec', alias: 'ClusterRemoteCopyRequestsPerSec', unit: '次/s'},
        {label: '读取远程请求频率', value: 'ClusterRemoteFetchRequestsPerSec', alias: 'ClusterRemoteFetchRequestsPerSec', unit: '次/s'},
        {label: '删除远程请求频率', value: 'ClusterRemoteDeleteRequestsPerSec', alias: 'ClusterRemoteDeleteRequestsPerSec', unit: '次/s'},
        {label: '远程日志大小', value: 'ClusterRemoteLogSizeBytes', alias: 'ClusterRemoteLogSizeBytes', unit: 'Bytes'},
        {label: '远程元数据数量', value: 'ClusterRemoteLogMetadataCount', alias: 'ClusterRemoteLogMetadataCount', unit: '个'}
    ]
};

const originaltopicIndicators = {
    basicIndicators: {
        comman: [
            {label: '生产消息速率', value: 'TopicMessagesInPerSec', alias: 'TopicMessagesInPerSec', checked: true, disabled: true, unit: '条/s'},
            {label: '生产消息流量', value: 'TopicBytesInPerSec', alias: 'TopicBytesInPerSec', checked: true, unit: 'Bytes/s'},
            {label: '消费消息流量', value: 'TopicBytesOutPerSec', alias: 'TopicBytesOutPerSec', checked: true, unit: 'Bytes/s'},
            {label: '写入请求频率', value: 'TopicTotalProduceRequestsPerSec', alias: 'TopicTotalProduceRequestsPerSec', unit: '次/s'},
            {label: '读取请求频率', value: 'TopicTotalFetchRequestsPerSec', alias: 'TopicTotalFetchRequestsPerSec', unit: '次/s'},
            {label: '失败写入请求频率', value: 'TopicFailedProduceRequestsPerSec', alias: 'TopicFailedProduceRequestsPerSec', unit: '次/s'},
            {label: '失败读取请求频率', value: 'TopicFailedFetchRequestsPerSec', alias: 'TopicFailedFetchRequestsPerSec', unit: '次/s'},
            {label: '日志删除速率', value: 'TopicDeleteSizeByThresholdPerSec', alias: 'TopicDeleteSizeByThresholdPerSec', unit: 'Bytes/s'},
            {label: '日志文件删除速率', value: 'TopicDeleteSegmentsByThresholdPerSec', alias: 'TopicDeleteSegmentsByThresholdPerSec', unit: '个/s'},
        ]
    },
    brokerIndicators: {
        comman: [
            {label: '生产消息速率', value: 'MessagesInPerSec', alias: 'MessagesInPerSec', disabled: true, checked: true, unit: '条/s'},
            {label: '生产消息流量', value: 'BytesInPerSec', alias: 'BytesInPerSec', checked: true, unit: 'Byte/s'},
            {label: '消费消息流量', value: 'BytesOutPerSec', alias: 'BytesOutPerSec', checked: true, unit: 'Byte/s'},
            {label: '拒绝写入流量', value: 'BytesRejectedPerSec', alias: 'BytesRejectedPerSec', unit: 'Byte/s'},
            {label: '写入请求频率', value: 'TotalProduceRequestsPerSec', alias: 'TotalProduceRequestsPerSec', unit: '次/s'},
            {label: '读取请求频率', value: 'TotalFetchRequestsPerSec', alias: 'TotalFetchRequestsPerSec', unit: '次/s'},
            {label: '失败写入请求频率', value: 'FailedProduceRequestsPerSec', alias: 'FailedProduceRequestsPerSec', unit: '次/s'},
            {label: '失败读取请求频率', value: 'FailedFetchRequestsPerSec', alias: 'FailedFetchRequestsPerSec', unit: '次/s'},
            {label: '写入消息格式转换率', value: 'ProduceMessageConversionsPerSec', alias: 'ProduceMessageConversionsPerSec', unit: '条/s'},
            {label: '读取消息格式转换率', value: 'FetchMessageConversionsPerSec', alias: 'FetchMessageConversionsPerSec', unit: '条/s'},
            {label: '日志删除速率', value: 'DeleteSizeByThresholdPerSec', alias: 'DeleteSizeByThresholdPerSec', unit: 'Bytes/s'},
            {label: '日志文件删除速率', value: 'DeleteSegmentsByThresholdPerSec', alias: 'DeleteSegmentsByThresholdPerSec', unit: '个/s'},
        ],
        other: [
            {label: '写入远程流量', value: 'RemoteCopyBytesPerSec', alias: 'RemoteCopyBytesPerSec', unit: 'Bytes/s'},
            {label: '读取远程流量', value: 'RemoteFetchBytesPerSec', alias: 'RemoteFetchBytesPerSec', unit: 'Bytes/s'},
            {label: '写入远程请求频率', value: 'RemoteCopyRequestsPerSec', alias: 'RemoteCopyRequestsPerSec', unit: '次/s'},
            {label: '读取远程请求频率', value: 'RemoteFetchRequestsPerSec', alias: 'RemoteFetchRequestsPerSec', unit: '次/s'},
            {label: '删除远程请求频率', value: 'RemoteDeleteRequestsPerSec', alias: 'RemoteDeleteRequestsPerSec', unit: '次/s'},
            {label: '失败写入远程请求频率', value: 'RemoteCopyErrorsPerSec', alias: 'RemoteCopyErrorsPerSec', unit: '次/s'},
            {label: '失败读取远程请求频率', value: 'RemoteFetchErrorsPerSec', alias: 'RemoteFetchErrorsPerSec', unit: '次/s'},
            {label: '失败删除远程请求频率', value: 'RemoteDeleteErrorsPerSec', alias: 'RemoteDeleteErrorsPerSec', unit: '次/s'},
            {label: '远程日志大小', value: 'RemoteLogSizeBytes', alias: 'RemoteLogSizeBytes', unit: 'Bytes'},
            {label: '远程元数据数量', value: 'RemoteLogMetadataCount', alias: 'RemoteLogMetadataCount', unit: '个'}
        ]
    },
    topicIndicators: {
        comman: [
            {label: '生产消息速率', value: 'MessagesInPerSec', alias: 'MessagesInPerSec', disabled: true, checked: true, unit: '条/s'},
            {label: '生产消息流量', value: 'BytesInPerSec', alias: 'BytesInPerSec', checked: true, unit: 'Byte/s'},
            {label: '消费消息流量', value: 'BytesOutPerSec', alias: 'BytesOutPerSec', checked: true, unit: 'Byte/s'},
            {label: '拒绝写入流量', value: 'BytesRejectedPerSec', alias: 'BytesRejectedPerSec', unit: 'Byte/s'},
            {label: '写入请求频率', value: 'TotalProduceRequestsPerSec', alias: 'TotalProduceRequestsPerSec', unit: '次/s'},
            {label: '读取请求频率', value: 'TotalFetchRequestsPerSec', alias: 'TotalFetchRequestsPerSec', unit: '次/s'},
            {label: '失败写入请求频率', value: 'FailedProduceRequestsPerSec', alias: 'FailedProduceRequestsPerSec', unit: '次/s'},
            {label: '失败读取请求频率', value: 'FailedFetchRequestsPerSec', alias: 'FailedFetchRequestsPerSec', unit: '次/s'},
            {label: '写入消息格式转换率', value: 'ProduceMessageConversionsPerSec', alias: 'ProduceMessageConversionsPerSec', unit: '条/s'},
            {label: '读取消息格式转换率', value: 'FetchMessageConversionsPerSec', alias: 'FetchMessageConversionsPerSec', unit: '条/s'},
            {label: '日志删除速率', value: 'DeleteSizeByThresholdPerSec', alias: 'DeleteSizeByThresholdPerSec', unit: 'Bytes/s'},
            {label: '日志文件删除速率', value: 'DeleteSegmentsByThresholdPerSec', alias: 'DeleteSegmentsByThresholdPerSec', unit: '个/s'},
        ],
        other: [
            {label: '写入远程流量', value: 'RemoteCopyBytesPerSec', alias: 'RemoteCopyBytesPerSec', unit: 'Bytes/s'},
            {label: '读取远程流量', value: 'RemoteFetchBytesPerSec', alias: 'RemoteFetchBytesPerSec', unit: 'Bytes/s'},
            {label: '写入远程请求频率', value: 'RemoteCopyRequestsPerSec', alias: 'RemoteCopyRequestsPerSec', unit: '次/s'},
            {label: '读取远程请求频率', value: 'RemoteFetchRequestsPerSec', alias: 'RemoteFetchRequestsPerSec', unit: '次/s'},
            {label: '删除远程请求频率', value: 'RemoteDeleteRequestsPerSec', alias: 'RemoteDeleteRequestsPerSec', unit: '次/s'},
            {label: '失败写入远程请求频率', value: 'RemoteCopyErrorsPerSec', alias: 'RemoteCopyErrorsPerSec', unit: '次/s'},
            {label: '失败读取远程请求频率', value: 'RemoteFetchErrorsPerSec', alias: 'RemoteFetchErrorsPerSec', unit: '次/s'},
            {label: '失败删除远程请求频率', value: 'RemoteDeleteErrorsPerSec', alias: 'RemoteDeleteErrorsPerSec', unit: '次/s'},
            {label: '远程日志大小', value: 'RemoteLogSizeBytes', alias: 'RemoteLogSizeBytes', unit: 'Bytes'},
            {label: '远程元数据数量', value: 'RemoteLogMetadataCount', alias: 'RemoteLogMetadataCount', unit: '个'}
        ]
    }
};

const originalbrokerIndicators = {
    // 服务监控
    serviceIndicators: {
        // 消息
        message: [
            {label: '生产消息速率', value: 'BrokerMessagesInPerSec', alias: 'BrokerMessagesInPerSec', disabled: true, checked: true, unit: '条/s'},
            {label: '生产消息流量', value: 'BrokerBytesInPerSec', alias: 'BrokerBytesInPerSec', checked: true, unit: 'Bytes/s'},
            {label: '消费消息流量', value: 'BrokerBytesOutPerSec', alias: 'BrokerBytesOutPerSec', checked: true, unit: 'Bytes/s'},
            {label: '副本同步流入流量', value: 'BrokerReplicationBytesInPerSec', alias: 'BrokerReplicationBytesInPerSec', unit: 'Bytes/s'},
            {label: '副本同步流出流量', value: 'BrokerReplicationBytesOutPerSec', alias: 'BrokerReplicationBytesOutPerSec', unit: 'Bytes/s'},
            {label: '重新分区流入流量', value: 'BrokerReassignmentBytesInPerSec', alias: 'BrokerReassignmentBytesInPerSec', unit: 'Bytes/s'},
            {label: '重新分区流出流量', value: 'BrokerReassignmentBytesOutPerSec', alias: 'BrokerReassignmentBytesOutPerSec', unit: 'Bytes/s'},
            {label: '拒绝写入流量', value: 'BrokerBytesRejectedPerSec', alias: 'BrokerBytesRejectedPerSec', unit: 'Bytes/s'},
            {label: '写入请求频率', value: 'ProduceRequestsPerSec', alias: 'ProduceRequestsPerSec', unit: '次/s'},
            {label: '消费请求频率', value: 'FetchConsumerRequestsPerSec', alias: 'FetchConsumerRequestsPerSec', unit: '次/s'},
            {label: '副本同步请求频率', value: 'FetchFollowerRequestsPerSec', alias: 'FetchFollowerRequestsPerSec', unit: '次/s'},
            {label: '失败写入请求频率', value: 'ProduceRequestsErrorsPerSec', alias: 'ProduceRequestsErrorsPerSec', unit: '次/s'},
            {label: '失败读取请求频率', value: 'FetchConsumerRequestsErrorsPerSec', alias: 'FetchConsumerRequestsErrorsPerSec', unit: '次/s'},
            {label: '失败副本同步请求频率', value: 'FetchFollowerRequestsErrorsPerSec', alias: 'FetchFollowerRequestsErrorsPerSec', unit: '次/s'},
            {label: '写入消息格式转换率', value: 'BrokerProduceMessageConversionsPerSec', alias: 'BrokerProduceMessageConversionsPerSec', unit: '条/s'},
            {label: '读取消息格式转换率', value: 'BrokerFetchMessageConversionsPerSec', alias: 'BrokerFetchMessageConversionsPerSec', unit: '条/s'},
            {label: '活跃 Controller 节点个数', value: 'ActiveControllerCount', alias: 'ActiveControllerCount', unit: '个'},
            {label: '主题总数', value: 'GlobalTopicCount', alias: 'GlobalTopicCount', unit: '个'}
        ],
        // 分区
        partition: [
            {label: 'Leader分区数', value: 'LeaderCount', alias: 'LeaderCount', checked: true, unit: '个'},
            {label: '分区数', value: 'PartitionCount', alias: 'PartitionCount', checked: true, unit: '个'},
            {label: 'Leader选举频率', value: 'LeaderElectionRateAndTimeMs', alias: 'LeaderElectionRateAndTimeMs', checked: true, unit: '次/s'},
            {label: '未同步Leader选举频率', value: 'UncleanLeaderElectionsPerSec', alias: 'UncleanLeaderElectionsPerSec', checked: true, unit: '次/s'},
            {label: '同步副本少于最小 ISR 的分区数', value: 'UnderMinIsrPartitionCount', alias: 'UnderMinIsrPartitionCount', unit: '个'},
            {label: '非最优 Leader 分区数', value: 'PreferredReplicaImbalanceCount', alias: 'PreferredReplicaImbalanceCount', unit: '个'},
            {label: '存在落后副本的分区数', value: 'UnderReplicatedPartitions', alias: 'UnderReplicatedPartitions', checked: true, unit: '个'},
            {label: '下线分区总数', value: 'OfflinePartitionsCount', alias: 'OfflinePartitionsCount', unit: '个'},
            {label: '副本落后消息条数', value: 'ReplicaMaxLag', alias: 'ReplicaMaxLag', checked: true, unit: '条'}
        ],
        // GC
        gc: [
            {label: 'Young GC 次数', value: 'YoungGCCount', alias: 'YoungGCCount', unit: '次'},
            {label: 'Young GC 耗时', value: 'YoungGCTotalTimeMs', alias: 'YoungGCTotalTimeMs', unit: 'ms'},
            {label: 'Old GC 次数', value: 'OldGCCount', alias: 'OldGCCount', unit: '次'},
            {label: 'Old GC 耗时', value: 'OldGCTotalTimeMs', alias: 'OldGCTotalTimeMs', unit: 'ms'},
            {label: 'Young GC 频率', value: 'YoungGCPerSec', alias: 'YoungGCPerSec', unit: '次/s'},
            {label: 'Young GC 单次耗时', value: 'YoungGCTimeMs', alias: 'YoungGCTimeMs', unit: 'ms'},
            {label: 'Old GC 频率', value: 'OldGCPerSec', alias: 'OldGCPerSec', unit: '次/s'},
            {label: 'Old GC 单次耗时', value: 'OldGCTimeMs', alias: 'OldGCTimeMs', unit: 'ms'}
        ],
        // ZK
        zk: [
            {label: 'ZK 请求延迟 P75', value: 'ZooKeeperRequestLatencyMsP75', alias: 'ZooKeeperRequestLatencyMsP75', unit: 'ms'},
            {label: 'ZK 请求延迟 P95', value: 'ZooKeeperRequestLatencyMsP95', alias: 'ZooKeeperRequestLatencyMsP95', unit: 'ms'},
            {label: 'ZK 请求延迟 P99', value: 'ZooKeeperRequestLatencyMsP99', alias: 'ZooKeeperRequestLatencyMsP99', unit: 'ms'},
            {label: 'ZK 连接会话状态', value: 'ZooKeeperSessionState', alias: 'ZooKeeperSessionState', unit: '枚举', tip: '枚举(1：连接中；3：已连接；5：连接关闭)'},
            {label: 'ZK 会话过期频率', value: 'ZooKeeperExpiresPerSec', alias: 'ZooKeeperExpiresPerSec', unit: '次/s'},
            {label: '断开 ZK 连接频率', value: 'ZooKeeperDisconnectsPerSec', alias: 'ZooKeeperDisconnectsPerSec', unit: '次/s'},
            {label: '建立 ZK 连接频率', value: 'ZooKeeperSyncConnectsPerSec', alias: 'ZooKeeperSyncConnectsPerSec', unit: '次/s'}
        ],
        // 其他
        other: [
            {label: '网络处理线程空闲率', value: 'NetworkProcessorAvgIdlePercent', alias: 'NetworkProcessorAvgIdlePercent', checked: true, unit: '%'},
            {label: '请求处理线程空闲率', value: 'RequestHandlerAvgIdlePercent', alias: 'RequestHandlerAvgIdlePercent', checked: true, unit: '%'},
            {label: 'ISR 收缩频率', value: 'IsrShrinksPerSec', alias: 'IsrShrinksPerSec', checked: true, unit: '次/s'},
            {label: 'ISR 扩张频率', value: 'IsrExpandsPerSec', alias: 'IsrExpandsPerSec', checked: true, unit: '次/s'},
            {label: 'Broker 请求队列大小', value: 'RequestQueueSize', alias: 'RequestQueueSize', unit: '个'},
            {label: 'Broker 响应队列大小', value: 'ResponseQueueSize', alias: 'ResponseQueueSize', unit: '个'},
            {label: 'Controller 事件队列大小', value: 'EventQueueSize', alias: 'EventQueueSize', unit: '个'},
            {label: '日志删除速率', value: 'BrokerDeleteSizeByThresholdPerSec', alias: 'BrokerDeleteSizeByThresholdPerSec', unit: 'Bytes/s'},
            {label: '日志文件删除速率', value: 'BrokerDeleteSegmentsByThresholdPerSec', alias: 'BrokerDeleteSegmentsByThresholdPerSec', unit: '个/s'},
            {label: '写入远程请求频率', value: 'BrokerRemoteCopyRequestsPerSec', alias: 'BrokerRemoteCopyRequestsPerSec', unit: '次/s'},
            {label: '读取远程请求频率', value: 'BrokerRemoteFetchRequestsPerSec', alias: 'BrokerRemoteFetchRequestsPerSec', unit: '次/s'},
            {label: '删除远程请求频率', value: 'BrokerRemoteDeleteRequestsPerSec', alias: 'BrokerRemoteDeleteRequestsPerSec', unit: '次/s'},
            {label: '失败写入远程请求频率', value: 'BrokerRemoteCopyErrorsPerSec', alias: 'BrokerRemoteCopyErrorsPerSec', unit: '次/s'},
            {label: '失败读取远程请求频率', value: 'BrokerRemoteFetchErrorsPerSec', alias: 'BrokerRemoteFetchErrorsPerSec', unit: '次/s'},
            {label: '失败删除远程请求频率', value: 'BrokerRemoteDeleteErrorsPerSec', alias: 'BrokerRemoteDeleteErrorsPerSec', unit: '次/s'},
            {label: '远程日志大小', value: 'BrokerRemoteLogSizeBytes', alias: 'BrokerRemoteLogSizeBytes', unit: 'Bytes'},
            {label: '远程元数据数量', value: 'BrokerRemoteLogMetadataCount', alias: 'BrokerRemoteLogMetadataCount', unit: '个'},
            {label: '远程日志读取线程空闲百分比', value: 'RemoteLogReaderAvgIdlePercent', alias: 'RemoteLogReaderAvgIdlePercent', unit: '%'},
            {label: '远程日志任务线程空闲百分比', value: 'RemoteLogManagerTasksAvgIdlePercent', alias: 'RemoteLogManagerTasksAvgIdlePercent', unit: '%'}
        ],
    },

    // 主机监控
    hostIndicators: {
        // cpu
        cpu: [
            {label: 'CPU 使用率', value: 'CpuUsedPercent', alias: 'CpuUsedPercent', checked: true, unit: '%'},
            {label: 'CPU 空闲率', value: 'CpuIdlePercent', alias: 'CpuIdlePercent', unit: '%'},
            {label: 'CPU 等待IO时长占比', value: 'CpuIOWaitPercent', alias: 'CpuIOWaitPercent', unit: '%'},
            {label: 'CPU 用户使用时长占比', value: 'CpuUserPercent', alias: 'CpuUserPercent', unit: '%'},
            {label: 'CPU 系统使用时长占比', value: 'CpuSystemPercent', alias: 'CpuSystemPercent', unit: '%'},
            {label: 'CPU 最近一分钟负载', value: 'CpuLoadAvg1', alias: 'CpuLoadAvg1', unit: '个'},
            {label: 'CPU 最近五分钟负载', value: 'CpuLoadAvg5', alias: 'CpuLoadAvg5', unit: '个'},
            {label: 'CPU 最近十五分钟负载', value: 'CpuLoadAvg15', alias: 'CpuLoadAvg15', unit: '个'},
        ],
        // 内存
        ram: [
            {label: '内存总量', value: 'MemTotalBytes', alias: 'MemTotalBytes', unit: 'Bytes'},
            {label: '内存可用量', value: 'MemFreeBytes', alias: 'MemFreeBytes', unit: 'Bytes'},
            {label: '内存缓存大小', value: 'MemCacheBytes', alias: 'MemCacheBytes', unit: 'Bytes'},
            {label: '内存缓冲大小', value: 'MemBufferBytes', alias: 'MemBufferBytes', unit: 'Bytes'},
            {label: '内存使用量', value: 'MemUsedBytes', alias: 'MemUsedBytes', unit: 'Bytes'},
            {label: '内存使用率', value: 'MemUsedPercent', alias: 'MemUsedPercent', checked: true, unit: '%'},
            {label: '内存可用率', value: 'MemFreePercent', alias: 'MemFreePercent', unit: '%'},
        ],
        // 网络
        network: [
            {label: '公网流入量', value: 'WebInBytes', alias: 'WebInBytes', checked: true, unit: 'Bytes/min'},
            {label: '公网流出量', value: 'WebOutBytes', alias: 'WebOutBytes', checked: true, unit: 'Bytes/min'},
            {label: '公网流入带宽', value: 'WebInBitsPerSecond', alias: 'WebInBitsPerSecond', unit: 'bps'},
            {label: '公网流出带宽', value: 'WebOutBitsPerSecond', alias: 'WebOutBitsPerSecond', unit: 'bps'},
            {label: '公网流入包速率', value: 'WebInPPS', alias: 'WebInPPS', unit: 'pps'},
            {label: '公网流出包速率', value: 'WebOutPPS', alias: 'WebOutPPS', unit: 'pps'},
            {label: '网卡流入量', value: 'NetReceiveBytesPerSec', alias: 'NetReceiveBytesPerSec', checked: true, unit: 'Bytes/s'},
            {label: '网卡流出量', value: 'NetTransmitBytesPerSec', alias: 'NetTransmitBytesPerSec', checked: true, unit: 'Bytes/s'},
            {label: '网卡接收数据包速率', value: 'NetReceivePacketsPerSec', alias: 'NetReceivePacketsPerSec', unit: '个/s'},
            {label: '网卡发送数据包速率', value: 'NetTransmitPacketsPerSec', alias: 'NetTransmitPacketsPerSec', unit: '个/s'},
            {label: '网卡接收错误数据包速率', value: 'NetReceiveErrorsPerSec', alias: 'NetReceiveErrorsPerSec', unit: '个/s'},
            {label: '网卡发送错误数据包速率', value: 'NetTransmitErrorsPerSec', alias: 'NetTransmitErrorsPerSec', unit: '个/s'},
            {label: '网卡丢弃的接收数据包速率', value: 'NetReceiveDroppedPerSec', alias: 'NetReceiveDroppedPerSec', unit: '个/s'},
            {label: '网卡丢弃的发送数据包速率', value: 'NetTransmitDroppedPerSec', alias: 'NetTransmitDroppedPerSec', unit: '个/s'},
            {label: '网卡流入带宽使用率', value: 'NetReceiveBandwidthUsedPercent', alias: 'NetReceiveBandwidthUsedPercent', unit: '%'},
            {label: '网卡流出带宽使用率', value: 'NetTransmitBandwidthUsedPercent', alias: 'NetTransmitBandwidthUsedPercent', unit: '%'},
        ],
        // 磁盘
        disk: [
            {label: '磁盘 IO 使用率', value: 'DiskIOUsedPercent', alias: 'DiskIOUsedPercent', unit: '%'},
            {label: '磁盘读流量', value: 'DiskReadBytesPerSec', alias: 'DiskReadBytesPerSec', checked: true, unit: 'Byte/s'},
            {label: '磁盘写流量', value: 'DiskWriteBytesPerSec', alias: 'DiskWriteBytesPerSec', checked: true, unit: 'Byte/s'},
            {label: '磁盘读 IOPS', value: 'DiskReadIOPerSec', alias: 'DiskReadIOPerSec', checked: true, unit: '次/s'},
            {label: '磁盘写 IOPS', value: 'DiskWriteIOPerSec', alias: 'DiskWriteIOPerSec', checked: true, unit: '次/s'},
            {label: '磁盘读 IO 操作时长', value: 'DiskReadAwaitMs', alias: 'DiskReadAwaitMs', unit: 'ms'},
            {label: '磁盘写 IO 操作时长', value: 'DiskWriteAwaitMs', alias: 'DiskWriteAwaitMs', unit: 'ms'},
            {label: '磁盘空间使用率', value: 'DiskUsedPercent', alias: 'DiskUsedPercent', checked: true, unit: '%'},
            {label: '磁盘总空间大小', value: 'DiskTotalBytes', alias: 'DiskTotalBytes', unit: 'Bytes'},
            {label: '磁盘可用空间大小', value: 'DiskFreeBytes', alias: 'DiskFreeBytes', unit: 'Bytes'},
            {label: '磁盘空间使用量', value: 'DiskUsedBytes', alias: 'DiskUsedBytes', checked: true, unit: 'Bytes'},
            {label: '最大磁盘空间使用率', value: 'NodeMaxDiskUsedPercent', alias: 'NodeMaxDiskUsedPercent', unit: '%'},
            {label: '最大数据盘空间使用率', value: 'NodeMaxDataDiskUsedPercent', alias: 'NodeMaxDataDiskUsedPercent', unit: '%'},
        ],
        // 其他
        other: [
            {label: 'SWAP 总量', value: 'SwapTotalBytes', alias: 'SwapTotalBytes', unit: 'Bytes'},
            {label: 'SWAP 可用量', value: 'SwapFreeBytes', alias: 'SwapFreeBytes', unit: 'Bytes'},
            {label: 'SWAP 使用量', value: 'SwapUsedBytes', alias: 'SwapUsedBytes', unit: 'Bytes'},
            {label: 'FD 上限', value: 'FdLimitation', alias: 'FdLimitation', unit: '个'},
            {label: 'FD 使用量', value: 'FdUsed', alias: 'FdUsed', checked: true, unit: '个'},
            {label: 'FD 使用率', value: 'FdUsedPercent', alias: 'FdUsedPercent', unit: '%'},
            {label: 'TCP 已建立连接数', value: 'NetTcpCurrentEstab', alias: 'NetTcpCurrentEstab', checked: true, unit: '个'},
            {label: 'TCP 接收包个数', value: 'NetTcpInSegs', alias: 'NetTcpInSegs', unit: '个'},
            {label: 'TCP 发送包个数', value: 'NetTcpOutSegs', alias: 'NetTcpOutSegs', unit: '个'},
            {label: 'TCP 丢包个数', value: 'NetTcpInErrs', alias: 'NetTcpInErrs', unit: '个'},
            {label: 'TCP 重传个数', value: 'NetTcpRetransSegs', alias: 'NetTcpRetransSegs', unit: '个'},
            {label: 'TCP 关闭连接数', value: 'NetTcpCurrentClosed', alias: 'NetTcpCurrentClosed', unit: '个'},
            {label: 'Broker 进程死亡个数', value: 'BrokerDeadCount', alias: 'BrokerDeadCount', unit: '个'},
            {label: '系统启动时间', value: 'SystemStartTime', alias: 'SystemStartTime', unit: 'ms'},
        ]
    }
};

let myStore = new Store({

    // @ts-ignore
    initData: {
        // 消费组监控指标
        originalclusterIndicators: _.cloneDeep(originalclusterIndicators),
        clusterIndicators: _.cloneDeep(originalclusterIndicators),
        originalconsumerIndicators: _.cloneDeep(originalconsumerIndicators),
        consumerIndicators: _.cloneDeep(originalconsumerIndicators),
        originalbrokerIndicators: _.cloneDeep(originalbrokerIndicators),
        brokerIndicators: _.cloneDeep(originalbrokerIndicators),
        originaltopicIndicators: _.cloneDeep(originaltopicIndicators),
        topicIndicators: _.cloneDeep(originaltopicIndicators),
    },

    actions: {
        changeClusterIndicators({monitorType, type, index, target}) {
            return builder().set(`clusterIndicators.${type}[${index}].checked`, target.value);
        },
        changeTopicIndicators({monitorType, type, index, target}) {
            return builder().set(`topicIndicators.${monitorType}Indicators.${type}[${index}].checked`, target.value);
        },
        changeConsumerGroupIndicators({monitorType, type, index, target}) {
            return builder().set(`consumerIndicators.${monitorType}Indicators.${type}[${index}].checked`, target.value);
        },
        changeBrokerIndicators({monitorType, type, index, target}) {
            return builder().set(`brokerIndicators.${monitorType}Indicators.${type}[${index}].checked`, target.value);
        },
        updateClusterIndicators(monitorType, {getState}) {
            return builder().set(`originalclusterIndicators`, myStore.getState('clusterIndicators'));
        },
        updateTopicIndicators(monitorType, {getState}) {
            return builder().set(`originaltopicIndicators.${monitorType}Indicators`, myStore.getState(`topicIndicators.${monitorType}Indicators`));
        },
        updateConsumerGroupIndicators(monitorType, {getState}) {
            return builder().set(`originalconsumerIndicators.${monitorType}Indicators`, myStore.getState(`consumerIndicators.${monitorType}Indicators`));
        },
        updateBrokerIndicators(monitorType, {getState}) {
            return builder().set(`originalbrokerIndicators.${monitorType}Indicators`, myStore.getState(`brokerIndicators.${monitorType}Indicators`));
        },
        resetClusterIndicators(monitorType, {getState}) {
            return builder().set(`clusterIndicators`, myStore.getState('originalclusterIndicators'));
        },
        resetTopicIndicators(monitorType, {getState}) {
            return builder().set(`topicIndicators.${monitorType}Indicators`, myStore.getState(`originaltopicIndicators.${monitorType}Indicators`));
        },
        resetConsumerGroupIndicators(monitorType, {getState}) {
            return builder().set(`consumerIndicators.${monitorType}Indicators`, myStore.getState(`originalconsumerIndicators.${monitorType}Indicators`));
        },
        resetBrokerIndicators(monitorType, {getState}) {
            return builder().set(`brokerIndicators.${monitorType}Indicators`, myStore.getState(`originalbrokerIndicators.${monitorType}Indicators`));
        },
    }
});

export const connectMyStore = connect.createConnector(myStore);

export default myStore;
