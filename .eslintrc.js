module.exports = {
    extends: [
        '@ecomfe/eslint-config',
        '@ecomfe/eslint-config/typescript' // 使用TS基本规则校验
        // '@ecomfe/eslint-config/typescript/strict' // 或者选择严格模式
    ],
    rules: {
        'comma-dangle': 'off', // 关闭了代码拖尾逗号的问题校验
        quotes: ['error', 'single'],
        'object-curly-spacing': ['error', 'never'],
        '@typescript-eslint/no-floating-promises': 'off',
        '@typescript-eslint/consistent-type-definitions': 'warn',
        '@typescript-eslint/member-ordering': 'off',
        'no-else-return': 'off'
    },
    parser: '@typescript-eslint/parser'
    // parserOptions: {
    //     // 兼容 .js 文件使用装饰器的情况！！！
    //     babelOptions: {
    //         plugins: [['@babel/plugin-proposal-decorators', {legacy: true}]]
    //     }
    // }
};
