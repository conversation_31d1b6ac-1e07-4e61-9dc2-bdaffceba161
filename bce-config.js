/**
 * @file 产品启动配置
 * <AUTHOR>
 */
const path = require('path');
const CopyWebpackPlugin =  require('copy-webpack-plugin');

module.exports = {
    // ------模块相关配置 start------
    service: 'KAFKA', // 在P3M平台注册的service type，默认name全大写
    pathname: 'kafka',
    runtime: '@baiducloud/runtime',
    flags: ['KAFKA'],

    // resourceBasePath: 'http://console.dev-paas.abcstackint.com',

    // ------模块相关配置 end------
    // mock配置
    mockup: {
        caching: true,
        root: '.mockup' // 默认 /.mockup
    },

    debug: {
        // '@baiducloud/runtime': 'http://localhost:8990/runtime.js'
    },

    // 代理地址
    // proxyTarget: 'https://qasandbox.bcetest.baidu.com', // 沙盒
    // proxyTarget: 'https://console.bce.baidu.com', // 线上

    // public: false, // 单产品私有化，使用非公共资源

    // 需要排除的依赖，模块会帮助配置esl，sdk则需要依赖模块的配置
    dependencies: {
        '@baiducloud/bcm-sdk': '@baiducloud/fe-bcm-sdk/*********/bcm-sdk.js',
        '@baiducloud/bcm-sdk-san': '@baiducloud/fe-bcm-sdk/*********/bcm-sdk-san.js',
        '@baiducloud/tag-sdk': '@baiducloud/fe-tag-sdk/*******/tag-sdk.js',
        '@baiducloud/tag-sdk/san': '@baiducloud/fe-tag-sdk/*******/tag-sdk-san.js',
        '@baidu/sui': '@baiducloud/sui/1.0.270.1/sui.js',
        '@baidu/sui-biz': '@baiducloud/sui-biz/********/sui-biz.js'
    },

    eslConfigAddMap: {
        '@baidu/sui': '@baidu/sui@1.0.270.1'
    },

    // 自定义webpack，可选，默认一个入口
    webpack: {
        devtool: 'none',
        resolve: {
            alias: {
                '@': path.resolve('./src'),
                '@/common/*': path.resolve('./src/common/*'),
                '@/components/*': path.resolve('./src/components/*'),
            }
        },

        // 需要排除的依赖，模块会帮助配置esl，sdk则需要依赖模块的配置
        externals: [
            'echarts',
            '@baiducloud/i18n',
            '@baiducloud/runtime',
            '@baiducloud/flowchart',
            '@baiducloud/bce-ui/san',
            '@baidu/bce-track',
            '@baiducloud/httpclient',
            '@baidu/sui',
            '@baidu/sui-biz'
        ],
        plugins: [
            new CopyWebpackPlugin({
                patterns: [
                    {
                        from: path.resolve(__dirname, 'node_modules/@baidu/san-monaco-editor/dist-npm'),
                        to: path.resolve(__dirname, 'dist')
                    }
                ]
            })
        ],
        optimization: {}
    },
    babelOptions: {
        plugins: [
            '@babel/plugin-proposal-nullish-coalescing-operator',
            '@babel/plugin-proposal-optional-chaining',
            ['@babel/plugin-proposal-private-methods', {loose: true}]
        ]
    }
};
